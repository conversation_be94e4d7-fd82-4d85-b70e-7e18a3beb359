// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "GAS/AURACRONAttributeSet.h"

#ifdef AURACRON_AURACRONAttributeSet_generated_h
#error "AURACRONAttributeSet.generated.h already included, missing '#pragma once' in AURACRONAttributeSet.h"
#endif
#define AURACRON_AURACRONAttributeSet_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"
#include "Net/Core/PushModel/PushModelMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

struct FGameplayAttributeData;

// ********** Begin Class UAURACRONAttributeSet ****************************************************
#define FID_AURACRON_Source_AURACRON_Public_GAS_AURACRONAttributeSet_h_27_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_MaxSigilSlots); \
	DECLARE_FUNCTION(execOnRep_SigilEfficiency); \
	DECLARE_FUNCTION(execOnRep_CooldownReduction); \
	DECLARE_FUNCTION(execOnRep_ManaRegeneration); \
	DECLARE_FUNCTION(execOnRep_HealthRegeneration); \
	DECLARE_FUNCTION(execOnRep_MovementSpeed); \
	DECLARE_FUNCTION(execOnRep_CriticalChance); \
	DECLARE_FUNCTION(execOnRep_AttackSpeed); \
	DECLARE_FUNCTION(execOnRep_MagicResistance); \
	DECLARE_FUNCTION(execOnRep_Armor); \
	DECLARE_FUNCTION(execOnRep_AbilityPower); \
	DECLARE_FUNCTION(execOnRep_AttackDamage); \
	DECLARE_FUNCTION(execOnRep_MaxMana); \
	DECLARE_FUNCTION(execOnRep_Mana); \
	DECLARE_FUNCTION(execOnRep_MaxHealth); \
	DECLARE_FUNCTION(execOnRep_Health);


AURACRON_API UClass* Z_Construct_UClass_UAURACRONAttributeSet_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_GAS_AURACRONAttributeSet_h_27_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAURACRONAttributeSet(); \
	friend struct Z_Construct_UClass_UAURACRONAttributeSet_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UAURACRONAttributeSet_NoRegister(); \
public: \
	DECLARE_CLASS2(UAURACRONAttributeSet, UAttributeSet, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UAURACRONAttributeSet_NoRegister) \
	DECLARE_SERIALIZER(UAURACRONAttributeSet) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		Health=NETFIELD_REP_START, \
		MaxHealth, \
		Mana, \
		MaxMana, \
		AttackDamage, \
		AbilityPower, \
		Armor, \
		MagicResistance, \
		AttackSpeed, \
		CriticalChance, \
		MovementSpeed, \
		HealthRegeneration, \
		ManaRegeneration, \
		CooldownReduction, \
		SigilEfficiency, \
		MaxSigilSlots, \
		NETFIELD_REP_END=MaxSigilSlots	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API) \
private: \
	REPLICATED_BASE_CLASS(UAURACRONAttributeSet) \
public:


#define FID_AURACRON_Source_AURACRON_Public_GAS_AURACRONAttributeSet_h_27_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAURACRONAttributeSet(UAURACRONAttributeSet&&) = delete; \
	UAURACRONAttributeSet(const UAURACRONAttributeSet&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAURACRONAttributeSet); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAURACRONAttributeSet); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAURACRONAttributeSet) \
	NO_API virtual ~UAURACRONAttributeSet();


#define FID_AURACRON_Source_AURACRON_Public_GAS_AURACRONAttributeSet_h_24_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_GAS_AURACRONAttributeSet_h_27_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_GAS_AURACRONAttributeSet_h_27_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_GAS_AURACRONAttributeSet_h_27_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_GAS_AURACRONAttributeSet_h_27_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAURACRONAttributeSet;

// ********** End Class UAURACRONAttributeSet ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_GAS_AURACRONAttributeSet_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
