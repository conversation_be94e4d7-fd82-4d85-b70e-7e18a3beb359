// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AURACRONEnums.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONEnums() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONBuffType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnergyType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONNetworkState();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONSigilType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONTrailType();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAURACRONMapPhase *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONMapPhase;
static UEnum* EAURACRONMapPhase_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONMapPhase.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONMapPhase.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONMapPhase"));
	}
	return Z_Registration_Info_UEnum_EAURACRONMapPhase.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONMapPhase>()
{
	return EAURACRONMapPhase_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Awakening.DisplayName", "Despertar" },
		{ "Awakening.Name", "EAURACRONMapPhase::Awakening" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Fases do mapa durante a partida - Sistema de Altern\xc3\xa2ncia de Mapas\n * Baseado na documenta\xc3\xa7\xc3\xa3o: FASE 1: DESPERTAR (0-15 min), FASE 2: CONVERG\xc3\x8aNCIA (15-25 min), etc.\n */" },
#endif
		{ "Convergence.Comment", "// 15-20 minutos (fase intermedi\xc3\xa1ria)\n" },
		{ "Convergence.DisplayName", "Converg\xc3\xaancia" },
		{ "Convergence.Name", "EAURACRONMapPhase::Convergence" },
		{ "Convergence.ToolTip", "15-20 minutos (fase intermedi\xc3\xa1ria)" },
		{ "Expansion.Comment", "// 0-15 minutos\n" },
		{ "Expansion.DisplayName", "Expans\xc3\xa3o" },
		{ "Expansion.Name", "EAURACRONMapPhase::Expansion" },
		{ "Expansion.ToolTip", "0-15 minutos" },
		{ "Intensification.Comment", "// 15-25 minutos\n" },
		{ "Intensification.DisplayName", "Intensifica\xc3\xa7\xc3\xa3o" },
		{ "Intensification.Name", "EAURACRONMapPhase::Intensification" },
		{ "Intensification.ToolTip", "15-25 minutos" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "Resolution.Comment", "// 25-35 minutos\n" },
		{ "Resolution.DisplayName", "Resolu\xc3\xa7\xc3\xa3o" },
		{ "Resolution.Name", "EAURACRONMapPhase::Resolution" },
		{ "Resolution.ToolTip", "25-35 minutos" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fases do mapa durante a partida - Sistema de Altern\xc3\xa2ncia de Mapas\nBaseado na documenta\xc3\xa7\xc3\xa3o: FASE 1: DESPERTAR (0-15 min), FASE 2: CONVERG\xc3\x8aNCIA (15-25 min), etc." },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONMapPhase::Awakening", (int64)EAURACRONMapPhase::Awakening },
		{ "EAURACRONMapPhase::Expansion", (int64)EAURACRONMapPhase::Expansion },
		{ "EAURACRONMapPhase::Convergence", (int64)EAURACRONMapPhase::Convergence },
		{ "EAURACRONMapPhase::Intensification", (int64)EAURACRONMapPhase::Intensification },
		{ "EAURACRONMapPhase::Resolution", (int64)EAURACRONMapPhase::Resolution },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONMapPhase",
	"EAURACRONMapPhase",
	Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase()
{
	if (!Z_Registration_Info_UEnum_EAURACRONMapPhase.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONMapPhase.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONMapPhase.InnerSingleton;
}
// ********** End Enum EAURACRONMapPhase ***********************************************************

// ********** Begin Enum EAURACRONEnvironmentType **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONEnvironmentType;
static UEnum* EAURACRONEnvironmentType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONEnvironmentType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONEnvironmentType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONEnvironmentType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONEnvironmentType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONEnvironmentType>()
{
	return EAURACRONEnvironmentType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de ambiente no sistema de altern\xc3\xa2ncia de mapas\n * Baseado na documenta\xc3\xa7\xc3\xa3o: Plan\xc3\xad""cie Radiante, Firmamento Zephyr, Reino Purgat\xc3\xb3rio\n */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "PurgatoryRealm.Comment", "// Plataformas celestiais\n" },
		{ "PurgatoryRealm.DisplayName", "Reino Purgat\xc3\xb3rio" },
		{ "PurgatoryRealm.Name", "EAURACRONEnvironmentType::PurgatoryRealm" },
		{ "PurgatoryRealm.ToolTip", "Plataformas celestiais" },
		{ "RadiantPlains.DisplayName", "Plan\xc3\xad""cie Radiante" },
		{ "RadiantPlains.Name", "EAURACRONEnvironmentType::RadiantPlains" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de ambiente no sistema de altern\xc3\xa2ncia de mapas\nBaseado na documenta\xc3\xa7\xc3\xa3o: Plan\xc3\xad""cie Radiante, Firmamento Zephyr, Reino Purgat\xc3\xb3rio" },
#endif
		{ "ZephyrFirmament.Comment", "// Mapa base terrestre\n" },
		{ "ZephyrFirmament.DisplayName", "Firmamento Zephyr" },
		{ "ZephyrFirmament.Name", "EAURACRONEnvironmentType::ZephyrFirmament" },
		{ "ZephyrFirmament.ToolTip", "Mapa base terrestre" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONEnvironmentType::RadiantPlains", (int64)EAURACRONEnvironmentType::RadiantPlains },
		{ "EAURACRONEnvironmentType::ZephyrFirmament", (int64)EAURACRONEnvironmentType::ZephyrFirmament },
		{ "EAURACRONEnvironmentType::PurgatoryRealm", (int64)EAURACRONEnvironmentType::PurgatoryRealm },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONEnvironmentType",
	"EAURACRONEnvironmentType",
	Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONEnvironmentType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONEnvironmentType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONEnvironmentType.InnerSingleton;
}
// ********** End Enum EAURACRONEnvironmentType ****************************************************

// ********** Begin Enum EAURACRONObjectiveType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONObjectiveType;
static UEnum* EAURACRONObjectiveType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONObjectiveType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONObjectiveType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONObjectiveType>()
{
	return EAURACRONObjectiveType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de objetivos procedurais\n * Sistema de gera\xc3\xa7\xc3\xa3o din\xc3\xa2mica baseado no estado da partida\n */" },
#endif
		{ "EnvironmentAnchor.Comment", "// Permite rewind de 10 segundos\n" },
		{ "EnvironmentAnchor.DisplayName", "\xc3\x82ncora de Ambiente" },
		{ "EnvironmentAnchor.Name", "EAURACRONObjectiveType::EnvironmentAnchor" },
		{ "EnvironmentAnchor.ToolTip", "Permite rewind de 10 segundos" },
		{ "FragmentAuracron.Comment", "// Valor padr\xc3\xa3o/inv\xc3\xa1lido\n" },
		{ "FragmentAuracron.DisplayName", "Fragmento Auracron" },
		{ "FragmentAuracron.Name", "EAURACRONObjectiveType::FragmentAuracron" },
		{ "FragmentAuracron.ToolTip", "Valor padr\xc3\xa3o/inv\xc3\xa1lido" },
		{ "FusionCatalyst.Comment", "// Controla qual mapa est\xc3\xa1 ativo\n" },
		{ "FusionCatalyst.DisplayName", "Catalisador de Fus\xc3\xa3o" },
		{ "FusionCatalyst.Name", "EAURACRONObjectiveType::FusionCatalyst" },
		{ "FusionCatalyst.ToolTip", "Controla qual mapa est\xc3\xa1 ativo" },
		{ "MapAnchor.Comment", "// Controla ambiente ativo\n" },
		{ "MapAnchor.DisplayName", "\xc3\x82ncora de Mapa" },
		{ "MapAnchor.Name", "EAURACRONObjectiveType::MapAnchor" },
		{ "MapAnchor.ToolTip", "Controla ambiente ativo" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "NexusFragment.Comment", "// Mini-objetivos que constroem buff maior\n" },
		{ "NexusFragment.DisplayName", "Fragmento de Nexus" },
		{ "NexusFragment.Name", "EAURACRONObjectiveType::NexusFragment" },
		{ "NexusFragment.ToolTip", "Mini-objetivos que constroem buff maior" },
		{ "None.DisplayName", "Nenhum" },
		{ "None.Name", "EAURACRONObjectiveType::None" },
		{ "PrismalGuardian.Comment", "// Ativa transi\xc3\xa7\xc3\xb5""es entre ambientes\n" },
		{ "PrismalGuardian.DisplayName", "Guardi\xc3\xa3o Prismal" },
		{ "PrismalGuardian.Name", "EAURACRONObjectiveType::PrismalGuardian" },
		{ "PrismalGuardian.ToolTip", "Ativa transi\xc3\xa7\xc3\xb5""es entre ambientes" },
		{ "PrismalNexus.Comment", "// Objetivo principal terrestre\n" },
		{ "PrismalNexus.DisplayName", "Nexus Prismal" },
		{ "PrismalNexus.Name", "EAURACRONObjectiveType::PrismalNexus" },
		{ "PrismalNexus.ToolTip", "Objetivo principal terrestre" },
		{ "SpectralGuardian.Comment", "// Objetivo principal celestial\n" },
		{ "SpectralGuardian.DisplayName", "Guardi\xc3\xa3o Espectral" },
		{ "SpectralGuardian.Name", "EAURACRONObjectiveType::SpectralGuardian" },
		{ "SpectralGuardian.ToolTip", "Objetivo principal celestial" },
		{ "StormCore.Comment", "// Nexus de energia prism\xc3\xa1tica\n" },
		{ "StormCore.DisplayName", "N\xc3\xba""cleo de Tempestade" },
		{ "StormCore.Name", "EAURACRONObjectiveType::StormCore" },
		{ "StormCore.ToolTip", "Nexus de energia prism\xc3\xa1tica" },
		{ "TemporalRift.Comment", "// Fragmentos de nexus para construir buffs\n" },
		{ "TemporalRift.DisplayName", "Fenda Temporal" },
		{ "TemporalRift.Name", "EAURACRONObjectiveType::TemporalRift" },
		{ "TemporalRift.ToolTip", "Fragmentos de nexus para construir buffs" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de objetivos procedurais\nSistema de gera\xc3\xa7\xc3\xa3o din\xc3\xa2mica baseado no estado da partida" },
#endif
		{ "TransitionPortal.Comment", "// Reduz cooldown de S\xc3\xadgilos\n" },
		{ "TransitionPortal.DisplayName", "Portal de Transi\xc3\xa7\xc3\xa3o" },
		{ "TransitionPortal.Name", "EAURACRONObjectiveType::TransitionPortal" },
		{ "TransitionPortal.ToolTip", "Reduz cooldown de S\xc3\xadgilos" },
		{ "UmbraticLeviathan.Comment", "// Objetivo principal espectral\n" },
		{ "UmbraticLeviathan.DisplayName", "Leviat\xc3\xa3 Umbr\xc3\xa1tico" },
		{ "UmbraticLeviathan.Name", "EAURACRONObjectiveType::UmbraticLeviathan" },
		{ "UmbraticLeviathan.ToolTip", "Objetivo principal espectral" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONObjectiveType::None", (int64)EAURACRONObjectiveType::None },
		{ "EAURACRONObjectiveType::FragmentAuracron", (int64)EAURACRONObjectiveType::FragmentAuracron },
		{ "EAURACRONObjectiveType::NexusFragment", (int64)EAURACRONObjectiveType::NexusFragment },
		{ "EAURACRONObjectiveType::TemporalRift", (int64)EAURACRONObjectiveType::TemporalRift },
		{ "EAURACRONObjectiveType::EnvironmentAnchor", (int64)EAURACRONObjectiveType::EnvironmentAnchor },
		{ "EAURACRONObjectiveType::MapAnchor", (int64)EAURACRONObjectiveType::MapAnchor },
		{ "EAURACRONObjectiveType::FusionCatalyst", (int64)EAURACRONObjectiveType::FusionCatalyst },
		{ "EAURACRONObjectiveType::TransitionPortal", (int64)EAURACRONObjectiveType::TransitionPortal },
		{ "EAURACRONObjectiveType::PrismalGuardian", (int64)EAURACRONObjectiveType::PrismalGuardian },
		{ "EAURACRONObjectiveType::PrismalNexus", (int64)EAURACRONObjectiveType::PrismalNexus },
		{ "EAURACRONObjectiveType::StormCore", (int64)EAURACRONObjectiveType::StormCore },
		{ "EAURACRONObjectiveType::SpectralGuardian", (int64)EAURACRONObjectiveType::SpectralGuardian },
		{ "EAURACRONObjectiveType::UmbraticLeviathan", (int64)EAURACRONObjectiveType::UmbraticLeviathan },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONObjectiveType",
	"EAURACRONObjectiveType",
	Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONObjectiveType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveType.InnerSingleton;
}
// ********** End Enum EAURACRONObjectiveType ******************************************************

// ********** Begin Enum EAURACRONBuffType *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONBuffType;
static UEnum* EAURACRONBuffType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONBuffType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONBuffType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONBuffType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONBuffType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONBuffType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONBuffType>()
{
	return EAURACRONBuffType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONBuffType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Armor.DisplayName", "Armadura" },
		{ "Armor.Name", "EAURACRONBuffType::Armor" },
		{ "AttackSpeed.DisplayName", "Velocidade de Ataque" },
		{ "AttackSpeed.Name", "EAURACRONBuffType::AttackSpeed" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de buff aplicados por objetivos\n * Sistema robusto de buffs para equipes e \xc3\xa1reas\n */" },
#endif
		{ "CooldownReduction.DisplayName", "Redu\xc3\xa7\xc3\xa3o de Recarga" },
		{ "CooldownReduction.Name", "EAURACRONBuffType::CooldownReduction" },
		{ "CriticalChance.DisplayName", "Chance Cr\xc3\xadtica" },
		{ "CriticalChance.Name", "EAURACRONBuffType::CriticalChance" },
		{ "DamageBoost.DisplayName", "Aumento de Dano" },
		{ "DamageBoost.Name", "EAURACRONBuffType::DamageBoost" },
		{ "DefenseBoost.DisplayName", "Aumento de Defesa" },
		{ "DefenseBoost.Name", "EAURACRONBuffType::DefenseBoost" },
		{ "HealthRegeneration.DisplayName", "Regenera\xc3\xa7\xc3\xa3o de Vida" },
		{ "HealthRegeneration.Name", "EAURACRONBuffType::HealthRegeneration" },
		{ "Lifesteal.DisplayName", "Roubo de Vida" },
		{ "Lifesteal.Name", "EAURACRONBuffType::Lifesteal" },
		{ "MagicResistance.DisplayName", "Resist\xc3\xaancia M\xc3\xa1gica" },
		{ "MagicResistance.Name", "EAURACRONBuffType::MagicResistance" },
		{ "ManaRegeneration.DisplayName", "Regenera\xc3\xa7\xc3\xa3o de Mana" },
		{ "ManaRegeneration.Name", "EAURACRONBuffType::ManaRegeneration" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "MovementSpeed.DisplayName", "Velocidade de Movimento" },
		{ "MovementSpeed.Name", "EAURACRONBuffType::MovementSpeed" },
		{ "SpellPower.DisplayName", "Poder M\xc3\xa1gico" },
		{ "SpellPower.Name", "EAURACRONBuffType::SpellPower" },
		{ "Tenacity.DisplayName", "Tenacidade" },
		{ "Tenacity.Name", "EAURACRONBuffType::Tenacity" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de buff aplicados por objetivos\nSistema robusto de buffs para equipes e \xc3\xa1reas" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONBuffType::MovementSpeed", (int64)EAURACRONBuffType::MovementSpeed },
		{ "EAURACRONBuffType::DamageBoost", (int64)EAURACRONBuffType::DamageBoost },
		{ "EAURACRONBuffType::DefenseBoost", (int64)EAURACRONBuffType::DefenseBoost },
		{ "EAURACRONBuffType::CooldownReduction", (int64)EAURACRONBuffType::CooldownReduction },
		{ "EAURACRONBuffType::HealthRegeneration", (int64)EAURACRONBuffType::HealthRegeneration },
		{ "EAURACRONBuffType::ManaRegeneration", (int64)EAURACRONBuffType::ManaRegeneration },
		{ "EAURACRONBuffType::CriticalChance", (int64)EAURACRONBuffType::CriticalChance },
		{ "EAURACRONBuffType::AttackSpeed", (int64)EAURACRONBuffType::AttackSpeed },
		{ "EAURACRONBuffType::SpellPower", (int64)EAURACRONBuffType::SpellPower },
		{ "EAURACRONBuffType::Armor", (int64)EAURACRONBuffType::Armor },
		{ "EAURACRONBuffType::MagicResistance", (int64)EAURACRONBuffType::MagicResistance },
		{ "EAURACRONBuffType::Lifesteal", (int64)EAURACRONBuffType::Lifesteal },
		{ "EAURACRONBuffType::Tenacity", (int64)EAURACRONBuffType::Tenacity },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONBuffType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONBuffType",
	"EAURACRONBuffType",
	Z_Construct_UEnum_AURACRON_EAURACRONBuffType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONBuffType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONBuffType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONBuffType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONBuffType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONBuffType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONBuffType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONBuffType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONBuffType.InnerSingleton;
}
// ********** End Enum EAURACRONBuffType ***********************************************************

// ********** Begin Enum EAURACRONTemporalEffectType ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONTemporalEffectType;
static UEnum* EAURACRONTemporalEffectType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONTemporalEffectType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONTemporalEffectType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONTemporalEffectType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONTemporalEffectType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONTemporalEffectType>()
{
	return EAURACRONTemporalEffectType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Accelerate.Comment", "// Reduz velocidade temporal\n" },
		{ "Accelerate.DisplayName", "Acelera\xc3\xa7\xc3\xa3o" },
		{ "Accelerate.Name", "EAURACRONTemporalEffectType::Accelerate" },
		{ "Accelerate.ToolTip", "Reduz velocidade temporal" },
		{ "BlueprintType", "true" },
		{ "ChronoShield.Comment", "// Desacelera inimigos\n" },
		{ "ChronoShield.DisplayName", "Escudo Temporal" },
		{ "ChronoShield.Name", "EAURACRONTemporalEffectType::ChronoShield" },
		{ "ChronoShield.ToolTip", "Desacelera inimigos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de efeitos temporais\n * Sistema de manipula\xc3\xa7\xc3\xa3o temporal para Fendas Temporais\n */" },
#endif
		{ "Freeze.Comment", "// Aumenta velocidade temporal\n" },
		{ "Freeze.DisplayName", "Congelamento" },
		{ "Freeze.Name", "EAURACRONTemporalEffectType::Freeze" },
		{ "Freeze.ToolTip", "Aumenta velocidade temporal" },
		{ "Loop.Comment", "// Congela no tempo\n" },
		{ "Loop.DisplayName", "Loop" },
		{ "Loop.Name", "EAURACRONTemporalEffectType::Loop" },
		{ "Loop.ToolTip", "Congela no tempo" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "None.DisplayName", "Nenhum" },
		{ "None.Name", "EAURACRONTemporalEffectType::None" },
		{ "Rewind.Comment", "// Sem efeito temporal\n" },
		{ "Rewind.DisplayName", "Retrocesso" },
		{ "Rewind.Name", "EAURACRONTemporalEffectType::Rewind" },
		{ "Rewind.ToolTip", "Sem efeito temporal" },
		{ "Slow.Comment", "// Volta posi\xc3\xa7\xc3\xa3o/vida 10 segundos\n" },
		{ "Slow.DisplayName", "Lentid\xc3\xa3o" },
		{ "Slow.Name", "EAURACRONTemporalEffectType::Slow" },
		{ "Slow.ToolTip", "Volta posi\xc3\xa7\xc3\xa3o/vida 10 segundos" },
		{ "TemporalEcho.Comment", "// Imunidade temporal breve\n" },
		{ "TemporalEcho.DisplayName", "Eco Temporal" },
		{ "TemporalEcho.Name", "EAURACRONTemporalEffectType::TemporalEcho" },
		{ "TemporalEcho.ToolTip", "Imunidade temporal breve" },
		{ "TimeAcceleration.Comment", "// Cria loop temporal\n" },
		{ "TimeAcceleration.DisplayName", "Acelera\xc3\xa7\xc3\xa3o Temporal" },
		{ "TimeAcceleration.Name", "EAURACRONTemporalEffectType::TimeAcceleration" },
		{ "TimeAcceleration.ToolTip", "Cria loop temporal" },
		{ "TimeDeceleration.Comment", "// Acelera cooldowns\n" },
		{ "TimeDeceleration.DisplayName", "Desacelera\xc3\xa7\xc3\xa3o Temporal" },
		{ "TimeDeceleration.Name", "EAURACRONTemporalEffectType::TimeDeceleration" },
		{ "TimeDeceleration.ToolTip", "Acelera cooldowns" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de efeitos temporais\nSistema de manipula\xc3\xa7\xc3\xa3o temporal para Fendas Temporais" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONTemporalEffectType::None", (int64)EAURACRONTemporalEffectType::None },
		{ "EAURACRONTemporalEffectType::Rewind", (int64)EAURACRONTemporalEffectType::Rewind },
		{ "EAURACRONTemporalEffectType::Slow", (int64)EAURACRONTemporalEffectType::Slow },
		{ "EAURACRONTemporalEffectType::Accelerate", (int64)EAURACRONTemporalEffectType::Accelerate },
		{ "EAURACRONTemporalEffectType::Freeze", (int64)EAURACRONTemporalEffectType::Freeze },
		{ "EAURACRONTemporalEffectType::Loop", (int64)EAURACRONTemporalEffectType::Loop },
		{ "EAURACRONTemporalEffectType::TimeAcceleration", (int64)EAURACRONTemporalEffectType::TimeAcceleration },
		{ "EAURACRONTemporalEffectType::TimeDeceleration", (int64)EAURACRONTemporalEffectType::TimeDeceleration },
		{ "EAURACRONTemporalEffectType::ChronoShield", (int64)EAURACRONTemporalEffectType::ChronoShield },
		{ "EAURACRONTemporalEffectType::TemporalEcho", (int64)EAURACRONTemporalEffectType::TemporalEcho },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONTemporalEffectType",
	"EAURACRONTemporalEffectType",
	Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONTemporalEffectType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONTemporalEffectType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONTemporalEffectType.InnerSingleton;
}
// ********** End Enum EAURACRONTemporalEffectType *************************************************

// ********** Begin Enum EAURACRONTrailType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONTrailType;
static UEnum* EAURACRONTrailType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONTrailType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONTrailType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONTrailType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONTrailType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONTrailType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONTrailType>()
{
	return EAURACRONTrailType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Axis.Comment", "// Energia dourada, boost velocidade\n" },
		{ "Axis.DisplayName", "Trilho Axis" },
		{ "Axis.Name", "EAURACRONTrailType::Axis" },
		{ "Axis.ToolTip", "Energia dourada, boost velocidade" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de trilhos din\xc3\xa2micos\n * Sistema de trilhos que mudam baseado em condi\xc3\xa7\xc3\xb5""es\n */" },
#endif
		{ "EtherealPath.Comment", "// Trilho do fluxo prismal\n" },
		{ "EtherealPath.DisplayName", "Caminho Et\xc3\xa9reo" },
		{ "EtherealPath.Name", "EAURACRONTrailType::EtherealPath" },
		{ "EtherealPath.ToolTip", "Trilho do fluxo prismal" },
		{ "Lunar.Comment", "// Canais neutros, transi\xc3\xa7\xc3\xa3o instant\xc3\xa2nea\n" },
		{ "Lunar.DisplayName", "Trilho Lunar" },
		{ "Lunar.Name", "EAURACRONTrailType::Lunar" },
		{ "Lunar.ToolTip", "Canais neutros, transi\xc3\xa7\xc3\xa3o instant\xc3\xa2nea" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "NexusConnection.Comment", "// Trilho et\xc3\xa9reo\n" },
		{ "NexusConnection.DisplayName", "Conex\xc3\xa3o Nexus" },
		{ "NexusConnection.Name", "EAURACRONTrailType::NexusConnection" },
		{ "NexusConnection.ToolTip", "Trilho et\xc3\xa9reo" },
		{ "None.DisplayName", "Nenhum" },
		{ "None.Name", "EAURACRONTrailType::None" },
		{ "PrismalFlow.Comment", "// Caminhos et\xc3\xa9reos, furtividade noturna\n" },
		{ "PrismalFlow.DisplayName", "Fluxo Prismal" },
		{ "PrismalFlow.Name", "EAURACRONTrailType::PrismalFlow" },
		{ "PrismalFlow.ToolTip", "Caminhos et\xc3\xa9reos, furtividade noturna" },
		{ "Solar.Comment", "// Valor padr\xc3\xa3o/inv\xc3\xa1lido\n" },
		{ "Solar.DisplayName", "Trilho Solar" },
		{ "Solar.Name", "EAURACRONTrailType::Solar" },
		{ "Solar.ToolTip", "Valor padr\xc3\xa3o/inv\xc3\xa1lido" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de trilhos din\xc3\xa2micos\nSistema de trilhos que mudam baseado em condi\xc3\xa7\xc3\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONTrailType::None", (int64)EAURACRONTrailType::None },
		{ "EAURACRONTrailType::Solar", (int64)EAURACRONTrailType::Solar },
		{ "EAURACRONTrailType::Axis", (int64)EAURACRONTrailType::Axis },
		{ "EAURACRONTrailType::Lunar", (int64)EAURACRONTrailType::Lunar },
		{ "EAURACRONTrailType::PrismalFlow", (int64)EAURACRONTrailType::PrismalFlow },
		{ "EAURACRONTrailType::EtherealPath", (int64)EAURACRONTrailType::EtherealPath },
		{ "EAURACRONTrailType::NexusConnection", (int64)EAURACRONTrailType::NexusConnection },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONTrailType",
	"EAURACRONTrailType",
	Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONTrailType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONTrailType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONTrailType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONTrailType.InnerSingleton;
}
// ********** End Enum EAURACRONTrailType **********************************************************

// ********** Begin Enum EAURACRONHardwareQuality **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONHardwareQuality;
static UEnum* EAURACRONHardwareQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONHardwareQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONHardwareQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONHardwareQuality"));
	}
	return Z_Registration_Info_UEnum_EAURACRONHardwareQuality.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONHardwareQuality>()
{
	return EAURACRONHardwareQuality_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estados de qualidade de hardware para otimiza\xc3\xa7\xc3\xa3o adaptativa\n * Sistema de detec\xc3\xa7\xc3\xa3o autom\xc3\xa1tica de performance\n */" },
#endif
		{ "Entry.DisplayName", "Entry Level" },
		{ "Entry.Name", "EAURACRONHardwareQuality::Entry" },
		{ "HighEnd.Comment", "// 3-4GB RAM, GPU intermedi\xc3\xa1ria  \n" },
		{ "HighEnd.DisplayName", "High End" },
		{ "HighEnd.Name", "EAURACRONHardwareQuality::HighEnd" },
		{ "HighEnd.ToolTip", "3-4GB RAM, GPU intermedi\xc3\xa1ria" },
		{ "MidRange.Comment", "// 2-3GB RAM, GPU b\xc3\xa1sica\n" },
		{ "MidRange.DisplayName", "Mid Range" },
		{ "MidRange.Name", "EAURACRONHardwareQuality::MidRange" },
		{ "MidRange.ToolTip", "2-3GB RAM, GPU b\xc3\xa1sica" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estados de qualidade de hardware para otimiza\xc3\xa7\xc3\xa3o adaptativa\nSistema de detec\xc3\xa7\xc3\xa3o autom\xc3\xa1tica de performance" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONHardwareQuality::Entry", (int64)EAURACRONHardwareQuality::Entry },
		{ "EAURACRONHardwareQuality::MidRange", (int64)EAURACRONHardwareQuality::MidRange },
		{ "EAURACRONHardwareQuality::HighEnd", (int64)EAURACRONHardwareQuality::HighEnd },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONHardwareQuality",
	"EAURACRONHardwareQuality",
	Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality()
{
	if (!Z_Registration_Info_UEnum_EAURACRONHardwareQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONHardwareQuality.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONHardwareQuality.InnerSingleton;
}
// ********** End Enum EAURACRONHardwareQuality ****************************************************

// ********** Begin Enum EAURACRONSigilType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONSigilType;
static UEnum* EAURACRONSigilType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONSigilType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONSigilType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONSigilType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONSigilType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONSigilType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONSigilType>()
{
	return EAURACRONSigilType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONSigilType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aegis.DisplayName", "Aegis" },
		{ "Aegis.Name", "EAURACRONSigilType::Aegis" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de s\xc3\xadgilos do sistema de fus\xc3\xa3o\n * Sistema de S\xc3\xadgilos Auracron (Fusion 2.0)\n */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "Ruin.Comment", "// Tanque - +15% HP, Armadura adaptativa\n" },
		{ "Ruin.DisplayName", "Ruin" },
		{ "Ruin.Name", "EAURACRONSigilType::Ruin" },
		{ "Ruin.ToolTip", "Tanque - +15% HP, Armadura adaptativa" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de s\xc3\xadgilos do sistema de fus\xc3\xa3o\nSistema de S\xc3\xadgilos Auracron (Fusion 2.0)" },
#endif
		{ "Vesper.Comment", "// Dano - +12% ATK/AP adaptativo\n" },
		{ "Vesper.DisplayName", "Vesper" },
		{ "Vesper.Name", "EAURACRONSigilType::Vesper" },
		{ "Vesper.ToolTip", "Dano - +12% ATK/AP adaptativo" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONSigilType::Aegis", (int64)EAURACRONSigilType::Aegis },
		{ "EAURACRONSigilType::Ruin", (int64)EAURACRONSigilType::Ruin },
		{ "EAURACRONSigilType::Vesper", (int64)EAURACRONSigilType::Vesper },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONSigilType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONSigilType",
	"EAURACRONSigilType",
	Z_Construct_UEnum_AURACRON_EAURACRONSigilType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONSigilType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONSigilType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONSigilType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONSigilType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONSigilType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONSigilType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONSigilType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONSigilType.InnerSingleton;
}
// ********** End Enum EAURACRONSigilType **********************************************************

// ********** Begin Enum EAURACRONNetworkState *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONNetworkState;
static UEnum* EAURACRONNetworkState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONNetworkState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONNetworkState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONNetworkState, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONNetworkState"));
	}
	return Z_Registration_Info_UEnum_EAURACRONNetworkState.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONNetworkState>()
{
	return EAURACRONNetworkState_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONNetworkState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estados de replica\xc3\xa7\xc3\xa3o de rede\n * Sistema robusto para multiplayer\n */" },
#endif
		{ "Connected.DisplayName", "Conectado" },
		{ "Connected.Name", "EAURACRONNetworkState::Connected" },
		{ "Connecting.DisplayName", "Conectando" },
		{ "Connecting.Name", "EAURACRONNetworkState::Connecting" },
		{ "Disconnected.DisplayName", "Desconectado" },
		{ "Disconnected.Name", "EAURACRONNetworkState::Disconnected" },
		{ "Error.DisplayName", "Erro" },
		{ "Error.Name", "EAURACRONNetworkState::Error" },
		{ "InGame.DisplayName", "Em Jogo" },
		{ "InGame.Name", "EAURACRONNetworkState::InGame" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "Ready.DisplayName", "Pronto" },
		{ "Ready.Name", "EAURACRONNetworkState::Ready" },
		{ "Reconnecting.DisplayName", "Reconectando" },
		{ "Reconnecting.Name", "EAURACRONNetworkState::Reconnecting" },
		{ "Synchronizing.DisplayName", "Sincronizando" },
		{ "Synchronizing.Name", "EAURACRONNetworkState::Synchronizing" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estados de replica\xc3\xa7\xc3\xa3o de rede\nSistema robusto para multiplayer" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONNetworkState::Disconnected", (int64)EAURACRONNetworkState::Disconnected },
		{ "EAURACRONNetworkState::Connecting", (int64)EAURACRONNetworkState::Connecting },
		{ "EAURACRONNetworkState::Connected", (int64)EAURACRONNetworkState::Connected },
		{ "EAURACRONNetworkState::Synchronizing", (int64)EAURACRONNetworkState::Synchronizing },
		{ "EAURACRONNetworkState::Ready", (int64)EAURACRONNetworkState::Ready },
		{ "EAURACRONNetworkState::InGame", (int64)EAURACRONNetworkState::InGame },
		{ "EAURACRONNetworkState::Reconnecting", (int64)EAURACRONNetworkState::Reconnecting },
		{ "EAURACRONNetworkState::Error", (int64)EAURACRONNetworkState::Error },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONNetworkState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONNetworkState",
	"EAURACRONNetworkState",
	Z_Construct_UEnum_AURACRON_EAURACRONNetworkState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONNetworkState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONNetworkState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONNetworkState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONNetworkState()
{
	if (!Z_Registration_Info_UEnum_EAURACRONNetworkState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONNetworkState.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONNetworkState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONNetworkState.InnerSingleton;
}
// ********** End Enum EAURACRONNetworkState *******************************************************

// ********** Begin Enum EAURACRONObjectiveCategory ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONObjectiveCategory;
static UEnum* EAURACRONObjectiveCategory_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveCategory.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONObjectiveCategory.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONObjectiveCategory"));
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveCategory.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONObjectiveCategory>()
{
	return EAURACRONObjectiveCategory_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Bonus.Comment", "// Objetivos de catch-up\n" },
		{ "Bonus.DisplayName", "B\xc3\xb4nus" },
		{ "Bonus.Name", "EAURACRONObjectiveCategory::Bonus" },
		{ "Bonus.ToolTip", "Objetivos de catch-up" },
		{ "CatchUp.Comment", "// Objetivos principais\n" },
		{ "CatchUp.DisplayName", "Recupera\xc3\xa7\xc3\xa3o" },
		{ "CatchUp.Name", "EAURACRONObjectiveCategory::CatchUp" },
		{ "CatchUp.ToolTip", "Objetivos principais" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Categoria de objetivos procedurais\n * Sistema de classifica\xc3\xa7\xc3\xa3o para balanceamento\n */" },
#endif
		{ "Core.DisplayName", "Principal" },
		{ "Core.Name", "EAURACRONObjectiveCategory::Core" },
		{ "Event.Comment", "// Objetivos extras\n" },
		{ "Event.DisplayName", "Evento" },
		{ "Event.Name", "EAURACRONObjectiveCategory::Event" },
		{ "Event.ToolTip", "Objetivos extras" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Categoria de objetivos procedurais\nSistema de classifica\xc3\xa7\xc3\xa3o para balanceamento" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONObjectiveCategory::Core", (int64)EAURACRONObjectiveCategory::Core },
		{ "EAURACRONObjectiveCategory::CatchUp", (int64)EAURACRONObjectiveCategory::CatchUp },
		{ "EAURACRONObjectiveCategory::Bonus", (int64)EAURACRONObjectiveCategory::Bonus },
		{ "EAURACRONObjectiveCategory::Event", (int64)EAURACRONObjectiveCategory::Event },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONObjectiveCategory",
	"EAURACRONObjectiveCategory",
	Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveCategory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONObjectiveCategory.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveCategory.InnerSingleton;
}
// ********** End Enum EAURACRONObjectiveCategory **************************************************

// ********** Begin Enum EAURACRONObjectiveState ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONObjectiveState;
static UEnum* EAURACRONObjectiveState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONObjectiveState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONObjectiveState"));
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveState.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONObjectiveState>()
{
	return EAURACRONObjectiveState_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Active.Comment", "// Objetivo n\xc3\xa3o ativo\n" },
		{ "Active.DisplayName", "Ativo" },
		{ "Active.Name", "EAURACRONObjectiveState::Active" },
		{ "Active.ToolTip", "Objetivo n\xc3\xa3o ativo" },
		{ "BlueprintType", "true" },
		{ "Captured.Comment", "// Objetivo sendo capturado\n" },
		{ "Captured.DisplayName", "Capturado" },
		{ "Captured.Name", "EAURACRONObjectiveState::Captured" },
		{ "Captured.ToolTip", "Objetivo sendo capturado" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estados de objetivos procedurais\n * Sistema de controle de estado dos objetivos\n */" },
#endif
		{ "Completed.Comment", "// Objetivo capturado por uma equipe\n" },
		{ "Completed.DisplayName", "Completado" },
		{ "Completed.Name", "EAURACRONObjectiveState::Completed" },
		{ "Completed.ToolTip", "Objetivo capturado por uma equipe" },
		{ "Expired.Comment", "// Objetivo completado\n" },
		{ "Expired.DisplayName", "Expirado" },
		{ "Expired.Name", "EAURACRONObjectiveState::Expired" },
		{ "Expired.ToolTip", "Objetivo completado" },
		{ "Inactive.DisplayName", "Inativo" },
		{ "Inactive.Name", "EAURACRONObjectiveState::Inactive" },
		{ "InProgress.Comment", "// Objetivo ativo e dispon\xc3\xadvel\n" },
		{ "InProgress.DisplayName", "Em Progresso" },
		{ "InProgress.Name", "EAURACRONObjectiveState::InProgress" },
		{ "InProgress.ToolTip", "Objetivo ativo e dispon\xc3\xadvel" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estados de objetivos procedurais\nSistema de controle de estado dos objetivos" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONObjectiveState::Inactive", (int64)EAURACRONObjectiveState::Inactive },
		{ "EAURACRONObjectiveState::Active", (int64)EAURACRONObjectiveState::Active },
		{ "EAURACRONObjectiveState::InProgress", (int64)EAURACRONObjectiveState::InProgress },
		{ "EAURACRONObjectiveState::Captured", (int64)EAURACRONObjectiveState::Captured },
		{ "EAURACRONObjectiveState::Completed", (int64)EAURACRONObjectiveState::Completed },
		{ "EAURACRONObjectiveState::Expired", (int64)EAURACRONObjectiveState::Expired },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONObjectiveState",
	"EAURACRONObjectiveState",
	Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONObjectiveState.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveState.InnerSingleton;
}
// ********** End Enum EAURACRONObjectiveState *****************************************************

// ********** Begin Enum EAURACRONEnergyType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONEnergyType;
static UEnum* EAURACRONEnergyType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONEnergyType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONEnergyType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONEnergyType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONEnergyType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONEnergyType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONEnergyType>()
{
	return EAURACRONEnergyType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONEnergyType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Chaos.Comment", "// Energia prism\xc3\xa1tica multicolor\n" },
		{ "Chaos.DisplayName", "Caos" },
		{ "Chaos.Name", "EAURACRONEnergyType::Chaos" },
		{ "Chaos.ToolTip", "Energia prism\xc3\xa1tica multicolor" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de energia para efeitos especiais\n * Sistema de classifica\xc3\xa7\xc3\xa3o de energia\n */" },
#endif
		{ "Golden.DisplayName", "Energia Dourada" },
		{ "Golden.Name", "EAURACRONEnergyType::Golden" },
		{ "Lunar.Comment", "// Energia solar dourada\n" },
		{ "Lunar.DisplayName", "Lunar" },
		{ "Lunar.Name", "EAURACRONEnergyType::Lunar" },
		{ "Lunar.ToolTip", "Energia solar dourada" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "Prismal.Comment", "// Energia lunar prateada\n" },
		{ "Prismal.DisplayName", "Prismal" },
		{ "Prismal.Name", "EAURACRONEnergyType::Prismal" },
		{ "Prismal.ToolTip", "Energia lunar prateada" },
		{ "Silver.Comment", "// Energia dos Portais Radiantes\n" },
		{ "Silver.DisplayName", "Energia Prateada" },
		{ "Silver.Name", "EAURACRONEnergyType::Silver" },
		{ "Silver.ToolTip", "Energia dos Portais Radiantes" },
		{ "Solar.Comment", "// Energia dos Portais Umbrais\n" },
		{ "Solar.DisplayName", "Solar" },
		{ "Solar.Name", "EAURACRONEnergyType::Solar" },
		{ "Solar.ToolTip", "Energia dos Portais Umbrais" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de energia para efeitos especiais\nSistema de classifica\xc3\xa7\xc3\xa3o de energia" },
#endif
		{ "Violet.Comment", "// Energia dos Portais Zephyr\n" },
		{ "Violet.DisplayName", "Energia Violeta" },
		{ "Violet.Name", "EAURACRONEnergyType::Violet" },
		{ "Violet.ToolTip", "Energia dos Portais Zephyr" },
		{ "Void.Comment", "// Energia ca\xc3\xb3tica vermelha\n" },
		{ "Void.DisplayName", "Vazio" },
		{ "Void.Name", "EAURACRONEnergyType::Void" },
		{ "Void.ToolTip", "Energia ca\xc3\xb3tica vermelha" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONEnergyType::Golden", (int64)EAURACRONEnergyType::Golden },
		{ "EAURACRONEnergyType::Silver", (int64)EAURACRONEnergyType::Silver },
		{ "EAURACRONEnergyType::Violet", (int64)EAURACRONEnergyType::Violet },
		{ "EAURACRONEnergyType::Solar", (int64)EAURACRONEnergyType::Solar },
		{ "EAURACRONEnergyType::Lunar", (int64)EAURACRONEnergyType::Lunar },
		{ "EAURACRONEnergyType::Prismal", (int64)EAURACRONEnergyType::Prismal },
		{ "EAURACRONEnergyType::Chaos", (int64)EAURACRONEnergyType::Chaos },
		{ "EAURACRONEnergyType::Void", (int64)EAURACRONEnergyType::Void },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONEnergyType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONEnergyType",
	"EAURACRONEnergyType",
	Z_Construct_UEnum_AURACRON_EAURACRONEnergyType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONEnergyType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONEnergyType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONEnergyType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnergyType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONEnergyType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONEnergyType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONEnergyType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONEnergyType.InnerSingleton;
}
// ********** End Enum EAURACRONEnergyType *********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONEnums_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAURACRONMapPhase_StaticEnum, TEXT("EAURACRONMapPhase"), &Z_Registration_Info_UEnum_EAURACRONMapPhase, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2541365769U) },
		{ EAURACRONEnvironmentType_StaticEnum, TEXT("EAURACRONEnvironmentType"), &Z_Registration_Info_UEnum_EAURACRONEnvironmentType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2161956974U) },
		{ EAURACRONObjectiveType_StaticEnum, TEXT("EAURACRONObjectiveType"), &Z_Registration_Info_UEnum_EAURACRONObjectiveType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 11509637U) },
		{ EAURACRONBuffType_StaticEnum, TEXT("EAURACRONBuffType"), &Z_Registration_Info_UEnum_EAURACRONBuffType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 362549284U) },
		{ EAURACRONTemporalEffectType_StaticEnum, TEXT("EAURACRONTemporalEffectType"), &Z_Registration_Info_UEnum_EAURACRONTemporalEffectType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3331782527U) },
		{ EAURACRONTrailType_StaticEnum, TEXT("EAURACRONTrailType"), &Z_Registration_Info_UEnum_EAURACRONTrailType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2049964576U) },
		{ EAURACRONHardwareQuality_StaticEnum, TEXT("EAURACRONHardwareQuality"), &Z_Registration_Info_UEnum_EAURACRONHardwareQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 919796832U) },
		{ EAURACRONSigilType_StaticEnum, TEXT("EAURACRONSigilType"), &Z_Registration_Info_UEnum_EAURACRONSigilType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1798462891U) },
		{ EAURACRONNetworkState_StaticEnum, TEXT("EAURACRONNetworkState"), &Z_Registration_Info_UEnum_EAURACRONNetworkState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2714070138U) },
		{ EAURACRONObjectiveCategory_StaticEnum, TEXT("EAURACRONObjectiveCategory"), &Z_Registration_Info_UEnum_EAURACRONObjectiveCategory, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1919075103U) },
		{ EAURACRONObjectiveState_StaticEnum, TEXT("EAURACRONObjectiveState"), &Z_Registration_Info_UEnum_EAURACRONObjectiveState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1497233863U) },
		{ EAURACRONEnergyType_StaticEnum, TEXT("EAURACRONEnergyType"), &Z_Registration_Info_UEnum_EAURACRONEnergyType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 81350420U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONEnums_h__Script_AURACRON_1330274743(TEXT("/Script/AURACRON"),
	nullptr, 0,
	nullptr, 0,
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONEnums_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONEnums_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
