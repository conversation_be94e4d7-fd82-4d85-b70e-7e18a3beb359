// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Components/AURACRONMovementComponent.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONMovementComponent() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_UAURACRONMovementComponent();
AURACRON_API UClass* Z_Construct_UClass_UAURACRONMovementComponent_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_UAURACRONSigilComponent_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMovementState();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSpeedModifier();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UCharacterMovementComponent();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FSpeedModifier ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSpeedModifier;
class UScriptStruct* FSpeedModifier::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSpeedModifier.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSpeedModifier.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSpeedModifier, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SpeedModifier"));
	}
	return Z_Registration_Info_UScriptStruct_FSpeedModifier.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSpeedModifier_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para modificadores tempor\xc3\xa1rios de velocidade\n */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para modificadores tempor\xc3\xa1rios de velocidade" },
#endif
	};
#endif // WITH_METADATA
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSpeedModifier>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSpeedModifier_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SpeedModifier",
	nullptr,
	0,
	sizeof(FSpeedModifier),
	alignof(FSpeedModifier),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSpeedModifier_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSpeedModifier_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSpeedModifier()
{
	if (!Z_Registration_Info_UScriptStruct_FSpeedModifier.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSpeedModifier.InnerSingleton, Z_Construct_UScriptStruct_FSpeedModifier_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSpeedModifier.InnerSingleton;
}
// ********** End ScriptStruct FSpeedModifier ******************************************************

// ********** Begin Enum EAURACRONMovementState ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONMovementState;
static UEnum* EAURACRONMovementState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONMovementState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONMovementState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONMovementState, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONMovementState"));
	}
	return Z_Registration_Info_UEnum_EAURACRONMovementState.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONMovementState>()
{
	return EAURACRONMovementState_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONMovementState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estados especiais de movimento do AURACRON\n */" },
#endif
		{ "EnvironmentBoost.Comment", "// Dash de S\xc3\xadgilo\n" },
		{ "EnvironmentBoost.DisplayName", "Environment Boost" },
		{ "EnvironmentBoost.Name", "EAURACRONMovementState::EnvironmentBoost" },
		{ "EnvironmentBoost.ToolTip", "Dash de S\xc3\xadgilo" },
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "EAURACRONMovementState::Normal" },
		{ "PrismalFlow.Comment", "// Movimento padr\xc3\xa3o\n" },
		{ "PrismalFlow.DisplayName", "Prismal Flow" },
		{ "PrismalFlow.Name", "EAURACRONMovementState::PrismalFlow" },
		{ "PrismalFlow.ToolTip", "Movimento padr\xc3\xa3o" },
		{ "Rooted.Comment", "// Atordoado\n" },
		{ "Rooted.DisplayName", "Rooted" },
		{ "Rooted.Name", "EAURACRONMovementState::Rooted" },
		{ "Rooted.ToolTip", "Atordoado" },
		{ "SigilDash.Comment", "// Movimento no fluxo prism\xc3\xa1tico\n" },
		{ "SigilDash.DisplayName", "Sigil Dash" },
		{ "SigilDash.Name", "EAURACRONMovementState::SigilDash" },
		{ "SigilDash.ToolTip", "Movimento no fluxo prism\xc3\xa1tico" },
		{ "Stunned.Comment", "// Boost de ambiente\n" },
		{ "Stunned.DisplayName", "Stunned" },
		{ "Stunned.Name", "EAURACRONMovementState::Stunned" },
		{ "Stunned.ToolTip", "Boost de ambiente" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estados especiais de movimento do AURACRON" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONMovementState::Normal", (int64)EAURACRONMovementState::Normal },
		{ "EAURACRONMovementState::PrismalFlow", (int64)EAURACRONMovementState::PrismalFlow },
		{ "EAURACRONMovementState::SigilDash", (int64)EAURACRONMovementState::SigilDash },
		{ "EAURACRONMovementState::EnvironmentBoost", (int64)EAURACRONMovementState::EnvironmentBoost },
		{ "EAURACRONMovementState::Stunned", (int64)EAURACRONMovementState::Stunned },
		{ "EAURACRONMovementState::Rooted", (int64)EAURACRONMovementState::Rooted },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONMovementState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONMovementState",
	"EAURACRONMovementState",
	Z_Construct_UEnum_AURACRON_EAURACRONMovementState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONMovementState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONMovementState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONMovementState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMovementState()
{
	if (!Z_Registration_Info_UEnum_EAURACRONMovementState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONMovementState.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONMovementState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONMovementState.InnerSingleton;
}
// ********** End Enum EAURACRONMovementState ******************************************************

// ********** Begin ScriptStruct FAURACRONEnvironmentMovementConfig ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentMovementConfig;
class UScriptStruct* FAURACRONEnvironmentMovementConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentMovementConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentMovementConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONEnvironmentMovementConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentMovementConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xb5""es de movimento para diferentes ambientes\n */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de movimento para diferentes ambientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpeedMultiplier_MetaData[] = {
		{ "Category", "Movimento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de velocidade para este ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de velocidade para este ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccelerationMultiplier_MetaData[] = {
		{ "Category", "Movimento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de acelera\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de acelera\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JumpForceMultiplier_MetaData[] = {
		{ "Category", "Movimento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de for\xc3\xa7""a de pulo */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de for\xc3\xa7""a de pulo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowsFlight_MetaData[] = {
		{ "Category", "Movimento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se permite voo neste ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se permite voo neste ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpeedMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccelerationMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_JumpForceMultiplier;
	static void NewProp_bAllowsFlight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowsFlight;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONEnvironmentMovementConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::NewProp_SpeedMultiplier = { "SpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentMovementConfig, SpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpeedMultiplier_MetaData), NewProp_SpeedMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::NewProp_AccelerationMultiplier = { "AccelerationMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentMovementConfig, AccelerationMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccelerationMultiplier_MetaData), NewProp_AccelerationMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::NewProp_JumpForceMultiplier = { "JumpForceMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentMovementConfig, JumpForceMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JumpForceMultiplier_MetaData), NewProp_JumpForceMultiplier_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::NewProp_bAllowsFlight_SetBit(void* Obj)
{
	((FAURACRONEnvironmentMovementConfig*)Obj)->bAllowsFlight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::NewProp_bAllowsFlight = { "bAllowsFlight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONEnvironmentMovementConfig), &Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::NewProp_bAllowsFlight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowsFlight_MetaData), NewProp_bAllowsFlight_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::NewProp_SpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::NewProp_AccelerationMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::NewProp_JumpForceMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::NewProp_bAllowsFlight,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONEnvironmentMovementConfig",
	Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::PropPointers),
	sizeof(FAURACRONEnvironmentMovementConfig),
	alignof(FAURACRONEnvironmentMovementConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentMovementConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentMovementConfig.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentMovementConfig.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONEnvironmentMovementConfig **********************************

// ********** Begin Class UAURACRONMovementComponent Function ApplySpeedModifier *******************
struct AURACRONMovementComponent_eventApplySpeedModifier_Parms
{
	float Multiplier;
	float Duration;
	FName ModifierName;
};
static FName NAME_UAURACRONMovementComponent_ApplySpeedModifier = FName(TEXT("ApplySpeedModifier"));
void UAURACRONMovementComponent::ApplySpeedModifier(float Multiplier, float Duration, FName ModifierName)
{
	AURACRONMovementComponent_eventApplySpeedModifier_Parms Parms;
	Parms.Multiplier=Multiplier;
	Parms.Duration=Duration;
	Parms.ModifierName=ModifierName;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONMovementComponent_ApplySpeedModifier);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Movimento|Modificadores" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplica um modificador tempor\xc3\xa1rio de velocidade */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica um modificador tempor\xc3\xa1rio de velocidade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Multiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FNamePropertyParams NewProp_ModifierName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier_Statics::NewProp_Multiplier = { "Multiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventApplySpeedModifier_Parms, Multiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventApplySpeedModifier_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier_Statics::NewProp_ModifierName = { "ModifierName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventApplySpeedModifier_Parms, ModifierName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier_Statics::NewProp_Multiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier_Statics::NewProp_ModifierName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "ApplySpeedModifier", Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier_Statics::PropPointers), sizeof(AURACRONMovementComponent_eventApplySpeedModifier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONMovementComponent_eventApplySpeedModifier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMovementComponent::execApplySpeedModifier)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Multiplier);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FNameProperty,Z_Param_ModifierName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplySpeedModifier_Implementation(Z_Param_Multiplier,Z_Param_Duration,Z_Param_ModifierName);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMovementComponent Function ApplySpeedModifier *********************

// ********** Begin Class UAURACRONMovementComponent Function CanPerformDash ***********************
struct Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash_Statics
{
	struct AURACRONMovementComponent_eventCanPerformDash_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Movimento|Dash" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verifica se pode executar dash */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se pode executar dash" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONMovementComponent_eventCanPerformDash_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONMovementComponent_eventCanPerformDash_Parms), &Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "CanPerformDash", Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash_Statics::AURACRONMovementComponent_eventCanPerformDash_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash_Statics::AURACRONMovementComponent_eventCanPerformDash_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMovementComponent::execCanPerformDash)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanPerformDash();
	P_NATIVE_END;
}
// ********** End Class UAURACRONMovementComponent Function CanPerformDash *************************

// ********** Begin Class UAURACRONMovementComponent Function EnterPrismalFlow *********************
struct AURACRONMovementComponent_eventEnterPrismalFlow_Parms
{
	FVector FlowDirection;
	float FlowSpeed;
};
static FName NAME_UAURACRONMovementComponent_EnterPrismalFlow = FName(TEXT("EnterPrismalFlow"));
void UAURACRONMovementComponent::EnterPrismalFlow(FVector FlowDirection, float FlowSpeed)
{
	AURACRONMovementComponent_eventEnterPrismalFlow_Parms Parms;
	Parms.FlowDirection=FlowDirection;
	Parms.FlowSpeed=FlowSpeed;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONMovementComponent_EnterPrismalFlow);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONMovementComponent_EnterPrismalFlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Movimento|Fluxo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Entra no fluxo prism\xc3\xa1tico */" },
#endif
		{ "CPP_Default_FlowSpeed", "1.000000" },
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Entra no fluxo prism\xc3\xa1tico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_EnterPrismalFlow_Statics::NewProp_FlowDirection = { "FlowDirection", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventEnterPrismalFlow_Parms, FlowDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_EnterPrismalFlow_Statics::NewProp_FlowSpeed = { "FlowSpeed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventEnterPrismalFlow_Parms, FlowSpeed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMovementComponent_EnterPrismalFlow_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_EnterPrismalFlow_Statics::NewProp_FlowDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_EnterPrismalFlow_Statics::NewProp_FlowSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_EnterPrismalFlow_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_EnterPrismalFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "EnterPrismalFlow", Z_Construct_UFunction_UAURACRONMovementComponent_EnterPrismalFlow_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_EnterPrismalFlow_Statics::PropPointers), sizeof(AURACRONMovementComponent_eventEnterPrismalFlow_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04A20CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_EnterPrismalFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_EnterPrismalFlow_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONMovementComponent_eventEnterPrismalFlow_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_EnterPrismalFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_EnterPrismalFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMovementComponent::execEnterPrismalFlow)
{
	P_GET_STRUCT(FVector,Z_Param_FlowDirection);
	P_GET_PROPERTY(FFloatProperty,Z_Param_FlowSpeed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnterPrismalFlow_Implementation(Z_Param_FlowDirection,Z_Param_FlowSpeed);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMovementComponent Function EnterPrismalFlow ***********************

// ********** Begin Class UAURACRONMovementComponent Function ExitPrismalFlow **********************
static FName NAME_UAURACRONMovementComponent_ExitPrismalFlow = FName(TEXT("ExitPrismalFlow"));
void UAURACRONMovementComponent::ExitPrismalFlow()
{
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONMovementComponent_ExitPrismalFlow);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_UAURACRONMovementComponent_ExitPrismalFlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Movimento|Fluxo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sai do fluxo prism\xc3\xa1tico */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sai do fluxo prism\xc3\xa1tico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_ExitPrismalFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "ExitPrismalFlow", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_ExitPrismalFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_ExitPrismalFlow_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_ExitPrismalFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_ExitPrismalFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMovementComponent::execExitPrismalFlow)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExitPrismalFlow_Implementation();
	P_NATIVE_END;
}
// ********** End Class UAURACRONMovementComponent Function ExitPrismalFlow ************************

// ********** Begin Class UAURACRONMovementComponent Function GetCurrentEnvironment ****************
struct Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment_Statics
{
	struct AURACRONMovementComponent_eventGetCurrentEnvironment_Parms
	{
		EAURACRONEnvironmentType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Movimento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m o ambiente atual */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m o ambiente atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventGetCurrentEnvironment_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2161956974
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "GetCurrentEnvironment", Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment_Statics::AURACRONMovementComponent_eventGetCurrentEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment_Statics::AURACRONMovementComponent_eventGetCurrentEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMovementComponent::execGetCurrentEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONEnvironmentType*)Z_Param__Result=P_THIS->GetCurrentEnvironment();
	P_NATIVE_END;
}
// ********** End Class UAURACRONMovementComponent Function GetCurrentEnvironment ******************

// ********** Begin Class UAURACRONMovementComponent Function GetModifiedSpeed *********************
struct Z_Construct_UFunction_UAURACRONMovementComponent_GetModifiedSpeed_Statics
{
	struct AURACRONMovementComponent_eventGetModifiedSpeed_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Movimento|Modificadores" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m a velocidade modificada atual */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m a velocidade modificada atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_GetModifiedSpeed_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventGetModifiedSpeed_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMovementComponent_GetModifiedSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_GetModifiedSpeed_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_GetModifiedSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_GetModifiedSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "GetModifiedSpeed", Z_Construct_UFunction_UAURACRONMovementComponent_GetModifiedSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_GetModifiedSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMovementComponent_GetModifiedSpeed_Statics::AURACRONMovementComponent_eventGetModifiedSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_GetModifiedSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_GetModifiedSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMovementComponent_GetModifiedSpeed_Statics::AURACRONMovementComponent_eventGetModifiedSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_GetModifiedSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_GetModifiedSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMovementComponent::execGetModifiedSpeed)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetModifiedSpeed();
	P_NATIVE_END;
}
// ********** End Class UAURACRONMovementComponent Function GetModifiedSpeed ***********************

// ********** Begin Class UAURACRONMovementComponent Function GetMovementState *********************
struct Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState_Statics
{
	struct AURACRONMovementComponent_eventGetMovementState_Parms
	{
		EAURACRONMovementState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Movimento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m o estado atual de movimento */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m o estado atual de movimento" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventGetMovementState_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONMovementState, METADATA_PARAMS(0, nullptr) }; // 3596517699
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "GetMovementState", Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState_Statics::AURACRONMovementComponent_eventGetMovementState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState_Statics::AURACRONMovementComponent_eventGetMovementState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMovementComponent::execGetMovementState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONMovementState*)Z_Param__Result=P_THIS->GetMovementState();
	P_NATIVE_END;
}
// ********** End Class UAURACRONMovementComponent Function GetMovementState ***********************

// ********** Begin Class UAURACRONMovementComponent Function IsInPrismalFlow **********************
struct Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow_Statics
{
	struct AURACRONMovementComponent_eventIsInPrismalFlow_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Movimento|Fluxo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verifica se est\xc3\xa1 no fluxo prism\xc3\xa1tico */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se est\xc3\xa1 no fluxo prism\xc3\xa1tico" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONMovementComponent_eventIsInPrismalFlow_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONMovementComponent_eventIsInPrismalFlow_Parms), &Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "IsInPrismalFlow", Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow_Statics::AURACRONMovementComponent_eventIsInPrismalFlow_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow_Statics::AURACRONMovementComponent_eventIsInPrismalFlow_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMovementComponent::execIsInPrismalFlow)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInPrismalFlow();
	P_NATIVE_END;
}
// ********** End Class UAURACRONMovementComponent Function IsInPrismalFlow ************************

// ********** Begin Class UAURACRONMovementComponent Function OnEnteredPrismalFlow *****************
struct AURACRONMovementComponent_eventOnEnteredPrismalFlow_Parms
{
	FVector FlowDirection;
	float FlowSpeed;
};
static FName NAME_UAURACRONMovementComponent_OnEnteredPrismalFlow = FName(TEXT("OnEnteredPrismalFlow"));
void UAURACRONMovementComponent::OnEnteredPrismalFlow(FVector FlowDirection, float FlowSpeed)
{
	AURACRONMovementComponent_eventOnEnteredPrismalFlow_Parms Parms;
	Parms.FlowDirection=FlowDirection;
	Parms.FlowSpeed=FlowSpeed;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONMovementComponent_OnEnteredPrismalFlow);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONMovementComponent_OnEnteredPrismalFlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Eventos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento chamado quando entra no fluxo prism\xc3\xa1tico */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento chamado quando entra no fluxo prism\xc3\xa1tico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_OnEnteredPrismalFlow_Statics::NewProp_FlowDirection = { "FlowDirection", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventOnEnteredPrismalFlow_Parms, FlowDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_OnEnteredPrismalFlow_Statics::NewProp_FlowSpeed = { "FlowSpeed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventOnEnteredPrismalFlow_Parms, FlowSpeed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMovementComponent_OnEnteredPrismalFlow_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_OnEnteredPrismalFlow_Statics::NewProp_FlowDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_OnEnteredPrismalFlow_Statics::NewProp_FlowSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_OnEnteredPrismalFlow_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_OnEnteredPrismalFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "OnEnteredPrismalFlow", Z_Construct_UFunction_UAURACRONMovementComponent_OnEnteredPrismalFlow_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_OnEnteredPrismalFlow_Statics::PropPointers), sizeof(AURACRONMovementComponent_eventOnEnteredPrismalFlow_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08820800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_OnEnteredPrismalFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_OnEnteredPrismalFlow_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONMovementComponent_eventOnEnteredPrismalFlow_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_OnEnteredPrismalFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_OnEnteredPrismalFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAURACRONMovementComponent Function OnEnteredPrismalFlow *******************

// ********** Begin Class UAURACRONMovementComponent Function OnExitedPrismalFlow ******************
static FName NAME_UAURACRONMovementComponent_OnExitedPrismalFlow = FName(TEXT("OnExitedPrismalFlow"));
void UAURACRONMovementComponent::OnExitedPrismalFlow()
{
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONMovementComponent_OnExitedPrismalFlow);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_UAURACRONMovementComponent_OnExitedPrismalFlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Eventos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento chamado quando sai do fluxo prism\xc3\xa1tico */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento chamado quando sai do fluxo prism\xc3\xa1tico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_OnExitedPrismalFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "OnExitedPrismalFlow", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_OnExitedPrismalFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_OnExitedPrismalFlow_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_OnExitedPrismalFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_OnExitedPrismalFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAURACRONMovementComponent Function OnExitedPrismalFlow ********************

// ********** Begin Class UAURACRONMovementComponent Function OnMovementStateChanged ***************
struct AURACRONMovementComponent_eventOnMovementStateChanged_Parms
{
	EAURACRONMovementState OldState;
	EAURACRONMovementState NewState;
};
static FName NAME_UAURACRONMovementComponent_OnMovementStateChanged = FName(TEXT("OnMovementStateChanged"));
void UAURACRONMovementComponent::OnMovementStateChanged(EAURACRONMovementState OldState, EAURACRONMovementState NewState)
{
	AURACRONMovementComponent_eventOnMovementStateChanged_Parms Parms;
	Parms.OldState=OldState;
	Parms.NewState=NewState;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONMovementComponent_OnMovementStateChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Eventos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento chamado quando o estado de movimento muda */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento chamado quando o estado de movimento muda" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::NewProp_OldState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::NewProp_OldState = { "OldState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventOnMovementStateChanged_Parms, OldState), Z_Construct_UEnum_AURACRON_EAURACRONMovementState, METADATA_PARAMS(0, nullptr) }; // 3596517699
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventOnMovementStateChanged_Parms, NewState), Z_Construct_UEnum_AURACRON_EAURACRONMovementState, METADATA_PARAMS(0, nullptr) }; // 3596517699
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::NewProp_OldState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::NewProp_OldState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "OnMovementStateChanged", Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::PropPointers), sizeof(AURACRONMovementComponent_eventOnMovementStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONMovementComponent_eventOnMovementStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAURACRONMovementComponent Function OnMovementStateChanged *****************

// ********** Begin Class UAURACRONMovementComponent Function OnRep_CurrentEnvironment *************
struct Z_Construct_UFunction_UAURACRONMovementComponent_OnRep_CurrentEnvironment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_OnRep_CurrentEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "OnRep_CurrentEnvironment", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_OnRep_CurrentEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_OnRep_CurrentEnvironment_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_OnRep_CurrentEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_OnRep_CurrentEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMovementComponent::execOnRep_CurrentEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_CurrentEnvironment();
	P_NATIVE_END;
}
// ********** End Class UAURACRONMovementComponent Function OnRep_CurrentEnvironment ***************

// ********** Begin Class UAURACRONMovementComponent Function OnRep_MovementState ******************
struct Z_Construct_UFunction_UAURACRONMovementComponent_OnRep_MovementState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de Replica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de Replica\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_OnRep_MovementState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "OnRep_MovementState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_OnRep_MovementState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_OnRep_MovementState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_OnRep_MovementState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_OnRep_MovementState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMovementComponent::execOnRep_MovementState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_MovementState();
	P_NATIVE_END;
}
// ********** End Class UAURACRONMovementComponent Function OnRep_MovementState ********************

// ********** Begin Class UAURACRONMovementComponent Function OnSigilDashPerformed *****************
struct AURACRONMovementComponent_eventOnSigilDashPerformed_Parms
{
	FVector Direction;
	float Distance;
};
static FName NAME_UAURACRONMovementComponent_OnSigilDashPerformed = FName(TEXT("OnSigilDashPerformed"));
void UAURACRONMovementComponent::OnSigilDashPerformed(FVector Direction, float Distance)
{
	AURACRONMovementComponent_eventOnSigilDashPerformed_Parms Parms;
	Parms.Direction=Direction;
	Parms.Distance=Distance;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONMovementComponent_OnSigilDashPerformed);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONMovementComponent_OnSigilDashPerformed_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Eventos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento chamado quando executa dash */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento chamado quando executa dash" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Direction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_OnSigilDashPerformed_Statics::NewProp_Direction = { "Direction", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventOnSigilDashPerformed_Parms, Direction), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_OnSigilDashPerformed_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventOnSigilDashPerformed_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMovementComponent_OnSigilDashPerformed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_OnSigilDashPerformed_Statics::NewProp_Direction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_OnSigilDashPerformed_Statics::NewProp_Distance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_OnSigilDashPerformed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_OnSigilDashPerformed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "OnSigilDashPerformed", Z_Construct_UFunction_UAURACRONMovementComponent_OnSigilDashPerformed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_OnSigilDashPerformed_Statics::PropPointers), sizeof(AURACRONMovementComponent_eventOnSigilDashPerformed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08820800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_OnSigilDashPerformed_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_OnSigilDashPerformed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONMovementComponent_eventOnSigilDashPerformed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_OnSigilDashPerformed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_OnSigilDashPerformed_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAURACRONMovementComponent Function OnSigilDashPerformed *******************

// ********** Begin Class UAURACRONMovementComponent Function PerformSigilDash *********************
struct AURACRONMovementComponent_eventPerformSigilDash_Parms
{
	FVector Direction;
	float Distance;
	float Duration;
};
static FName NAME_UAURACRONMovementComponent_PerformSigilDash = FName(TEXT("PerformSigilDash"));
void UAURACRONMovementComponent::PerformSigilDash(FVector Direction, float Distance, float Duration)
{
	AURACRONMovementComponent_eventPerformSigilDash_Parms Parms;
	Parms.Direction=Direction;
	Parms.Distance=Distance;
	Parms.Duration=Duration;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONMovementComponent_PerformSigilDash);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Movimento|Dash" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Executa um dash de S\xc3\xadgilo */" },
#endif
		{ "CPP_Default_Distance", "800.000000" },
		{ "CPP_Default_Duration", "0.300000" },
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Executa um dash de S\xc3\xadgilo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Direction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash_Statics::NewProp_Direction = { "Direction", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventPerformSigilDash_Parms, Direction), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventPerformSigilDash_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventPerformSigilDash_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash_Statics::NewProp_Direction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "PerformSigilDash", Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash_Statics::PropPointers), sizeof(AURACRONMovementComponent_eventPerformSigilDash_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04A20CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONMovementComponent_eventPerformSigilDash_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMovementComponent::execPerformSigilDash)
{
	P_GET_STRUCT(FVector,Z_Param_Direction);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PerformSigilDash_Implementation(Z_Param_Direction,Z_Param_Distance,Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMovementComponent Function PerformSigilDash ***********************

// ********** Begin Class UAURACRONMovementComponent Function RemoveSpeedModifier ******************
struct AURACRONMovementComponent_eventRemoveSpeedModifier_Parms
{
	FName ModifierName;
};
static FName NAME_UAURACRONMovementComponent_RemoveSpeedModifier = FName(TEXT("RemoveSpeedModifier"));
void UAURACRONMovementComponent::RemoveSpeedModifier(FName ModifierName)
{
	AURACRONMovementComponent_eventRemoveSpeedModifier_Parms Parms;
	Parms.ModifierName=ModifierName;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONMovementComponent_RemoveSpeedModifier);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONMovementComponent_RemoveSpeedModifier_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Movimento|Modificadores" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Remove um modificador de velocidade */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove um modificador de velocidade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ModifierName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_RemoveSpeedModifier_Statics::NewProp_ModifierName = { "ModifierName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventRemoveSpeedModifier_Parms, ModifierName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMovementComponent_RemoveSpeedModifier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_RemoveSpeedModifier_Statics::NewProp_ModifierName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_RemoveSpeedModifier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_RemoveSpeedModifier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "RemoveSpeedModifier", Z_Construct_UFunction_UAURACRONMovementComponent_RemoveSpeedModifier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_RemoveSpeedModifier_Statics::PropPointers), sizeof(AURACRONMovementComponent_eventRemoveSpeedModifier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_RemoveSpeedModifier_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_RemoveSpeedModifier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONMovementComponent_eventRemoveSpeedModifier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_RemoveSpeedModifier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_RemoveSpeedModifier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMovementComponent::execRemoveSpeedModifier)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ModifierName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveSpeedModifier_Implementation(Z_Param_ModifierName);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMovementComponent Function RemoveSpeedModifier ********************

// ********** Begin Class UAURACRONMovementComponent Function SetCurrentEnvironment ****************
struct AURACRONMovementComponent_eventSetCurrentEnvironment_Parms
{
	EAURACRONEnvironmentType Environment;
};
static FName NAME_UAURACRONMovementComponent_SetCurrentEnvironment = FName(TEXT("SetCurrentEnvironment"));
void UAURACRONMovementComponent::SetCurrentEnvironment(EAURACRONEnvironmentType Environment)
{
	AURACRONMovementComponent_eventSetCurrentEnvironment_Parms Parms;
	Parms.Environment=Environment;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONMovementComponent_SetCurrentEnvironment);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONMovementComponent_SetCurrentEnvironment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Movimento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Define o ambiente atual */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define o ambiente atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_SetCurrentEnvironment_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_SetCurrentEnvironment_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventSetCurrentEnvironment_Parms, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2161956974
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMovementComponent_SetCurrentEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_SetCurrentEnvironment_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_SetCurrentEnvironment_Statics::NewProp_Environment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_SetCurrentEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_SetCurrentEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "SetCurrentEnvironment", Z_Construct_UFunction_UAURACRONMovementComponent_SetCurrentEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_SetCurrentEnvironment_Statics::PropPointers), sizeof(AURACRONMovementComponent_eventSetCurrentEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_SetCurrentEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_SetCurrentEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONMovementComponent_eventSetCurrentEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_SetCurrentEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_SetCurrentEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMovementComponent::execSetCurrentEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_Environment);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetCurrentEnvironment_Implementation(EAURACRONEnvironmentType(Z_Param_Environment));
	P_NATIVE_END;
}
// ********** End Class UAURACRONMovementComponent Function SetCurrentEnvironment ******************

// ********** Begin Class UAURACRONMovementComponent Function SetMovementState *********************
struct AURACRONMovementComponent_eventSetMovementState_Parms
{
	EAURACRONMovementState NewState;
};
static FName NAME_UAURACRONMovementComponent_SetMovementState = FName(TEXT("SetMovementState"));
void UAURACRONMovementComponent::SetMovementState(EAURACRONMovementState NewState)
{
	AURACRONMovementComponent_eventSetMovementState_Parms Parms;
	Parms.NewState=NewState;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONMovementComponent_SetMovementState);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONMovementComponent_SetMovementState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Movimento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Define o estado atual de movimento */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define o estado atual de movimento" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_SetMovementState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONMovementComponent_SetMovementState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMovementComponent_eventSetMovementState_Parms, NewState), Z_Construct_UEnum_AURACRON_EAURACRONMovementState, METADATA_PARAMS(0, nullptr) }; // 3596517699
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMovementComponent_SetMovementState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_SetMovementState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMovementComponent_SetMovementState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_SetMovementState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMovementComponent_SetMovementState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMovementComponent, nullptr, "SetMovementState", Z_Construct_UFunction_UAURACRONMovementComponent_SetMovementState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_SetMovementState_Statics::PropPointers), sizeof(AURACRONMovementComponent_eventSetMovementState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMovementComponent_SetMovementState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMovementComponent_SetMovementState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONMovementComponent_eventSetMovementState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMovementComponent_SetMovementState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMovementComponent_SetMovementState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMovementComponent::execSetMovementState)
{
	P_GET_ENUM(EAURACRONMovementState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMovementState_Implementation(EAURACRONMovementState(Z_Param_NewState));
	P_NATIVE_END;
}
// ********** End Class UAURACRONMovementComponent Function SetMovementState ***********************

// ********** Begin Class UAURACRONMovementComponent ***********************************************
void UAURACRONMovementComponent::StaticRegisterNativesUAURACRONMovementComponent()
{
	UClass* Class = UAURACRONMovementComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplySpeedModifier", &UAURACRONMovementComponent::execApplySpeedModifier },
		{ "CanPerformDash", &UAURACRONMovementComponent::execCanPerformDash },
		{ "EnterPrismalFlow", &UAURACRONMovementComponent::execEnterPrismalFlow },
		{ "ExitPrismalFlow", &UAURACRONMovementComponent::execExitPrismalFlow },
		{ "GetCurrentEnvironment", &UAURACRONMovementComponent::execGetCurrentEnvironment },
		{ "GetModifiedSpeed", &UAURACRONMovementComponent::execGetModifiedSpeed },
		{ "GetMovementState", &UAURACRONMovementComponent::execGetMovementState },
		{ "IsInPrismalFlow", &UAURACRONMovementComponent::execIsInPrismalFlow },
		{ "OnRep_CurrentEnvironment", &UAURACRONMovementComponent::execOnRep_CurrentEnvironment },
		{ "OnRep_MovementState", &UAURACRONMovementComponent::execOnRep_MovementState },
		{ "PerformSigilDash", &UAURACRONMovementComponent::execPerformSigilDash },
		{ "RemoveSpeedModifier", &UAURACRONMovementComponent::execRemoveSpeedModifier },
		{ "SetCurrentEnvironment", &UAURACRONMovementComponent::execSetCurrentEnvironment },
		{ "SetMovementState", &UAURACRONMovementComponent::execSetMovementState },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAURACRONMovementComponent;
UClass* UAURACRONMovementComponent::GetPrivateStaticClass()
{
	using TClass = UAURACRONMovementComponent;
	if (!Z_Registration_Info_UClass_UAURACRONMovementComponent.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONMovementComponent"),
			Z_Registration_Info_UClass_UAURACRONMovementComponent.InnerSingleton,
			StaticRegisterNativesUAURACRONMovementComponent,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAURACRONMovementComponent.InnerSingleton;
}
UClass* Z_Construct_UClass_UAURACRONMovementComponent_NoRegister()
{
	return UAURACRONMovementComponent::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAURACRONMovementComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Componente de movimento customizado para personagens AURACRON\n * Implementa mec\xc3\xa2nicas espec\xc3\xad""ficas como movimento no Fluxo Prismal e efeitos de S\xc3\xadgilos\n */" },
#endif
		{ "IncludePath", "Components/AURACRONMovementComponent.h" },
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de movimento customizado para personagens AURACRON\nImplementa mec\xc3\xa2nicas espec\xc3\xad""ficas como movimento no Fluxo Prismal e efeitos de S\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMovementState_MetaData[] = {
		{ "Category", "AURACRON|Movimento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado atual de movimento */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado atual de movimento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEnvironment_MetaData[] = {
		{ "Category", "AURACRON|Movimento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ambiente atual */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ambiente atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentConfigs_MetaData[] = {
		{ "Category", "AURACRON|Movimento|Configura\xc3\xa7\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es de movimento por ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de movimento por ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInPrismalFlow_MetaData[] = {
		{ "Category", "AURACRON|Movimento|Fluxo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se est\xc3\xa1 atualmente no fluxo prism\xc3\xa1tico */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se est\xc3\xa1 atualmente no fluxo prism\xc3\xa1tico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrismalFlowDirection_MetaData[] = {
		{ "Category", "AURACRON|Movimento|Fluxo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dire\xc3\xa7\xc3\xa3o do fluxo prism\xc3\xa1tico */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dire\xc3\xa7\xc3\xa3o do fluxo prism\xc3\xa1tico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrismalFlowSpeed_MetaData[] = {
		{ "Category", "AURACRON|Movimento|Fluxo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade do fluxo prism\xc3\xa1tico */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade do fluxo prism\xc3\xa1tico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSpeedMultiplier_MetaData[] = {
		{ "Category", "AURACRON|Movimento|Fluxo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de velocidade no fluxo */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de velocidade no fluxo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsDashing_MetaData[] = {
		{ "Category", "AURACRON|Movimento|Dash" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se est\xc3\xa1 executando dash */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se est\xc3\xa1 executando dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashTimeRemaining_MetaData[] = {
		{ "Category", "AURACRON|Movimento|Dash" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo restante do dash */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo restante do dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashDirection_MetaData[] = {
		{ "Category", "AURACRON|Movimento|Dash" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dire\xc3\xa7\xc3\xa3o do dash */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dire\xc3\xa7\xc3\xa3o do dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashSpeed_MetaData[] = {
		{ "Category", "AURACRON|Movimento|Dash" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade do dash */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade do dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashCooldown_MetaData[] = {
		{ "Category", "AURACRON|Movimento|Dash" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cooldown do dash */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cooldown do dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashCooldownRemaining_MetaData[] = {
		{ "Category", "AURACRON|Movimento|Dash" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo restante do cooldown do dash */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo restante do cooldown do dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveSpeedModifiers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Modificadores ativos de velocidade */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Modificadores ativos de velocidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilComponent_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancia ao componente de S\xc3\xadgilos */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Components/AURACRONMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancia ao componente de S\xc3\xadgilos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMovementState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMovementState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentEnvironment;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EnvironmentConfigs_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentConfigs_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentConfigs_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EnvironmentConfigs;
	static void NewProp_bIsInPrismalFlow_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInPrismalFlow;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PrismalFlowDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PrismalFlowSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowSpeedMultiplier;
	static void NewProp_bIsDashing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsDashing;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DashTimeRemaining;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DashDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DashSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DashCooldown;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DashCooldownRemaining;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveSpeedModifiers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveSpeedModifiers;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SigilComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_ApplySpeedModifier, "ApplySpeedModifier" }, // 737430610
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_CanPerformDash, "CanPerformDash" }, // 1061479744
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_EnterPrismalFlow, "EnterPrismalFlow" }, // 3882008781
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_ExitPrismalFlow, "ExitPrismalFlow" }, // 1957234776
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_GetCurrentEnvironment, "GetCurrentEnvironment" }, // 829130800
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_GetModifiedSpeed, "GetModifiedSpeed" }, // 426969103
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_GetMovementState, "GetMovementState" }, // 1433295397
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_IsInPrismalFlow, "IsInPrismalFlow" }, // 2232740575
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_OnEnteredPrismalFlow, "OnEnteredPrismalFlow" }, // 4028696232
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_OnExitedPrismalFlow, "OnExitedPrismalFlow" }, // 2458600183
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_OnMovementStateChanged, "OnMovementStateChanged" }, // 158795687
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_OnRep_CurrentEnvironment, "OnRep_CurrentEnvironment" }, // 2141922553
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_OnRep_MovementState, "OnRep_MovementState" }, // 2099746815
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_OnSigilDashPerformed, "OnSigilDashPerformed" }, // 4007644106
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_PerformSigilDash, "PerformSigilDash" }, // 211845891
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_RemoveSpeedModifier, "RemoveSpeedModifier" }, // 493291368
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_SetCurrentEnvironment, "SetCurrentEnvironment" }, // 3690687145
		{ &Z_Construct_UFunction_UAURACRONMovementComponent_SetMovementState, "SetMovementState" }, // 3716854894
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAURACRONMovementComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_CurrentMovementState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_CurrentMovementState = { "CurrentMovementState", "OnRep_MovementState", (EPropertyFlags)0x0020080100000034, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONMovementComponent, CurrentMovementState), Z_Construct_UEnum_AURACRON_EAURACRONMovementState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMovementState_MetaData), NewProp_CurrentMovementState_MetaData) }; // 3596517699
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_CurrentEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_CurrentEnvironment = { "CurrentEnvironment", "OnRep_CurrentEnvironment", (EPropertyFlags)0x0020080100000034, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONMovementComponent, CurrentEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEnvironment_MetaData), NewProp_CurrentEnvironment_MetaData) }; // 2161956974
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_EnvironmentConfigs_ValueProp = { "EnvironmentConfigs", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig, METADATA_PARAMS(0, nullptr) }; // 836797345
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_EnvironmentConfigs_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_EnvironmentConfigs_Key_KeyProp = { "EnvironmentConfigs_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2161956974
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_EnvironmentConfigs = { "EnvironmentConfigs", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONMovementComponent, EnvironmentConfigs), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentConfigs_MetaData), NewProp_EnvironmentConfigs_MetaData) }; // 2161956974 836797345
void Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_bIsInPrismalFlow_SetBit(void* Obj)
{
	((UAURACRONMovementComponent*)Obj)->bIsInPrismalFlow = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_bIsInPrismalFlow = { "bIsInPrismalFlow", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAURACRONMovementComponent), &Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_bIsInPrismalFlow_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInPrismalFlow_MetaData), NewProp_bIsInPrismalFlow_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_PrismalFlowDirection = { "PrismalFlowDirection", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONMovementComponent, PrismalFlowDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrismalFlowDirection_MetaData), NewProp_PrismalFlowDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_PrismalFlowSpeed = { "PrismalFlowSpeed", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONMovementComponent, PrismalFlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrismalFlowSpeed_MetaData), NewProp_PrismalFlowSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_FlowSpeedMultiplier = { "FlowSpeedMultiplier", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONMovementComponent, FlowSpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSpeedMultiplier_MetaData), NewProp_FlowSpeedMultiplier_MetaData) };
void Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_bIsDashing_SetBit(void* Obj)
{
	((UAURACRONMovementComponent*)Obj)->bIsDashing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_bIsDashing = { "bIsDashing", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAURACRONMovementComponent), &Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_bIsDashing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsDashing_MetaData), NewProp_bIsDashing_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_DashTimeRemaining = { "DashTimeRemaining", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONMovementComponent, DashTimeRemaining), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashTimeRemaining_MetaData), NewProp_DashTimeRemaining_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_DashDirection = { "DashDirection", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONMovementComponent, DashDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashDirection_MetaData), NewProp_DashDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_DashSpeed = { "DashSpeed", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONMovementComponent, DashSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashSpeed_MetaData), NewProp_DashSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_DashCooldown = { "DashCooldown", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONMovementComponent, DashCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashCooldown_MetaData), NewProp_DashCooldown_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_DashCooldownRemaining = { "DashCooldownRemaining", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONMovementComponent, DashCooldownRemaining), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashCooldownRemaining_MetaData), NewProp_DashCooldownRemaining_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_ActiveSpeedModifiers_Inner = { "ActiveSpeedModifiers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSpeedModifier, METADATA_PARAMS(0, nullptr) }; // 141309589
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_ActiveSpeedModifiers = { "ActiveSpeedModifiers", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONMovementComponent, ActiveSpeedModifiers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveSpeedModifiers_MetaData), NewProp_ActiveSpeedModifiers_MetaData) }; // 141309589
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_SigilComponent = { "SigilComponent", nullptr, (EPropertyFlags)0x0144000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONMovementComponent, SigilComponent), Z_Construct_UClass_UAURACRONSigilComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilComponent_MetaData), NewProp_SigilComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAURACRONMovementComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_CurrentMovementState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_CurrentMovementState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_CurrentEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_CurrentEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_EnvironmentConfigs_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_EnvironmentConfigs_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_EnvironmentConfigs_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_EnvironmentConfigs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_bIsInPrismalFlow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_PrismalFlowDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_PrismalFlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_FlowSpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_bIsDashing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_DashTimeRemaining,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_DashDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_DashSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_DashCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_DashCooldownRemaining,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_ActiveSpeedModifiers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_ActiveSpeedModifiers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONMovementComponent_Statics::NewProp_SigilComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONMovementComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAURACRONMovementComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UCharacterMovementComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONMovementComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAURACRONMovementComponent_Statics::ClassParams = {
	&UAURACRONMovementComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAURACRONMovementComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONMovementComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONMovementComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_UAURACRONMovementComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAURACRONMovementComponent()
{
	if (!Z_Registration_Info_UClass_UAURACRONMovementComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAURACRONMovementComponent.OuterSingleton, Z_Construct_UClass_UAURACRONMovementComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAURACRONMovementComponent.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAURACRONMovementComponent::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_CurrentMovementState(TEXT("CurrentMovementState"));
	static FName Name_CurrentEnvironment(TEXT("CurrentEnvironment"));
	static FName Name_bIsInPrismalFlow(TEXT("bIsInPrismalFlow"));
	static FName Name_PrismalFlowDirection(TEXT("PrismalFlowDirection"));
	static FName Name_PrismalFlowSpeed(TEXT("PrismalFlowSpeed"));
	static FName Name_bIsDashing(TEXT("bIsDashing"));
	static FName Name_DashTimeRemaining(TEXT("DashTimeRemaining"));
	static FName Name_DashDirection(TEXT("DashDirection"));
	static FName Name_DashSpeed(TEXT("DashSpeed"));
	static FName Name_DashCooldownRemaining(TEXT("DashCooldownRemaining"));
	const bool bIsValid = true
		&& Name_CurrentMovementState == ClassReps[(int32)ENetFields_Private::CurrentMovementState].Property->GetFName()
		&& Name_CurrentEnvironment == ClassReps[(int32)ENetFields_Private::CurrentEnvironment].Property->GetFName()
		&& Name_bIsInPrismalFlow == ClassReps[(int32)ENetFields_Private::bIsInPrismalFlow].Property->GetFName()
		&& Name_PrismalFlowDirection == ClassReps[(int32)ENetFields_Private::PrismalFlowDirection].Property->GetFName()
		&& Name_PrismalFlowSpeed == ClassReps[(int32)ENetFields_Private::PrismalFlowSpeed].Property->GetFName()
		&& Name_bIsDashing == ClassReps[(int32)ENetFields_Private::bIsDashing].Property->GetFName()
		&& Name_DashTimeRemaining == ClassReps[(int32)ENetFields_Private::DashTimeRemaining].Property->GetFName()
		&& Name_DashDirection == ClassReps[(int32)ENetFields_Private::DashDirection].Property->GetFName()
		&& Name_DashSpeed == ClassReps[(int32)ENetFields_Private::DashSpeed].Property->GetFName()
		&& Name_DashCooldownRemaining == ClassReps[(int32)ENetFields_Private::DashCooldownRemaining].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAURACRONMovementComponent"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAURACRONMovementComponent);
UAURACRONMovementComponent::~UAURACRONMovementComponent() {}
// ********** End Class UAURACRONMovementComponent *************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAURACRONMovementState_StaticEnum, TEXT("EAURACRONMovementState"), &Z_Registration_Info_UEnum_EAURACRONMovementState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3596517699U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FSpeedModifier::StaticStruct, Z_Construct_UScriptStruct_FSpeedModifier_Statics::NewStructOps, TEXT("SpeedModifier"), &Z_Registration_Info_UScriptStruct_FSpeedModifier, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSpeedModifier), 141309589U) },
		{ FAURACRONEnvironmentMovementConfig::StaticStruct, Z_Construct_UScriptStruct_FAURACRONEnvironmentMovementConfig_Statics::NewStructOps, TEXT("AURACRONEnvironmentMovementConfig"), &Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentMovementConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONEnvironmentMovementConfig), 836797345U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAURACRONMovementComponent, UAURACRONMovementComponent::StaticClass, TEXT("UAURACRONMovementComponent"), &Z_Registration_Info_UClass_UAURACRONMovementComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAURACRONMovementComponent), 2215968669U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h__Script_AURACRON_2999558311(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h__Script_AURACRON_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_AURACRONMovementComponent_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
