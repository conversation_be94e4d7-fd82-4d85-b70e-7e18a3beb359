// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGChaosIsland.h"

#ifdef AURACRON_AURACRONPCGChaosIsland_generated_h
#error "AURACRONPCGChaosIsland.generated.h already included, missing '#pragma once' in AURACRONPCGChaosIsland.h"
#endif
#define AURACRON_AURACRONPCGChaosIsland_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;

// ********** Begin Class AChaosIsland *************************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h_24_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execUpdateEnvironmentalHazards); \
	DECLARE_FUNCTION(execUpdateUnstableTerrain); \
	DECLARE_FUNCTION(execGrantHighRiskReward); \
	DECLARE_FUNCTION(execRemoveUnstableTerrainEffects); \
	DECLARE_FUNCTION(execApplyUnstableTerrainEffect); \
	DECLARE_FUNCTION(execRemoveEnvironmentalHazardEffects); \
	DECLARE_FUNCTION(execApplyEnvironmentalHazardEffect); \
	DECLARE_FUNCTION(execRemoveChaosEffects); \
	DECLARE_FUNCTION(execSetActivityLevel); \
	DECLARE_FUNCTION(execApplyChaosEffect);


AURACRON_API UClass* Z_Construct_UClass_AChaosIsland_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h_24_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAChaosIsland(); \
	friend struct Z_Construct_UClass_AChaosIsland_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AChaosIsland_NoRegister(); \
public: \
	DECLARE_CLASS2(AChaosIsland, APrismalFlowIsland, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AChaosIsland_NoRegister) \
	DECLARE_SERIALIZER(AChaosIsland) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		ChaosIntensity=NETFIELD_REP_START, \
		ChaosDuration, \
		EnvironmentalHazardIntensity, \
		HighRiskRewardMultiplier, \
		TerrainInstabilityIntensity, \
		ChaosGameplayEffect, \
		EnvironmentalHazardEffect, \
		UnstableTerrainEffect, \
		NETFIELD_REP_END=UnstableTerrainEffect	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h_24_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AChaosIsland(AChaosIsland&&) = delete; \
	AChaosIsland(const AChaosIsland&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AChaosIsland); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AChaosIsland); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AChaosIsland) \
	NO_API virtual ~AChaosIsland();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h_21_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h_24_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h_24_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h_24_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h_24_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AChaosIsland;

// ********** End Class AChaosIsland ***************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
