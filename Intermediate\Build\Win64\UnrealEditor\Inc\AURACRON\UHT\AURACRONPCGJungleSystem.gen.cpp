// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGJungleSystem.h"
#include "Engine/TimerHandle.h"
#include "PCG/AURACRONPCGWorldPartitionIntegration.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGJungleSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGJungleSystem();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGJungleSystem_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONJungleAdaptiveBehavior();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONJunglePlayerProfile();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONJungleStrategy();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONJungleCampInfo();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONMeshComponentArray();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_UCurveFloat_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTimerHandle();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAURACRONJunglePlayerProfile **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONJunglePlayerProfile;
static UEnum* EAURACRONJunglePlayerProfile_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONJunglePlayerProfile.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONJunglePlayerProfile.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONJunglePlayerProfile, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONJunglePlayerProfile"));
	}
	return Z_Registration_Info_UEnum_EAURACRONJunglePlayerProfile.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONJunglePlayerProfile>()
{
	return EAURACRONJunglePlayerProfile_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONJunglePlayerProfile_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "EAURACRONJunglePlayerProfile::Aggressive" },
		{ "Balanced.Comment", "// Jogador que controla territ\xc3\xb3rio e vis\xc3\xa3o\n" },
		{ "Balanced.DisplayName", "Balanced" },
		{ "Balanced.Name", "EAURACRONJunglePlayerProfile::Balanced" },
		{ "Balanced.ToolTip", "Jogador que controla territ\xc3\xb3rio e vis\xc3\xa3o" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\xa7\xc3\xa3o para definir os perfis de jogador para IA adaptativa\n */" },
#endif
		{ "Control.Comment", "// Jogador que compartilha recursos\n" },
		{ "Control.DisplayName", "Control" },
		{ "Control.Name", "EAURACRONJunglePlayerProfile::Control" },
		{ "Control.ToolTip", "Jogador que compartilha recursos" },
		{ "Farming.Comment", "// Jogador agressivo que invade jungle frequentemente\n" },
		{ "Farming.DisplayName", "Farming" },
		{ "Farming.Name", "EAURACRONJunglePlayerProfile::Farming" },
		{ "Farming.ToolTip", "Jogador agressivo que invade jungle frequentemente" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
		{ "Objective.Comment", "// Jogador que foca em farmar jungle\n" },
		{ "Objective.DisplayName", "Objective" },
		{ "Objective.Name", "EAURACRONJunglePlayerProfile::Objective" },
		{ "Objective.ToolTip", "Jogador que foca em farmar jungle" },
		{ "Supportive.Comment", "// Jogador que prioriza objetivos\n" },
		{ "Supportive.DisplayName", "Supportive" },
		{ "Supportive.Name", "EAURACRONJunglePlayerProfile::Supportive" },
		{ "Supportive.ToolTip", "Jogador que prioriza objetivos" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\xa7\xc3\xa3o para definir os perfis de jogador para IA adaptativa" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONJunglePlayerProfile::Aggressive", (int64)EAURACRONJunglePlayerProfile::Aggressive },
		{ "EAURACRONJunglePlayerProfile::Farming", (int64)EAURACRONJunglePlayerProfile::Farming },
		{ "EAURACRONJunglePlayerProfile::Objective", (int64)EAURACRONJunglePlayerProfile::Objective },
		{ "EAURACRONJunglePlayerProfile::Supportive", (int64)EAURACRONJunglePlayerProfile::Supportive },
		{ "EAURACRONJunglePlayerProfile::Control", (int64)EAURACRONJunglePlayerProfile::Control },
		{ "EAURACRONJunglePlayerProfile::Balanced", (int64)EAURACRONJunglePlayerProfile::Balanced },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONJunglePlayerProfile_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONJunglePlayerProfile",
	"EAURACRONJunglePlayerProfile",
	Z_Construct_UEnum_AURACRON_EAURACRONJunglePlayerProfile_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONJunglePlayerProfile_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONJunglePlayerProfile_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONJunglePlayerProfile_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONJunglePlayerProfile()
{
	if (!Z_Registration_Info_UEnum_EAURACRONJunglePlayerProfile.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONJunglePlayerProfile.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONJunglePlayerProfile_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONJunglePlayerProfile.InnerSingleton;
}
// ********** End Enum EAURACRONJunglePlayerProfile ************************************************

// ********** Begin Enum EAURACRONJungleCampType ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONJungleCampType;
static UEnum* EAURACRONJungleCampType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONJungleCampType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONJungleCampType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONJungleCampType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONJungleCampType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONJungleCampType>()
{
	return EAURACRONJungleCampType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AncientGuardian.Comment", "// Dragon equivalent\n" },
		{ "AncientGuardian.DisplayName", "Ancient Guardian" },
		{ "AncientGuardian.Name", "EAURACRONJungleCampType::AncientGuardian" },
		{ "AncientGuardian.ToolTip", "Dragon equivalent" },
		{ "BlueprintType", "true" },
		{ "ChaosEssence.Comment", "// Blue Buff equivalent (mana regen + CDR)\n" },
		{ "ChaosEssence.DisplayName", "Chaos Essence" },
		{ "ChaosEssence.Name", "EAURACRONJungleCampType::ChaosEssence" },
		{ "ChaosEssence.ToolTip", "Blue Buff equivalent (mana regen + CDR)" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de jungle camps baseados no LoL\n */" },
#endif
		{ "CrystalWolves.Comment", "// Raptors equivalent\n" },
		{ "CrystalWolves.DisplayName", "Crystal Wolves" },
		{ "CrystalWolves.Name", "EAURACRONJungleCampType::CrystalWolves" },
		{ "CrystalWolves.ToolTip", "Raptors equivalent" },
		{ "EtherealGrove.Comment", "// Krugs equivalent\n" },
		{ "EtherealGrove.DisplayName", "Ethereal Grove" },
		{ "EtherealGrove.Name", "EAURACRONJungleCampType::EtherealGrove" },
		{ "EtherealGrove.ToolTip", "Krugs equivalent" },
		{ "FluxCrawler.Comment", "// Wind spirit camp\n" },
		{ "FluxCrawler.DisplayName", "Flux Crawler" },
		{ "FluxCrawler.Name", "EAURACRONJungleCampType::FluxCrawler" },
		{ "FluxCrawler.ToolTip", "Wind spirit camp" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
		{ "PrismalDragon.Comment", "// EPIC MONSTERS (equivalentes ao Dragon/Baron)\n" },
		{ "PrismalDragon.DisplayName", "Prismal Dragon" },
		{ "PrismalDragon.Name", "EAURACRONJungleCampType::PrismalDragon" },
		{ "PrismalDragon.ToolTip", "EPIC MONSTERS (equivalentes ao Dragon/Baron)" },
		{ "PrismalToad.Comment", "// Stone guardian camp\n" },
		{ "PrismalToad.DisplayName", "Prismal Toad" },
		{ "PrismalToad.Name", "EAURACRONJungleCampType::PrismalToad" },
		{ "PrismalToad.ToolTip", "Stone guardian camp" },
		{ "RadiantEssence.Comment", "// BUFF CAMPS (equivalentes ao Blue/Red Buff)\n" },
		{ "RadiantEssence.DisplayName", "Radiant Essence" },
		{ "RadiantEssence.Name", "EAURACRONJungleCampType::RadiantEssence" },
		{ "RadiantEssence.ToolTip", "BUFF CAMPS (equivalentes ao Blue/Red Buff)" },
		{ "SpectralPack.Comment", "// REGULAR CAMPS\n" },
		{ "SpectralPack.DisplayName", "Spectral Pack" },
		{ "SpectralPack.Name", "EAURACRONJungleCampType::SpectralPack" },
		{ "SpectralPack.ToolTip", "REGULAR CAMPS" },
		{ "StoneGuardians.Comment", "// Wolves equivalent\n" },
		{ "StoneGuardians.DisplayName", "Stone Guardians" },
		{ "StoneGuardians.Name", "EAURACRONJungleCampType::StoneGuardians" },
		{ "StoneGuardians.ToolTip", "Wolves equivalent" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de jungle camps baseados no LoL" },
#endif
		{ "VoidRaptors.Comment", "// Gromp equivalent\n" },
		{ "VoidRaptors.DisplayName", "Void Raptors" },
		{ "VoidRaptors.Name", "EAURACRONJungleCampType::VoidRaptors" },
		{ "VoidRaptors.ToolTip", "Gromp equivalent" },
		{ "WindSpirits.Comment", "// Prismal toad camp\n" },
		{ "WindSpirits.DisplayName", "Wind Spirits" },
		{ "WindSpirits.Name", "EAURACRONJungleCampType::WindSpirits" },
		{ "WindSpirits.ToolTip", "Prismal toad camp" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONJungleCampType::RadiantEssence", (int64)EAURACRONJungleCampType::RadiantEssence },
		{ "EAURACRONJungleCampType::ChaosEssence", (int64)EAURACRONJungleCampType::ChaosEssence },
		{ "EAURACRONJungleCampType::SpectralPack", (int64)EAURACRONJungleCampType::SpectralPack },
		{ "EAURACRONJungleCampType::EtherealGrove", (int64)EAURACRONJungleCampType::EtherealGrove },
		{ "EAURACRONJungleCampType::VoidRaptors", (int64)EAURACRONJungleCampType::VoidRaptors },
		{ "EAURACRONJungleCampType::CrystalWolves", (int64)EAURACRONJungleCampType::CrystalWolves },
		{ "EAURACRONJungleCampType::StoneGuardians", (int64)EAURACRONJungleCampType::StoneGuardians },
		{ "EAURACRONJungleCampType::PrismalToad", (int64)EAURACRONJungleCampType::PrismalToad },
		{ "EAURACRONJungleCampType::WindSpirits", (int64)EAURACRONJungleCampType::WindSpirits },
		{ "EAURACRONJungleCampType::FluxCrawler", (int64)EAURACRONJungleCampType::FluxCrawler },
		{ "EAURACRONJungleCampType::PrismalDragon", (int64)EAURACRONJungleCampType::PrismalDragon },
		{ "EAURACRONJungleCampType::AncientGuardian", (int64)EAURACRONJungleCampType::AncientGuardian },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONJungleCampType",
	"EAURACRONJungleCampType",
	Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONJungleCampType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONJungleCampType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONJungleCampType.InnerSingleton;
}
// ********** End Enum EAURACRONJungleCampType *****************************************************

// ********** Begin Enum EAURACRONJungleAdaptiveBehavior *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONJungleAdaptiveBehavior;
static UEnum* EAURACRONJungleAdaptiveBehavior_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONJungleAdaptiveBehavior.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONJungleAdaptiveBehavior.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONJungleAdaptiveBehavior, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONJungleAdaptiveBehavior"));
	}
	return Z_Registration_Info_UEnum_EAURACRONJungleAdaptiveBehavior.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONJungleAdaptiveBehavior>()
{
	return EAURACRONJungleAdaptiveBehavior_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONJungleAdaptiveBehavior_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aggressive.Comment", "// Mais resistente, menos dano\n" },
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "EAURACRONJungleAdaptiveBehavior::Aggressive" },
		{ "Aggressive.ToolTip", "Mais resistente, menos dano" },
		{ "BlueprintType", "true" },
		{ "Challenging.Comment", "// Comportamento otimizado para efici\xc3\xaancia\n" },
		{ "Challenging.DisplayName", "Challenging" },
		{ "Challenging.Name", "EAURACRONJungleAdaptiveBehavior::Challenging" },
		{ "Challenging.ToolTip", "Comportamento otimizado para efici\xc3\xaancia" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\xa7\xc3\xa3o para definir os comportamentos adaptativos dos camps\n */" },
#endif
		{ "Defensive.Comment", "// Comportamento padr\xc3\xa3o\n" },
		{ "Defensive.DisplayName", "Defensive" },
		{ "Defensive.Name", "EAURACRONJungleAdaptiveBehavior::Defensive" },
		{ "Defensive.ToolTip", "Comportamento padr\xc3\xa3o" },
		{ "Efficient.Comment", "// Comportamento estrat\xc3\xa9gico e t\xc3\xa1tico\n" },
		{ "Efficient.DisplayName", "Efficient" },
		{ "Efficient.Name", "EAURACRONJungleAdaptiveBehavior::Efficient" },
		{ "Efficient.ToolTip", "Comportamento estrat\xc3\xa9gico e t\xc3\xa1tico" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
		{ "Rewarding.Comment", "// Mais dano, menos resistente\n" },
		{ "Rewarding.DisplayName", "Rewarding" },
		{ "Rewarding.Name", "EAURACRONJungleAdaptiveBehavior::Rewarding" },
		{ "Rewarding.ToolTip", "Mais dano, menos resistente" },
		{ "Standard.DisplayName", "Standard" },
		{ "Standard.Name", "EAURACRONJungleAdaptiveBehavior::Standard" },
		{ "Strategic.Comment", "// Mais recompensas\n" },
		{ "Strategic.DisplayName", "Strategic" },
		{ "Strategic.Name", "EAURACRONJungleAdaptiveBehavior::Strategic" },
		{ "Strategic.ToolTip", "Mais recompensas" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\xa7\xc3\xa3o para definir os comportamentos adaptativos dos camps" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONJungleAdaptiveBehavior::Standard", (int64)EAURACRONJungleAdaptiveBehavior::Standard },
		{ "EAURACRONJungleAdaptiveBehavior::Defensive", (int64)EAURACRONJungleAdaptiveBehavior::Defensive },
		{ "EAURACRONJungleAdaptiveBehavior::Aggressive", (int64)EAURACRONJungleAdaptiveBehavior::Aggressive },
		{ "EAURACRONJungleAdaptiveBehavior::Rewarding", (int64)EAURACRONJungleAdaptiveBehavior::Rewarding },
		{ "EAURACRONJungleAdaptiveBehavior::Strategic", (int64)EAURACRONJungleAdaptiveBehavior::Strategic },
		{ "EAURACRONJungleAdaptiveBehavior::Efficient", (int64)EAURACRONJungleAdaptiveBehavior::Efficient },
		{ "EAURACRONJungleAdaptiveBehavior::Challenging", (int64)EAURACRONJungleAdaptiveBehavior::Challenging },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONJungleAdaptiveBehavior_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONJungleAdaptiveBehavior",
	"EAURACRONJungleAdaptiveBehavior",
	Z_Construct_UEnum_AURACRON_EAURACRONJungleAdaptiveBehavior_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONJungleAdaptiveBehavior_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONJungleAdaptiveBehavior_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONJungleAdaptiveBehavior_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONJungleAdaptiveBehavior()
{
	if (!Z_Registration_Info_UEnum_EAURACRONJungleAdaptiveBehavior.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONJungleAdaptiveBehavior.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONJungleAdaptiveBehavior_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONJungleAdaptiveBehavior.InnerSingleton;
}
// ********** End Enum EAURACRONJungleAdaptiveBehavior *********************************************

// ********** Begin Enum EAURACRONJungleStrategy ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONJungleStrategy;
static UEnum* EAURACRONJungleStrategy_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONJungleStrategy.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONJungleStrategy.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONJungleStrategy, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONJungleStrategy"));
	}
	return Z_Registration_Info_UEnum_EAURACRONJungleStrategy.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONJungleStrategy>()
{
	return EAURACRONJungleStrategy_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONJungleStrategy_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aggressive.Comment", "// Estrat\xc3\xa9gia balanceada\n" },
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "EAURACRONJungleStrategy::Aggressive" },
		{ "Aggressive.ToolTip", "Estrat\xc3\xa9gia balanceada" },
		{ "Balanced.DisplayName", "Balanced" },
		{ "Balanced.Name", "EAURACRONJungleStrategy::Balanced" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\xa7\xc3\xa3o para definir as estrat\xc3\xa9gias de jungle previstas\n */" },
#endif
		{ "Farming.Comment", "// Foco em invas\xc3\xb5""es e ganks\n" },
		{ "Farming.DisplayName", "Farming" },
		{ "Farming.Name", "EAURACRONJungleStrategy::Farming" },
		{ "Farming.ToolTip", "Foco em invas\xc3\xb5""es e ganks" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
		{ "Objective.Comment", "// Foco em farming da jungle\n" },
		{ "Objective.DisplayName", "Objective" },
		{ "Objective.Name", "EAURACRONJungleStrategy::Objective" },
		{ "Objective.ToolTip", "Foco em farming da jungle" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\xa7\xc3\xa3o para definir as estrat\xc3\xa9gias de jungle previstas" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONJungleStrategy::Balanced", (int64)EAURACRONJungleStrategy::Balanced },
		{ "EAURACRONJungleStrategy::Aggressive", (int64)EAURACRONJungleStrategy::Aggressive },
		{ "EAURACRONJungleStrategy::Farming", (int64)EAURACRONJungleStrategy::Farming },
		{ "EAURACRONJungleStrategy::Objective", (int64)EAURACRONJungleStrategy::Objective },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONJungleStrategy_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONJungleStrategy",
	"EAURACRONJungleStrategy",
	Z_Construct_UEnum_AURACRON_EAURACRONJungleStrategy_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONJungleStrategy_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONJungleStrategy_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONJungleStrategy_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONJungleStrategy()
{
	if (!Z_Registration_Info_UEnum_EAURACRONJungleStrategy.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONJungleStrategy.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONJungleStrategy_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONJungleStrategy.InnerSingleton;
}
// ********** End Enum EAURACRONJungleStrategy *****************************************************

// ********** Begin ScriptStruct FAURACRONJungleAdaptiveData ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveData;
class UScriptStruct* FAURACRONJungleAdaptiveData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONJungleAdaptiveData"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar dados de adapta\xc3\xa7\xc3\xa3o da IA\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar dados de adapta\xc3\xa7\xc3\xa3o da IA" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerProfile_MetaData[] = {
		{ "Category", "AURACRONJungleAdaptiveData" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Perfil atual do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Perfil atual do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptiveBehavior_MetaData[] = {
		{ "Category", "AURACRONJungleAdaptiveData" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Comportamento adaptativo atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Comportamento adaptativo atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredictedStrategy_MetaData[] = {
		{ "Category", "AURACRONJungleAdaptiveData" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estrat\xc3\xa9gia prevista baseada na composi\xc3\xa7\xc3\xa3o da equipe */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrat\xc3\xa9gia prevista baseada na composi\xc3\xa7\xc3\xa3o da equipe" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptiveDifficulty_MetaData[] = {
		{ "Category", "AURACRONJungleAdaptiveData" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xadvel de dificuldade adaptativa (0.0-2.0) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xadvel de dificuldade adaptativa (0.0-2.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CampInteractionCount_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Hist\xc3\xb3rico de intera\xc3\xa7\xc3\xb5""es do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Hist\xc3\xb3rico de intera\xc3\xa7\xc3\xb5""es do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageClearTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo m\xc3\xa9""dio para limpar camps */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo m\xc3\xa9""dio para limpar camps" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreferredClearPath_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Padr\xc3\xa3o de rota do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Padr\xc3\xa3o de rota do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InvasionCount_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Contador de invas\xc3\xb5""es de jungle */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Contador de invas\xc3\xb5""es de jungle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectivesSecured_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Contador de objetivos tomados */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Contador de objetivos tomados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeSinceLastAdaptation_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo desde a \xc3\xbaltima adapta\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo desde a \xc3\xbaltima adapta\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_PlayerProfile_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PlayerProfile;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AdaptiveBehavior_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AdaptiveBehavior;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PredictedStrategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PredictedStrategy;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptiveDifficulty;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CampInteractionCount_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CampInteractionCount_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CampInteractionCount_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CampInteractionCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageClearTime_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AverageClearTime_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AverageClearTime_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_AverageClearTime;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PreferredClearPath_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PreferredClearPath_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PreferredClearPath;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InvasionCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectivesSecured;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeSinceLastAdaptation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONJungleAdaptiveData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_PlayerProfile_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_PlayerProfile = { "PlayerProfile", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleAdaptiveData, PlayerProfile), Z_Construct_UEnum_AURACRON_EAURACRONJunglePlayerProfile, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerProfile_MetaData), NewProp_PlayerProfile_MetaData) }; // 3394276117
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_AdaptiveBehavior_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_AdaptiveBehavior = { "AdaptiveBehavior", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleAdaptiveData, AdaptiveBehavior), Z_Construct_UEnum_AURACRON_EAURACRONJungleAdaptiveBehavior, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptiveBehavior_MetaData), NewProp_AdaptiveBehavior_MetaData) }; // 1587606717
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_PredictedStrategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_PredictedStrategy = { "PredictedStrategy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleAdaptiveData, PredictedStrategy), Z_Construct_UEnum_AURACRON_EAURACRONJungleStrategy, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredictedStrategy_MetaData), NewProp_PredictedStrategy_MetaData) }; // 1792380333
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_AdaptiveDifficulty = { "AdaptiveDifficulty", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleAdaptiveData, AdaptiveDifficulty), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptiveDifficulty_MetaData), NewProp_AdaptiveDifficulty_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_CampInteractionCount_ValueProp = { "CampInteractionCount", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_CampInteractionCount_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_CampInteractionCount_Key_KeyProp = { "CampInteractionCount_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType, METADATA_PARAMS(0, nullptr) }; // 1652231832
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_CampInteractionCount = { "CampInteractionCount", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleAdaptiveData, CampInteractionCount), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CampInteractionCount_MetaData), NewProp_CampInteractionCount_MetaData) }; // 1652231832
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_AverageClearTime_ValueProp = { "AverageClearTime", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_AverageClearTime_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_AverageClearTime_Key_KeyProp = { "AverageClearTime_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType, METADATA_PARAMS(0, nullptr) }; // 1652231832
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_AverageClearTime = { "AverageClearTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleAdaptiveData, AverageClearTime), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageClearTime_MetaData), NewProp_AverageClearTime_MetaData) }; // 1652231832
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_PreferredClearPath_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_PreferredClearPath_Inner = { "PreferredClearPath", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType, METADATA_PARAMS(0, nullptr) }; // 1652231832
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_PreferredClearPath = { "PreferredClearPath", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleAdaptiveData, PreferredClearPath), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreferredClearPath_MetaData), NewProp_PreferredClearPath_MetaData) }; // 1652231832
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_InvasionCount = { "InvasionCount", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleAdaptiveData, InvasionCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InvasionCount_MetaData), NewProp_InvasionCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_ObjectivesSecured = { "ObjectivesSecured", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleAdaptiveData, ObjectivesSecured), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectivesSecured_MetaData), NewProp_ObjectivesSecured_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_TimeSinceLastAdaptation = { "TimeSinceLastAdaptation", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleAdaptiveData, TimeSinceLastAdaptation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeSinceLastAdaptation_MetaData), NewProp_TimeSinceLastAdaptation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_PlayerProfile_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_PlayerProfile,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_AdaptiveBehavior_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_AdaptiveBehavior,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_PredictedStrategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_PredictedStrategy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_AdaptiveDifficulty,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_CampInteractionCount_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_CampInteractionCount_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_CampInteractionCount_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_CampInteractionCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_AverageClearTime_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_AverageClearTime_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_AverageClearTime_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_AverageClearTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_PreferredClearPath_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_PreferredClearPath_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_PreferredClearPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_InvasionCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_ObjectivesSecured,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewProp_TimeSinceLastAdaptation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONJungleAdaptiveData",
	Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::PropPointers),
	sizeof(FAURACRONJungleAdaptiveData),
	alignof(FAURACRONJungleAdaptiveData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveData.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveData.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONJungleAdaptiveData *****************************************

// ********** Begin ScriptStruct FAURACRONMeshComponentArray ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray;
class UScriptStruct* FAURACRONMeshComponentArray::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONMeshComponentArray, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONMeshComponentArray"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura wrapper para array de componentes de mesh (resolve problema UHT com TMap<Enum, TArray<Type>>)\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura wrapper para array de componentes de mesh (resolve problema UHT com TMap<Enum, TArray<Type>>)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponents_MetaData[] = {
		{ "Category", "AURACRONMeshComponentArray" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MeshComponents;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONMeshComponentArray>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::NewProp_MeshComponents_Inner = { "MeshComponents", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::NewProp_MeshComponents = { "MeshComponents", nullptr, (EPropertyFlags)0x001000800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMeshComponentArray, MeshComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponents_MetaData), NewProp_MeshComponents_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::NewProp_MeshComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::NewProp_MeshComponents,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONMeshComponentArray",
	Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::PropPointers),
	sizeof(FAURACRONMeshComponentArray),
	alignof(FAURACRONMeshComponentArray),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONMeshComponentArray()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONMeshComponentArray *****************************************

// ********** Begin ScriptStruct FAURACRONJungleAdaptiveSystemConfig *******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig;
class UScriptStruct* FAURACRONJungleAdaptiveSystemConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONJungleAdaptiveSystemConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xa3o do sistema adaptativo do jungle\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o do sistema adaptativo do jungle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationInterval_MetaData[] = {
		{ "Category", "Adaptive System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo de adapta\xc3\xa7\xc3\xa3o em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de adapta\xc3\xa7\xc3\xa3o em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InvasionThreshold_MetaData[] = {
		{ "Category", "Adaptive System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Limite de invas\xc3\xb5""es para trigger de adapta\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limite de invas\xc3\xb5""es para trigger de adapta\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveThreshold_MetaData[] = {
		{ "Category", "Adaptive System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Limite de objetivos para trigger de adapta\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limite de objetivos para trigger de adapta\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDifficultyMultiplier_MetaData[] = {
		{ "Category", "Adaptive System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador m\xc3\xa1ximo de dificuldade */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador m\xc3\xa1ximo de dificuldade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinDifficultyMultiplier_MetaData[] = {
		{ "Category", "Adaptive System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador m\xc3\xadnimo de dificuldade */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador m\xc3\xadnimo de dificuldade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptationInterval;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InvasionThreshold;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDifficultyMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDifficultyMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONJungleAdaptiveSystemConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::NewProp_AdaptationInterval = { "AdaptationInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleAdaptiveSystemConfig, AdaptationInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationInterval_MetaData), NewProp_AdaptationInterval_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::NewProp_InvasionThreshold = { "InvasionThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleAdaptiveSystemConfig, InvasionThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InvasionThreshold_MetaData), NewProp_InvasionThreshold_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::NewProp_ObjectiveThreshold = { "ObjectiveThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleAdaptiveSystemConfig, ObjectiveThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveThreshold_MetaData), NewProp_ObjectiveThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::NewProp_MaxDifficultyMultiplier = { "MaxDifficultyMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleAdaptiveSystemConfig, MaxDifficultyMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDifficultyMultiplier_MetaData), NewProp_MaxDifficultyMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::NewProp_MinDifficultyMultiplier = { "MinDifficultyMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleAdaptiveSystemConfig, MinDifficultyMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinDifficultyMultiplier_MetaData), NewProp_MinDifficultyMultiplier_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::NewProp_AdaptationInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::NewProp_InvasionThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::NewProp_ObjectiveThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::NewProp_MaxDifficultyMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::NewProp_MinDifficultyMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONJungleAdaptiveSystemConfig",
	Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::PropPointers),
	sizeof(FAURACRONJungleAdaptiveSystemConfig),
	alignof(FAURACRONJungleAdaptiveSystemConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONJungleAdaptiveSystemConfig *********************************

// ********** Begin ScriptStruct FAURACRONJungleCampInfo *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo;
class UScriptStruct* FAURACRONJungleCampInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONJungleCampInfo, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONJungleCampInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Informa\xc3\xa7\xc3\xb5""es de um jungle camp\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\xa7\xc3\xb5""es de um jungle camp" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CampType_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do camp */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do camp" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositionsByEnvironment_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o do camp para cada ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o do camp para cada ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CampRadius_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio do camp */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio do camp" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RespawnTime_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de respawn em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de respawn em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsBuffCamp_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se \xc3\xa9 um buff camp */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se \xc3\xa9 um buff camp" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DifficultyLevel_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xadvel de dificuldade */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xadvel de dificuldade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rewards_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensas do camp */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensas do camp" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o camp est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o camp est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeUntilRespawn_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo at\xc3\xa9 pr\xc3\xb3ximo respawn */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo at\xc3\xa9 pr\xc3\xb3ximo respawn" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapSide_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lado do mapa (0 = Team 1, 1 = Team 2, 2 = Neutro) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lado do mapa (0 = Team 1, 1 = Team 2, 2 = Neutro)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentBehavior_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Comportamento adaptativo atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Comportamento adaptativo atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptiveDamageMultiplier_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de dano adaptativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de dano adaptativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptiveHealthMultiplier_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de sa\xc3\xba""de adaptativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de sa\xc3\xba""de adaptativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptiveRewardMultiplier_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de recompensa adaptativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de recompensa adaptativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageDefeatTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo m\xc3\xa9""dio para ser derrotado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo m\xc3\xa9""dio para ser derrotado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefeatCount_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Contagem de derrotas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Contagem de derrotas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InvasionsSuffered_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Contagem de invas\xc3\xb5""es sofridas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Contagem de invas\xc3\xb5""es sofridas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationPriority_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade para adapta\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade para adapta\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CampType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CampType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PositionsByEnvironment_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PositionsByEnvironment_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PositionsByEnvironment_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PositionsByEnvironment;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CampRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RespawnTime;
	static void NewProp_bIsBuffCamp_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsBuffCamp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DifficultyLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Rewards_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Rewards_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Rewards;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeUntilRespawn;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MapSide;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentBehavior_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentBehavior;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptiveDamageMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptiveHealthMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptiveRewardMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageDefeatTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DefeatCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InvasionsSuffered;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptationPriority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONJungleCampInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CampType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CampType = { "CampType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, CampType), Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CampType_MetaData), NewProp_CampType_MetaData) }; // 1652231832
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment_ValueProp = { "PositionsByEnvironment", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp = { "PositionsByEnvironment_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2161956974
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment = { "PositionsByEnvironment", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, PositionsByEnvironment), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositionsByEnvironment_MetaData), NewProp_PositionsByEnvironment_MetaData) }; // 2161956974
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CampRadius = { "CampRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, CampRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CampRadius_MetaData), NewProp_CampRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_RespawnTime = { "RespawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, RespawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RespawnTime_MetaData), NewProp_RespawnTime_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsBuffCamp_SetBit(void* Obj)
{
	((FAURACRONJungleCampInfo*)Obj)->bIsBuffCamp = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsBuffCamp = { "bIsBuffCamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONJungleCampInfo), &Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsBuffCamp_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsBuffCamp_MetaData), NewProp_bIsBuffCamp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_DifficultyLevel = { "DifficultyLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, DifficultyLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DifficultyLevel_MetaData), NewProp_DifficultyLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_Rewards_ValueProp = { "Rewards", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_Rewards_Key_KeyProp = { "Rewards_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_Rewards = { "Rewards", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, Rewards), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rewards_MetaData), NewProp_Rewards_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAURACRONJungleCampInfo*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONJungleCampInfo), &Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_TimeUntilRespawn = { "TimeUntilRespawn", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, TimeUntilRespawn), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeUntilRespawn_MetaData), NewProp_TimeUntilRespawn_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_MapSide = { "MapSide", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, MapSide), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapSide_MetaData), NewProp_MapSide_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CurrentBehavior_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CurrentBehavior = { "CurrentBehavior", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, CurrentBehavior), Z_Construct_UEnum_AURACRON_EAURACRONJungleAdaptiveBehavior, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentBehavior_MetaData), NewProp_CurrentBehavior_MetaData) }; // 1587606717
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_AdaptiveDamageMultiplier = { "AdaptiveDamageMultiplier", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, AdaptiveDamageMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptiveDamageMultiplier_MetaData), NewProp_AdaptiveDamageMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_AdaptiveHealthMultiplier = { "AdaptiveHealthMultiplier", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, AdaptiveHealthMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptiveHealthMultiplier_MetaData), NewProp_AdaptiveHealthMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_AdaptiveRewardMultiplier = { "AdaptiveRewardMultiplier", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, AdaptiveRewardMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptiveRewardMultiplier_MetaData), NewProp_AdaptiveRewardMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_AverageDefeatTime = { "AverageDefeatTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, AverageDefeatTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageDefeatTime_MetaData), NewProp_AverageDefeatTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_DefeatCount = { "DefeatCount", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, DefeatCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefeatCount_MetaData), NewProp_DefeatCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_InvasionsSuffered = { "InvasionsSuffered", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, InvasionsSuffered), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InvasionsSuffered_MetaData), NewProp_InvasionsSuffered_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_AdaptationPriority = { "AdaptationPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, AdaptationPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationPriority_MetaData), NewProp_AdaptationPriority_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CampType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CampType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CampRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_RespawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsBuffCamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_DifficultyLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_Rewards_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_Rewards_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_Rewards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_TimeUntilRespawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_MapSide,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CurrentBehavior_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CurrentBehavior,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_AdaptiveDamageMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_AdaptiveHealthMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_AdaptiveRewardMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_AverageDefeatTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_DefeatCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_InvasionsSuffered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_AdaptationPriority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONJungleCampInfo",
	Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::PropPointers),
	sizeof(FAURACRONJungleCampInfo),
	alignof(FAURACRONJungleCampInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONJungleCampInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONJungleCampInfo *********************************************

// ********** Begin Class AAURACRONPCGJungleSystem Function AdaptCampBehaviors *********************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_AdaptCampBehaviors_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Adaptar comportamento dos camps */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adaptar comportamento dos camps" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_AdaptCampBehaviors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "AdaptCampBehaviors", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_AdaptCampBehaviors_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_AdaptCampBehaviors_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_AdaptCampBehaviors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_AdaptCampBehaviors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execAdaptCampBehaviors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AdaptCampBehaviors();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function AdaptCampBehaviors ***********************

// ********** Begin Class AAURACRONPCGJungleSystem Function ClearCamp ******************************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics
{
	struct AURACRONPCGJungleSystem_eventClearCamp_Parms
	{
		int32 CampIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Limpar camp (quando morto) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpar camp (quando morto)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CampIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::NewProp_CampIndex = { "CampIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventClearCamp_Parms, CampIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::NewProp_CampIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "ClearCamp", Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::AURACRONPCGJungleSystem_eventClearCamp_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::AURACRONPCGJungleSystem_eventClearCamp_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execClearCamp)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_CampIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearCamp(Z_Param_CampIndex);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function ClearCamp ********************************

// ********** Begin Class AAURACRONPCGJungleSystem Function GenerateCampsForEnvironment ************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics
{
	struct AURACRONPCGJungleSystem_eventGenerateCampsForEnvironment_Parms
	{
		EAURACRONEnvironmentType Environment;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar camps para ambiente espec\xc3\xad""fico */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar camps para ambiente espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGenerateCampsForEnvironment_Parms, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2161956974
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::NewProp_Environment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "GenerateCampsForEnvironment", Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::AURACRONPCGJungleSystem_eventGenerateCampsForEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::AURACRONPCGJungleSystem_eventGenerateCampsForEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execGenerateCampsForEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_Environment);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateCampsForEnvironment(EAURACRONEnvironmentType(Z_Param_Environment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function GenerateCampsForEnvironment **************

// ********** Begin Class AAURACRONPCGJungleSystem Function GenerateJungleCamps ********************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateJungleCamps_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar todos os jungle camps */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar todos os jungle camps" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateJungleCamps_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "GenerateJungleCamps", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateJungleCamps_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateJungleCamps_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateJungleCamps()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateJungleCamps_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execGenerateJungleCamps)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateJungleCamps();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function GenerateJungleCamps **********************

// ********** Begin Class AAURACRONPCGJungleSystem Function GetAdaptiveData ************************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAdaptiveData_Statics
{
	struct AURACRONPCGJungleSystem_eventGetAdaptiveData_Parms
	{
		FAURACRONJungleAdaptiveData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter dados adaptativos atuais */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter dados adaptativos atuais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAdaptiveData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetAdaptiveData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData, METADATA_PARAMS(0, nullptr) }; // 1676662704
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAdaptiveData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAdaptiveData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAdaptiveData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAdaptiveData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "GetAdaptiveData", Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAdaptiveData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAdaptiveData_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAdaptiveData_Statics::AURACRONPCGJungleSystem_eventGetAdaptiveData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAdaptiveData_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAdaptiveData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAdaptiveData_Statics::AURACRONPCGJungleSystem_eventGetAdaptiveData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAdaptiveData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAdaptiveData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execGetAdaptiveData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAURACRONJungleAdaptiveData*)Z_Param__Result=P_THIS->GetAdaptiveData();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function GetAdaptiveData **************************

// ********** Begin Class AAURACRONPCGJungleSystem Function GetAllCamps ****************************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics
{
	struct AURACRONPCGJungleSystem_eventGetAllCamps_Parms
	{
		TArray<FAURACRONJungleCampInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter informa\xc3\xa7\xc3\xb5""es de todos os camps */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter informa\xc3\xa7\xc3\xb5""es de todos os camps" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONJungleCampInfo, METADATA_PARAMS(0, nullptr) }; // 822983728
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetAllCamps_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 822983728
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "GetAllCamps", Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::AURACRONPCGJungleSystem_eventGetAllCamps_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::AURACRONPCGJungleSystem_eventGetAllCamps_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execGetAllCamps)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONJungleCampInfo>*)Z_Param__Result=P_THIS->GetAllCamps();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function GetAllCamps ******************************

// ********** Begin Class AAURACRONPCGJungleSystem Function GetCampDifficultyMultiplier ************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier_Statics
{
	struct AURACRONPCGJungleSystem_eventGetCampDifficultyMultiplier_Parms
	{
		int32 CampIndex;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter multiplicador de dificuldade para camp */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter multiplicador de dificuldade para camp" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CampIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier_Statics::NewProp_CampIndex = { "CampIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetCampDifficultyMultiplier_Parms, CampIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetCampDifficultyMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier_Statics::NewProp_CampIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "GetCampDifficultyMultiplier", Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier_Statics::AURACRONPCGJungleSystem_eventGetCampDifficultyMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier_Statics::AURACRONPCGJungleSystem_eventGetCampDifficultyMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execGetCampDifficultyMultiplier)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_CampIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCampDifficultyMultiplier(Z_Param_CampIndex);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function GetCampDifficultyMultiplier **************

// ********** Begin Class AAURACRONPCGJungleSystem Function GetCampRewardMultiplier ****************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier_Statics
{
	struct AURACRONPCGJungleSystem_eventGetCampRewardMultiplier_Parms
	{
		int32 CampIndex;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter multiplicador de recompensa para camp */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter multiplicador de recompensa para camp" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CampIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier_Statics::NewProp_CampIndex = { "CampIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetCampRewardMultiplier_Parms, CampIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetCampRewardMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier_Statics::NewProp_CampIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "GetCampRewardMultiplier", Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier_Statics::AURACRONPCGJungleSystem_eventGetCampRewardMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier_Statics::AURACRONPCGJungleSystem_eventGetCampRewardMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execGetCampRewardMultiplier)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_CampIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCampRewardMultiplier(Z_Param_CampIndex);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function GetCampRewardMultiplier ******************

// ********** Begin Class AAURACRONPCGJungleSystem Function GetCampsBySide *************************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics
{
	struct AURACRONPCGJungleSystem_eventGetCampsBySide_Parms
	{
		int32 MapSide;
		TArray<FAURACRONJungleCampInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter camps por lado do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter camps por lado do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MapSide;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::NewProp_MapSide = { "MapSide", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetCampsBySide_Parms, MapSide), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONJungleCampInfo, METADATA_PARAMS(0, nullptr) }; // 822983728
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetCampsBySide_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 822983728
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::NewProp_MapSide,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "GetCampsBySide", Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::AURACRONPCGJungleSystem_eventGetCampsBySide_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::AURACRONPCGJungleSystem_eventGetCampsBySide_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execGetCampsBySide)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MapSide);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONJungleCampInfo>*)Z_Param__Result=P_THIS->GetCampsBySide(Z_Param_MapSide);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function GetCampsBySide ***************************

// ********** Begin Class AAURACRONPCGJungleSystem Function GetCampsByType *************************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics
{
	struct AURACRONPCGJungleSystem_eventGetCampsByType_Parms
	{
		EAURACRONJungleCampType CampType;
		TArray<FAURACRONJungleCampInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter camps por tipo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter camps por tipo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CampType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CampType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_CampType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_CampType = { "CampType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetCampsByType_Parms, CampType), Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType, METADATA_PARAMS(0, nullptr) }; // 1652231832
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONJungleCampInfo, METADATA_PARAMS(0, nullptr) }; // 822983728
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetCampsByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 822983728
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_CampType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_CampType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "GetCampsByType", Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::AURACRONPCGJungleSystem_eventGetCampsByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::AURACRONPCGJungleSystem_eventGetCampsByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execGetCampsByType)
{
	P_GET_ENUM(EAURACRONJungleCampType,Z_Param_CampType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONJungleCampInfo>*)Z_Param__Result=P_THIS->GetCampsByType(EAURACRONJungleCampType(Z_Param_CampType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function GetCampsByType ***************************

// ********** Begin Class AAURACRONPCGJungleSystem Function GetRecommendedBehaviorForCamp **********
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics
{
	struct AURACRONPCGJungleSystem_eventGetRecommendedBehaviorForCamp_Parms
	{
		int32 CampIndex;
		EAURACRONJungleAdaptiveBehavior ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter comportamento recomendado para camp */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter comportamento recomendado para camp" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CampIndex;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::NewProp_CampIndex = { "CampIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetRecommendedBehaviorForCamp_Parms, CampIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetRecommendedBehaviorForCamp_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONJungleAdaptiveBehavior, METADATA_PARAMS(0, nullptr) }; // 1587606717
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::NewProp_CampIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "GetRecommendedBehaviorForCamp", Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::AURACRONPCGJungleSystem_eventGetRecommendedBehaviorForCamp_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::AURACRONPCGJungleSystem_eventGetRecommendedBehaviorForCamp_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execGetRecommendedBehaviorForCamp)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_CampIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONJungleAdaptiveBehavior*)Z_Param__Result=P_THIS->GetRecommendedBehaviorForCamp(Z_Param_CampIndex);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function GetRecommendedBehaviorForCamp ************

// ********** Begin Class AAURACRONPCGJungleSystem Function IsCampAvailable ************************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics
{
	struct AURACRONPCGJungleSystem_eventIsCampAvailable_Parms
	{
		int32 CampIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se camp est\xc3\xa1 dispon\xc3\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se camp est\xc3\xa1 dispon\xc3\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CampIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::NewProp_CampIndex = { "CampIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventIsCampAvailable_Parms, CampIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGJungleSystem_eventIsCampAvailable_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGJungleSystem_eventIsCampAvailable_Parms), &Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::NewProp_CampIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "IsCampAvailable", Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::AURACRONPCGJungleSystem_eventIsCampAvailable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::AURACRONPCGJungleSystem_eventIsCampAvailable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execIsCampAvailable)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_CampIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsCampAvailable(Z_Param_CampIndex);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function IsCampAvailable **************************

// ********** Begin Class AAURACRONPCGJungleSystem Function RegisterCampInteraction ****************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction_Statics
{
	struct AURACRONPCGJungleSystem_eventRegisterCampInteraction_Parms
	{
		int32 CampIndex;
		float ClearTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Registrar intera\xc3\xa7\xc3\xa3o com camp */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar intera\xc3\xa7\xc3\xa3o com camp" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CampIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClearTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction_Statics::NewProp_CampIndex = { "CampIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventRegisterCampInteraction_Parms, CampIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction_Statics::NewProp_ClearTime = { "ClearTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventRegisterCampInteraction_Parms, ClearTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction_Statics::NewProp_CampIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction_Statics::NewProp_ClearTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "RegisterCampInteraction", Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction_Statics::AURACRONPCGJungleSystem_eventRegisterCampInteraction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction_Statics::AURACRONPCGJungleSystem_eventRegisterCampInteraction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execRegisterCampInteraction)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_CampIndex);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ClearTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterCampInteraction(Z_Param_CampIndex,Z_Param_ClearTime);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function RegisterCampInteraction ******************

// ********** Begin Class AAURACRONPCGJungleSystem Function RegisterJungleInvasion *****************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterJungleInvasion_Statics
{
	struct AURACRONPCGJungleSystem_eventRegisterJungleInvasion_Parms
	{
		int32 MapSide;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Registrar invas\xc3\xa3o de jungle */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar invas\xc3\xa3o de jungle" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MapSide;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterJungleInvasion_Statics::NewProp_MapSide = { "MapSide", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventRegisterJungleInvasion_Parms, MapSide), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterJungleInvasion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterJungleInvasion_Statics::NewProp_MapSide,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterJungleInvasion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterJungleInvasion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "RegisterJungleInvasion", Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterJungleInvasion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterJungleInvasion_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterJungleInvasion_Statics::AURACRONPCGJungleSystem_eventRegisterJungleInvasion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterJungleInvasion_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterJungleInvasion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterJungleInvasion_Statics::AURACRONPCGJungleSystem_eventRegisterJungleInvasion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterJungleInvasion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterJungleInvasion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execRegisterJungleInvasion)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MapSide);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterJungleInvasion(Z_Param_MapSide);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function RegisterJungleInvasion *******************

// ********** Begin Class AAURACRONPCGJungleSystem Function RegisterObjectiveSecured ***************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterObjectiveSecured_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Registrar objetivo tomado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar objetivo tomado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterObjectiveSecured_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "RegisterObjectiveSecured", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterObjectiveSecured_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterObjectiveSecured_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterObjectiveSecured()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterObjectiveSecured_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execRegisterObjectiveSecured)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterObjectiveSecured();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function RegisterObjectiveSecured *****************

// ********** Begin Class AAURACRONPCGJungleSystem Function ResetAdaptiveData **********************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_ResetAdaptiveData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Resetar dados adaptativos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resetar dados adaptativos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_ResetAdaptiveData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "ResetAdaptiveData", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_ResetAdaptiveData_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_ResetAdaptiveData_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_ResetAdaptiveData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_ResetAdaptiveData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execResetAdaptiveData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetAdaptiveData();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function ResetAdaptiveData ************************

// ********** Begin Class AAURACRONPCGJungleSystem Function SetAdaptiveDifficulty ******************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetAdaptiveDifficulty_Statics
{
	struct AURACRONPCGJungleSystem_eventSetAdaptiveDifficulty_Parms
	{
		float NewDifficulty;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir n\xc3\xadvel de dificuldade adaptativa global */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir n\xc3\xadvel de dificuldade adaptativa global" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewDifficulty;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetAdaptiveDifficulty_Statics::NewProp_NewDifficulty = { "NewDifficulty", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventSetAdaptiveDifficulty_Parms, NewDifficulty), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetAdaptiveDifficulty_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetAdaptiveDifficulty_Statics::NewProp_NewDifficulty,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetAdaptiveDifficulty_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetAdaptiveDifficulty_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "SetAdaptiveDifficulty", Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetAdaptiveDifficulty_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetAdaptiveDifficulty_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetAdaptiveDifficulty_Statics::AURACRONPCGJungleSystem_eventSetAdaptiveDifficulty_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetAdaptiveDifficulty_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetAdaptiveDifficulty_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetAdaptiveDifficulty_Statics::AURACRONPCGJungleSystem_eventSetAdaptiveDifficulty_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetAdaptiveDifficulty()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetAdaptiveDifficulty_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execSetAdaptiveDifficulty)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewDifficulty);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAdaptiveDifficulty(Z_Param_NewDifficulty);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function SetAdaptiveDifficulty ********************

// ********** Begin Class AAURACRONPCGJungleSystem Function SetPlayerProfile ***********************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile_Statics
{
	struct AURACRONPCGJungleSystem_eventSetPlayerProfile_Parms
	{
		EAURACRONJunglePlayerProfile NewProfile;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir perfil do jogador manualmente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir perfil do jogador manualmente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewProfile_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewProfile;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile_Statics::NewProp_NewProfile_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile_Statics::NewProp_NewProfile = { "NewProfile", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventSetPlayerProfile_Parms, NewProfile), Z_Construct_UEnum_AURACRON_EAURACRONJunglePlayerProfile, METADATA_PARAMS(0, nullptr) }; // 3394276117
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile_Statics::NewProp_NewProfile_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile_Statics::NewProp_NewProfile,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "SetPlayerProfile", Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile_Statics::AURACRONPCGJungleSystem_eventSetPlayerProfile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile_Statics::AURACRONPCGJungleSystem_eventSetPlayerProfile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execSetPlayerProfile)
{
	P_GET_ENUM(EAURACRONJunglePlayerProfile,Z_Param_NewProfile);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPlayerProfile(EAURACRONJunglePlayerProfile(Z_Param_NewProfile));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function SetPlayerProfile *************************

// ********** Begin Class AAURACRONPCGJungleSystem Function UpdateForEnvironment *******************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics
{
	struct AURACRONPCGJungleSystem_eventUpdateForEnvironment_Parms
	{
		EAURACRONEnvironmentType NewEnvironment;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar para novo ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar para novo ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewEnvironment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment = { "NewEnvironment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventUpdateForEnvironment_Parms, NewEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2161956974
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "UpdateForEnvironment", Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::AURACRONPCGJungleSystem_eventUpdateForEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::AURACRONPCGJungleSystem_eventUpdateForEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execUpdateForEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_NewEnvironment);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForEnvironment(EAURACRONEnvironmentType(Z_Param_NewEnvironment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function UpdateForEnvironment *********************

// ********** Begin Class AAURACRONPCGJungleSystem Function UpdateForMapPhase **********************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics
{
	struct AURACRONPCGJungleSystem_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar para nova fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar para nova fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::AURACRONPCGJungleSystem_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::AURACRONPCGJungleSystem_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function UpdateForMapPhase ************************

// ********** Begin Class AAURACRONPCGJungleSystem *************************************************
void AAURACRONPCGJungleSystem::StaticRegisterNativesAAURACRONPCGJungleSystem()
{
	UClass* Class = AAURACRONPCGJungleSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AdaptCampBehaviors", &AAURACRONPCGJungleSystem::execAdaptCampBehaviors },
		{ "ClearCamp", &AAURACRONPCGJungleSystem::execClearCamp },
		{ "GenerateCampsForEnvironment", &AAURACRONPCGJungleSystem::execGenerateCampsForEnvironment },
		{ "GenerateJungleCamps", &AAURACRONPCGJungleSystem::execGenerateJungleCamps },
		{ "GetAdaptiveData", &AAURACRONPCGJungleSystem::execGetAdaptiveData },
		{ "GetAllCamps", &AAURACRONPCGJungleSystem::execGetAllCamps },
		{ "GetCampDifficultyMultiplier", &AAURACRONPCGJungleSystem::execGetCampDifficultyMultiplier },
		{ "GetCampRewardMultiplier", &AAURACRONPCGJungleSystem::execGetCampRewardMultiplier },
		{ "GetCampsBySide", &AAURACRONPCGJungleSystem::execGetCampsBySide },
		{ "GetCampsByType", &AAURACRONPCGJungleSystem::execGetCampsByType },
		{ "GetRecommendedBehaviorForCamp", &AAURACRONPCGJungleSystem::execGetRecommendedBehaviorForCamp },
		{ "IsCampAvailable", &AAURACRONPCGJungleSystem::execIsCampAvailable },
		{ "RegisterCampInteraction", &AAURACRONPCGJungleSystem::execRegisterCampInteraction },
		{ "RegisterJungleInvasion", &AAURACRONPCGJungleSystem::execRegisterJungleInvasion },
		{ "RegisterObjectiveSecured", &AAURACRONPCGJungleSystem::execRegisterObjectiveSecured },
		{ "ResetAdaptiveData", &AAURACRONPCGJungleSystem::execResetAdaptiveData },
		{ "SetAdaptiveDifficulty", &AAURACRONPCGJungleSystem::execSetAdaptiveDifficulty },
		{ "SetPlayerProfile", &AAURACRONPCGJungleSystem::execSetPlayerProfile },
		{ "UpdateForEnvironment", &AAURACRONPCGJungleSystem::execUpdateForEnvironment },
		{ "UpdateForMapPhase", &AAURACRONPCGJungleSystem::execUpdateForMapPhase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGJungleSystem;
UClass* AAURACRONPCGJungleSystem::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGJungleSystem;
	if (!Z_Registration_Info_UClass_AAURACRONPCGJungleSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGJungleSystem"),
			Z_Registration_Info_UClass_AAURACRONPCGJungleSystem.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGJungleSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGJungleSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGJungleSystem_NoRegister()
{
	return AAURACRONPCGJungleSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Sistema de jungle para AURACRON baseado no layout do LoL\n * 12 camps sim\xc3\xa9tricos distribu\xc3\xad""dos entre as lanes\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGJungleSystem.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de jungle para AURACRON baseado no layout do LoL\n12 camps sim\xc3\xa9tricos distribu\xc3\xad""dos entre as lanes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAdaptiveSystemEnabled_MetaData[] = {
		{ "Category", "AURACRON|Jungle|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o sistema adaptativo est\xc3\xa1 habilitado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o sistema adaptativo est\xc3\xa1 habilitado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JungleCamps_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Informa\xc3\xa7\xc3\xb5""es de todos os jungle camps */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\xa7\xc3\xb5""es de todos os jungle camps" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CampMeshesByEnvironment_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes visuais dos camps por ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes visuais dos camps por ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CampCollisionComponents_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes de colis\xc3\xa3o dos camps */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de colis\xc3\xa3o dos camps" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoGenerate_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve gerar automaticamente no BeginPlay */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve gerar automaticamente no BeginPlay" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEnvironment_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ambiente atualmente ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ambiente atualmente ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptiveData_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dados adaptativos da jungle */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados adaptativos da jungle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationInterval_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo de adapta\xc3\xa7\xc3\xa3o em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de adapta\xc3\xa7\xc3\xa3o em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAdaptiveAI_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativar IA adaptativa? */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar IA adaptativa?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationSensitivity_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sensibilidade da adapta\xc3\xa7\xc3\xa3o (0.0-1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sensibilidade da adapta\xc3\xa7\xc3\xa3o (0.0-1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptiveDifficultyCurve_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Curva de dificuldade adaptativa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Curva de dificuldade adaptativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingConfiguration_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xa3o de streaming para World Partition */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o de streaming para World Partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssociatedDataLayer_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data Layer associada */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Layer associada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptiveSystemConfig_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xa3o do sistema adaptativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o do sistema adaptativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CampRespawnTimers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timers de respawn dos camps */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timers de respawn dos camps" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bAdaptiveSystemEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAdaptiveSystemEnabled;
	static const UECodeGen_Private::FStructPropertyParams NewProp_JungleCamps_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_JungleCamps;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CampMeshesByEnvironment_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CampMeshesByEnvironment_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CampMeshesByEnvironment_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CampMeshesByEnvironment;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CampCollisionComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CampCollisionComponents;
	static void NewProp_bAutoGenerate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoGenerate;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentEnvironment;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AdaptiveData;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptationInterval;
	static void NewProp_bEnableAdaptiveAI_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdaptiveAI;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptationSensitivity;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AdaptiveDifficultyCurve;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingConfiguration;
	static const UECodeGen_Private::FNamePropertyParams NewProp_AssociatedDataLayer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AdaptiveSystemConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CampRespawnTimers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CampRespawnTimers;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_AdaptCampBehaviors, "AdaptCampBehaviors" }, // 4007214069
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp, "ClearCamp" }, // 1168696491
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment, "GenerateCampsForEnvironment" }, // 3755653096
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateJungleCamps, "GenerateJungleCamps" }, // 3582977273
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAdaptiveData, "GetAdaptiveData" }, // 1487016472
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps, "GetAllCamps" }, // 3676100172
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampDifficultyMultiplier, "GetCampDifficultyMultiplier" }, // 3945640589
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampRewardMultiplier, "GetCampRewardMultiplier" }, // 1733447711
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide, "GetCampsBySide" }, // 4000595290
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType, "GetCampsByType" }, // 611905519
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetRecommendedBehaviorForCamp, "GetRecommendedBehaviorForCamp" }, // 1428276555
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable, "IsCampAvailable" }, // 833303014
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterCampInteraction, "RegisterCampInteraction" }, // 1645055522
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterJungleInvasion, "RegisterJungleInvasion" }, // 2513904213
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_RegisterObjectiveSecured, "RegisterObjectiveSecured" }, // 2179787533
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_ResetAdaptiveData, "ResetAdaptiveData" }, // 2751095922
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetAdaptiveDifficulty, "SetAdaptiveDifficulty" }, // 3113322384
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_SetPlayerProfile, "SetPlayerProfile" }, // 4280014057
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment, "UpdateForEnvironment" }, // 4086893566
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase, "UpdateForMapPhase" }, // 4276548270
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGJungleSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bAdaptiveSystemEnabled_SetBit(void* Obj)
{
	((AAURACRONPCGJungleSystem*)Obj)->bAdaptiveSystemEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bAdaptiveSystemEnabled = { "bAdaptiveSystemEnabled", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGJungleSystem), &Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bAdaptiveSystemEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAdaptiveSystemEnabled_MetaData), NewProp_bAdaptiveSystemEnabled_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_JungleCamps_Inner = { "JungleCamps", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONJungleCampInfo, METADATA_PARAMS(0, nullptr) }; // 822983728
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_JungleCamps = { "JungleCamps", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, JungleCamps), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JungleCamps_MetaData), NewProp_JungleCamps_MetaData) }; // 822983728
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment_ValueProp = { "CampMeshesByEnvironment", nullptr, (EPropertyFlags)0x0000008000020001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONMeshComponentArray, METADATA_PARAMS(0, nullptr) }; // 3350711660
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment_Key_KeyProp = { "CampMeshesByEnvironment_Key", nullptr, (EPropertyFlags)0x0000008000020001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2161956974
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment = { "CampMeshesByEnvironment", nullptr, (EPropertyFlags)0x0020088000020015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, CampMeshesByEnvironment), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CampMeshesByEnvironment_MetaData), NewProp_CampMeshesByEnvironment_MetaData) }; // 2161956974 3350711660
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampCollisionComponents_Inner = { "CampCollisionComponents", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampCollisionComponents = { "CampCollisionComponents", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, CampCollisionComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CampCollisionComponents_MetaData), NewProp_CampCollisionComponents_MetaData) };
void Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bAutoGenerate_SetBit(void* Obj)
{
	((AAURACRONPCGJungleSystem*)Obj)->bAutoGenerate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bAutoGenerate = { "bAutoGenerate", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGJungleSystem), &Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bAutoGenerate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoGenerate_MetaData), NewProp_bAutoGenerate_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentEnvironment = { "CurrentEnvironment", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, CurrentEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEnvironment_MetaData), NewProp_CurrentEnvironment_MetaData) }; // 2161956974
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_AdaptiveData = { "AdaptiveData", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, AdaptiveData), Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptiveData_MetaData), NewProp_AdaptiveData_MetaData) }; // 1676662704
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_AdaptationInterval = { "AdaptationInterval", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, AdaptationInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationInterval_MetaData), NewProp_AdaptationInterval_MetaData) };
void Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bEnableAdaptiveAI_SetBit(void* Obj)
{
	((AAURACRONPCGJungleSystem*)Obj)->bEnableAdaptiveAI = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bEnableAdaptiveAI = { "bEnableAdaptiveAI", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGJungleSystem), &Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bEnableAdaptiveAI_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAdaptiveAI_MetaData), NewProp_bEnableAdaptiveAI_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_AdaptationSensitivity = { "AdaptationSensitivity", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, AdaptationSensitivity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationSensitivity_MetaData), NewProp_AdaptationSensitivity_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_AdaptiveDifficultyCurve = { "AdaptiveDifficultyCurve", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, AdaptiveDifficultyCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptiveDifficultyCurve_MetaData), NewProp_AdaptiveDifficultyCurve_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_StreamingConfiguration = { "StreamingConfiguration", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, StreamingConfiguration), Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingConfiguration_MetaData), NewProp_StreamingConfiguration_MetaData) }; // 1435596736
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_AssociatedDataLayer = { "AssociatedDataLayer", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, AssociatedDataLayer), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssociatedDataLayer_MetaData), NewProp_AssociatedDataLayer_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_AdaptiveSystemConfig = { "AdaptiveSystemConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, AdaptiveSystemConfig), Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptiveSystemConfig_MetaData), NewProp_AdaptiveSystemConfig_MetaData) }; // 288120464
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampRespawnTimers_Inner = { "CampRespawnTimers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(0, nullptr) }; // 3834150579
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampRespawnTimers = { "CampRespawnTimers", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, CampRespawnTimers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CampRespawnTimers_MetaData), NewProp_CampRespawnTimers_MetaData) }; // 3834150579
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentMapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentMapPhase = { "CurrentMapPhase", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, CurrentMapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapPhase_MetaData), NewProp_CurrentMapPhase_MetaData) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bAdaptiveSystemEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_JungleCamps_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_JungleCamps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampCollisionComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampCollisionComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bAutoGenerate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_AdaptiveData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_AdaptationInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bEnableAdaptiveAI,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_AdaptationSensitivity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_AdaptiveDifficultyCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_StreamingConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_AssociatedDataLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_AdaptiveSystemConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampRespawnTimers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampRespawnTimers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentMapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentMapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::ClassParams = {
	&AAURACRONPCGJungleSystem::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGJungleSystem()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGJungleSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGJungleSystem.OuterSingleton, Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGJungleSystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGJungleSystem);
AAURACRONPCGJungleSystem::~AAURACRONPCGJungleSystem() {}
// ********** End Class AAURACRONPCGJungleSystem ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAURACRONJunglePlayerProfile_StaticEnum, TEXT("EAURACRONJunglePlayerProfile"), &Z_Registration_Info_UEnum_EAURACRONJunglePlayerProfile, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3394276117U) },
		{ EAURACRONJungleCampType_StaticEnum, TEXT("EAURACRONJungleCampType"), &Z_Registration_Info_UEnum_EAURACRONJungleCampType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1652231832U) },
		{ EAURACRONJungleAdaptiveBehavior_StaticEnum, TEXT("EAURACRONJungleAdaptiveBehavior"), &Z_Registration_Info_UEnum_EAURACRONJungleAdaptiveBehavior, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1587606717U) },
		{ EAURACRONJungleStrategy_StaticEnum, TEXT("EAURACRONJungleStrategy"), &Z_Registration_Info_UEnum_EAURACRONJungleStrategy, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1792380333U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONJungleAdaptiveData::StaticStruct, Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveData_Statics::NewStructOps, TEXT("AURACRONJungleAdaptiveData"), &Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONJungleAdaptiveData), 1676662704U) },
		{ FAURACRONMeshComponentArray::StaticStruct, Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::NewStructOps, TEXT("AURACRONMeshComponentArray"), &Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONMeshComponentArray), 3350711660U) },
		{ FAURACRONJungleAdaptiveSystemConfig::StaticStruct, Z_Construct_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig_Statics::NewStructOps, TEXT("AURACRONJungleAdaptiveSystemConfig"), &Z_Registration_Info_UScriptStruct_FAURACRONJungleAdaptiveSystemConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONJungleAdaptiveSystemConfig), 288120464U) },
		{ FAURACRONJungleCampInfo::StaticStruct, Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewStructOps, TEXT("AURACRONJungleCampInfo"), &Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONJungleCampInfo), 822983728U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGJungleSystem, AAURACRONPCGJungleSystem::StaticClass, TEXT("AAURACRONPCGJungleSystem"), &Z_Registration_Info_UClass_AAURACRONPCGJungleSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGJungleSystem), 2347283135U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_2950187859(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
