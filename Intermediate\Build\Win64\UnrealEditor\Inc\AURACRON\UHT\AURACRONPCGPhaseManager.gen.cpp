// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGPhaseManager.h"
#include "Engine/TimerHandle.h"
#include "PCG/AURACRONPCGUtility.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGPhaseManager() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnvironmentManager_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGJungleSystem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGLaneSystem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGObjectiveSystem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPhaseManager();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPhaseManager_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGActorReferences();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPhaseSettings();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTimerHandle();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAURACRONPhaseSettings ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONPhaseSettings;
class UScriptStruct* FAURACRONPhaseSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPhaseSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONPhaseSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONPhaseSettings, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONPhaseSettings"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPhaseSettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xb5""es consolidadas de uma fase do mapa\n * CONSOLIDADO: Combina FAURACRONMapPhaseConfig e FAURACRONPhaseSettings\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es consolidadas de uma fase do mapa\nCONSOLIDADO: Combina FAURACRONMapPhaseConfig e FAURACRONPhaseSettings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Phase_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseName_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome da fase */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome da fase" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\xa7\xc3\xa3o da fase */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\xa7\xc3\xa3o da fase" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseDuration_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
		{ "ClampMax", "600.0" },
		{ "ClampMin", "30.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o da fase em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o da fase em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseStartTime_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de in\xc3\xad""cio da fase (desde o in\xc3\xad""cio da partida) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de in\xc3\xad""cio da fase (desde o in\xc3\xad""cio da partida)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapScaleFactor_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fator de escala do mapa (1.0 = tamanho normal, 0.8 = 20% menor) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fator de escala do mapa (1.0 = tamanho normal, 0.8 = 20% menor)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentSpeedMultiplier_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de velocidade dos ambientes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de velocidade dos ambientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseIntensity_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade global da fase */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade global da fase" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentActivityScale_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala de atividade dos ambientes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala de atividade dos ambientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionSpeed_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade das transi\xc3\xa7\xc3\xb5""es */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade das transi\xc3\xa7\xc3\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseColor_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor dominante da fase */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor dominante da fase" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JungleRespawnMultiplier_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de respawn dos jungle camps */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de respawn dos jungle camps" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveRespawnMultiplier_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de respawn dos objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de respawn dos objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveHealthMultiplier_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de HP dos objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de HP dos objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardMultiplier_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de recompensas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de recompensas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpecialEffects_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos especiais da fase */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos especiais da fase" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UniqueEvents_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Eventos \xc3\xbanicos da fase */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eventos \xc3\xbanicos da fase" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveEffects_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos especiais ativos nesta fase (do MapPhaseManager) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos especiais ativos nesta fase (do MapPhaseManager)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSpecialObjectivesActive_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se objetivos especiais est\xc3\xa3o ativos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se objetivos especiais est\xc3\xa3o ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bChaosIslandsActive_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se Chaos Islands est\xc3\xa3o ativas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se Chaos Islands est\xc3\xa3o ativas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsEntryDevice_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es para dispositivos de entrada (baixo desempenho) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es para dispositivos de entrada (baixo desempenho)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrailPowerPercentage_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Trilhos a 50% de poder na Fase 1 */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Trilhos a 50% de poder na Fase 1" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllIslandsFullyEmerged_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Todas as ilhas totalmente emergidas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Todas as ilhas totalmente emergidas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTerrainDeformationEnabled_MetaData[] = {
		{ "Category", "AURACRONPhaseSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Deforma\xc3\xa7\xc3\xa3o de terreno opcional */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Deforma\xc3\xa7\xc3\xa3o de terreno opcional" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Phase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Phase;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PhaseName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PhaseDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PhaseStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MapScaleFactor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentSpeedMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PhaseIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentActivityScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionSpeed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PhaseColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_JungleRespawnMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObjectiveRespawnMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObjectiveHealthMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RewardMultiplier;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SpecialEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpecialEffects;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UniqueEvents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UniqueEvents;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActiveEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveEffects;
	static void NewProp_bSpecialObjectivesActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSpecialObjectivesActive;
	static void NewProp_bChaosIslandsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bChaosIslandsActive;
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TrailPowerPercentage;
	static void NewProp_bAllIslandsFullyEmerged_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllIslandsFullyEmerged;
	static void NewProp_bTerrainDeformationEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTerrainDeformationEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONPhaseSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_Phase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_Phase = { "Phase", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, Phase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Phase_MetaData), NewProp_Phase_MetaData) }; // 2541365769
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_PhaseName = { "PhaseName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, PhaseName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseName_MetaData), NewProp_PhaseName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_PhaseDuration = { "PhaseDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, PhaseDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseDuration_MetaData), NewProp_PhaseDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_PhaseStartTime = { "PhaseStartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, PhaseStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseStartTime_MetaData), NewProp_PhaseStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_MapScaleFactor = { "MapScaleFactor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, MapScaleFactor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapScaleFactor_MetaData), NewProp_MapScaleFactor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_EnvironmentSpeedMultiplier = { "EnvironmentSpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, EnvironmentSpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentSpeedMultiplier_MetaData), NewProp_EnvironmentSpeedMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_PhaseIntensity = { "PhaseIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, PhaseIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseIntensity_MetaData), NewProp_PhaseIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_EnvironmentActivityScale = { "EnvironmentActivityScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, EnvironmentActivityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentActivityScale_MetaData), NewProp_EnvironmentActivityScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_TransitionSpeed = { "TransitionSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, TransitionSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionSpeed_MetaData), NewProp_TransitionSpeed_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_PhaseColor = { "PhaseColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, PhaseColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseColor_MetaData), NewProp_PhaseColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_JungleRespawnMultiplier = { "JungleRespawnMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, JungleRespawnMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JungleRespawnMultiplier_MetaData), NewProp_JungleRespawnMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_ObjectiveRespawnMultiplier = { "ObjectiveRespawnMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, ObjectiveRespawnMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveRespawnMultiplier_MetaData), NewProp_ObjectiveRespawnMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_ObjectiveHealthMultiplier = { "ObjectiveHealthMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, ObjectiveHealthMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveHealthMultiplier_MetaData), NewProp_ObjectiveHealthMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_RewardMultiplier = { "RewardMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, RewardMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardMultiplier_MetaData), NewProp_RewardMultiplier_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_SpecialEffects_Inner = { "SpecialEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_SpecialEffects = { "SpecialEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, SpecialEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpecialEffects_MetaData), NewProp_SpecialEffects_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_UniqueEvents_Inner = { "UniqueEvents", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_UniqueEvents = { "UniqueEvents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, UniqueEvents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UniqueEvents_MetaData), NewProp_UniqueEvents_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_ActiveEffects_Inner = { "ActiveEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_ActiveEffects = { "ActiveEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, ActiveEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveEffects_MetaData), NewProp_ActiveEffects_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bSpecialObjectivesActive_SetBit(void* Obj)
{
	((FAURACRONPhaseSettings*)Obj)->bSpecialObjectivesActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bSpecialObjectivesActive = { "bSpecialObjectivesActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPhaseSettings), &Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bSpecialObjectivesActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSpecialObjectivesActive_MetaData), NewProp_bSpecialObjectivesActive_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bChaosIslandsActive_SetBit(void* Obj)
{
	((FAURACRONPhaseSettings*)Obj)->bChaosIslandsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bChaosIslandsActive = { "bChaosIslandsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPhaseSettings), &Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bChaosIslandsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bChaosIslandsActive_MetaData), NewProp_bChaosIslandsActive_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((FAURACRONPhaseSettings*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPhaseSettings), &Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsEntryDevice_MetaData), NewProp_bIsEntryDevice_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_TrailPowerPercentage = { "TrailPowerPercentage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPhaseSettings, TrailPowerPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrailPowerPercentage_MetaData), NewProp_TrailPowerPercentage_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bAllIslandsFullyEmerged_SetBit(void* Obj)
{
	((FAURACRONPhaseSettings*)Obj)->bAllIslandsFullyEmerged = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bAllIslandsFullyEmerged = { "bAllIslandsFullyEmerged", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPhaseSettings), &Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bAllIslandsFullyEmerged_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllIslandsFullyEmerged_MetaData), NewProp_bAllIslandsFullyEmerged_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bTerrainDeformationEnabled_SetBit(void* Obj)
{
	((FAURACRONPhaseSettings*)Obj)->bTerrainDeformationEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bTerrainDeformationEnabled = { "bTerrainDeformationEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPhaseSettings), &Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bTerrainDeformationEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTerrainDeformationEnabled_MetaData), NewProp_bTerrainDeformationEnabled_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_Phase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_Phase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_PhaseName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_PhaseDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_PhaseStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_MapScaleFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_EnvironmentSpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_PhaseIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_EnvironmentActivityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_TransitionSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_PhaseColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_JungleRespawnMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_ObjectiveRespawnMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_ObjectiveHealthMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_RewardMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_SpecialEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_SpecialEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_UniqueEvents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_UniqueEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_ActiveEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_ActiveEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bSpecialObjectivesActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bChaosIslandsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bIsEntryDevice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_TrailPowerPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bAllIslandsFullyEmerged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewProp_bTerrainDeformationEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONPhaseSettings",
	Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::PropPointers),
	sizeof(FAURACRONPhaseSettings),
	alignof(FAURACRONPhaseSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPhaseSettings()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPhaseSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONPhaseSettings.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPhaseSettings.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONPhaseSettings **********************************************

// ********** Begin Delegate FOnMapPhaseChanged ****************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnMapPhaseChanged_Parms
	{
		EAURACRONMapPhase OldPhase;
		EAURACRONMapPhase NewPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Delegates para notifica\xc3\xa7\xc3\xa3o de mudan\xc3\xa7""as de fase\n * CONSOLIDADO: Combina delegates de ambos os sistemas\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates para notifica\xc3\xa7\xc3\xa3o de mudan\xc3\xa7""as de fase\nCONSOLIDADO: Combina delegates de ambos os sistemas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldPhase;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::NewProp_OldPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::NewProp_OldPhase = { "OldPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnMapPhaseChanged_Parms, OldPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase = { "NewPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnMapPhaseChanged_Parms, NewPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::NewProp_OldPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::NewProp_OldPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnMapPhaseChanged__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnMapPhaseChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnMapPhaseChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnMapPhaseChanged_DelegateWrapper(const FMulticastScriptDelegate& OnMapPhaseChanged, EAURACRONMapPhase OldPhase, EAURACRONMapPhase NewPhase)
{
	struct _Script_AURACRON_eventOnMapPhaseChanged_Parms
	{
		EAURACRONMapPhase OldPhase;
		EAURACRONMapPhase NewPhase;
	};
	_Script_AURACRON_eventOnMapPhaseChanged_Parms Parms;
	Parms.OldPhase=OldPhase;
	Parms.NewPhase=NewPhase;
	OnMapPhaseChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMapPhaseChanged ******************************************************

// ********** Begin Delegate FOnPhaseTransitionProgress ********************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnPhaseTransitionProgress_Parms
	{
		EAURACRONMapPhase CurrentPhase;
		float TransitionProgress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionProgress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::NewProp_CurrentPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::NewProp_CurrentPhase = { "CurrentPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnPhaseTransitionProgress_Parms, CurrentPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::NewProp_TransitionProgress = { "TransitionProgress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnPhaseTransitionProgress_Parms, TransitionProgress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::NewProp_CurrentPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::NewProp_CurrentPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::NewProp_TransitionProgress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnPhaseTransitionProgress__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::_Script_AURACRON_eventOnPhaseTransitionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::_Script_AURACRON_eventOnPhaseTransitionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPhaseTransitionProgress_DelegateWrapper(const FMulticastScriptDelegate& OnPhaseTransitionProgress, EAURACRONMapPhase CurrentPhase, float TransitionProgress)
{
	struct _Script_AURACRON_eventOnPhaseTransitionProgress_Parms
	{
		EAURACRONMapPhase CurrentPhase;
		float TransitionProgress;
	};
	_Script_AURACRON_eventOnPhaseTransitionProgress_Parms Parms;
	Parms.CurrentPhase=CurrentPhase;
	Parms.TransitionProgress=TransitionProgress;
	OnPhaseTransitionProgress.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPhaseTransitionProgress **********************************************

// ********** Begin Delegate FOnMapContraction *****************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnMapContraction_Parms
	{
		float ContractionFactor;
		float ContractedRadius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ContractionFactor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ContractedRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature_Statics::NewProp_ContractionFactor = { "ContractionFactor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnMapContraction_Parms, ContractionFactor), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature_Statics::NewProp_ContractedRadius = { "ContractedRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnMapContraction_Parms, ContractedRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature_Statics::NewProp_ContractionFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature_Statics::NewProp_ContractedRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnMapContraction__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature_Statics::_Script_AURACRON_eventOnMapContraction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature_Statics::_Script_AURACRON_eventOnMapContraction_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnMapContraction_DelegateWrapper(const FMulticastScriptDelegate& OnMapContraction, float ContractionFactor, float ContractedRadius)
{
	struct _Script_AURACRON_eventOnMapContraction_Parms
	{
		float ContractionFactor;
		float ContractedRadius;
	};
	_Script_AURACRON_eventOnMapContraction_Parms Parms;
	Parms.ContractionFactor=ContractionFactor;
	Parms.ContractedRadius=ContractedRadius;
	OnMapContraction.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMapContraction *******************************************************

// ********** Begin Class AAURACRONPCGPhaseManager Function AcceleratePhaseProgression *************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_AcceleratePhaseProgression_Statics
{
	struct AURACRONPCGPhaseManager_eventAcceleratePhaseProgression_Parms
	{
		float SpeedMultiplier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Acelerar progress\xc3\xa3o das fases (para testes) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Acelerar progress\xc3\xa3o das fases (para testes)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpeedMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_AcceleratePhaseProgression_Statics::NewProp_SpeedMultiplier = { "SpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPhaseManager_eventAcceleratePhaseProgression_Parms, SpeedMultiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_AcceleratePhaseProgression_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_AcceleratePhaseProgression_Statics::NewProp_SpeedMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_AcceleratePhaseProgression_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_AcceleratePhaseProgression_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "AcceleratePhaseProgression", Z_Construct_UFunction_AAURACRONPCGPhaseManager_AcceleratePhaseProgression_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_AcceleratePhaseProgression_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_AcceleratePhaseProgression_Statics::AURACRONPCGPhaseManager_eventAcceleratePhaseProgression_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_AcceleratePhaseProgression_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_AcceleratePhaseProgression_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_AcceleratePhaseProgression_Statics::AURACRONPCGPhaseManager_eventAcceleratePhaseProgression_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_AcceleratePhaseProgression()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_AcceleratePhaseProgression_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execAcceleratePhaseProgression)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_SpeedMultiplier);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AcceleratePhaseProgression(Z_Param_SpeedMultiplier);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function AcceleratePhaseProgression ***************

// ********** Begin Class AAURACRONPCGPhaseManager Function ApplyAwakeningPhaseSettings ************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyAwakeningPhaseSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas da Fase 1: Despertar */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas da Fase 1: Despertar" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyAwakeningPhaseSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "ApplyAwakeningPhaseSettings", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyAwakeningPhaseSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyAwakeningPhaseSettings_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyAwakeningPhaseSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyAwakeningPhaseSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execApplyAwakeningPhaseSettings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyAwakeningPhaseSettings();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function ApplyAwakeningPhaseSettings **************

// ********** Begin Class AAURACRONPCGPhaseManager Function ApplyConvergencePhaseSettings **********
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyConvergencePhaseSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas da Fase 2: Converg\xc3\xaancia */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas da Fase 2: Converg\xc3\xaancia" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyConvergencePhaseSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "ApplyConvergencePhaseSettings", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyConvergencePhaseSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyConvergencePhaseSettings_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyConvergencePhaseSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyConvergencePhaseSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execApplyConvergencePhaseSettings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyConvergencePhaseSettings();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function ApplyConvergencePhaseSettings ************

// ********** Begin Class AAURACRONPCGPhaseManager Function ApplyTemporaryPhaseEffect **************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect_Statics
{
	struct AURACRONPCGPhaseManager_eventApplyTemporaryPhaseEffect_Parms
	{
		FString EffectName;
		float Duration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar efeitos especiais tempor\xc3\xa1rios */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar efeitos especiais tempor\xc3\xa1rios" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EffectName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect_Statics::NewProp_EffectName = { "EffectName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPhaseManager_eventApplyTemporaryPhaseEffect_Parms, EffectName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectName_MetaData), NewProp_EffectName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPhaseManager_eventApplyTemporaryPhaseEffect_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect_Statics::NewProp_EffectName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "ApplyTemporaryPhaseEffect", Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect_Statics::AURACRONPCGPhaseManager_eventApplyTemporaryPhaseEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect_Statics::AURACRONPCGPhaseManager_eventApplyTemporaryPhaseEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execApplyTemporaryPhaseEffect)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EffectName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyTemporaryPhaseEffect(Z_Param_EffectName,Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function ApplyTemporaryPhaseEffect ****************

// ********** Begin Class AAURACRONPCGPhaseManager Function ConfigureEnvironmentsForDeviceType *****
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType_Statics
{
	struct AURACRONPCGPhaseManager_eventConfigureEnvironmentsForDeviceType_Parms
	{
		bool bIsEntryDevice;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar visibilidade dos ambientes com base no tipo de dispositivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar visibilidade dos ambientes com base no tipo de dispositivo" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((AURACRONPCGPhaseManager_eventConfigureEnvironmentsForDeviceType_Parms*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPhaseManager_eventConfigureEnvironmentsForDeviceType_Parms), &Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType_Statics::NewProp_bIsEntryDevice,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "ConfigureEnvironmentsForDeviceType", Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType_Statics::AURACRONPCGPhaseManager_eventConfigureEnvironmentsForDeviceType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType_Statics::AURACRONPCGPhaseManager_eventConfigureEnvironmentsForDeviceType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execConfigureEnvironmentsForDeviceType)
{
	P_GET_UBOOL(Z_Param_bIsEntryDevice);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureEnvironmentsForDeviceType(Z_Param_bIsEntryDevice);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function ConfigureEnvironmentsForDeviceType *******

// ********** Begin Class AAURACRONPCGPhaseManager Function ConfigureForEntryDevice ****************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice_Statics
{
	struct AURACRONPCGPhaseManager_eventConfigureForEntryDevice_Parms
	{
		bool bIsEntryDevice;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar o sistema para dispositivo de entrada (baixo desempenho) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar o sistema para dispositivo de entrada (baixo desempenho)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((AURACRONPCGPhaseManager_eventConfigureForEntryDevice_Parms*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPhaseManager_eventConfigureForEntryDevice_Parms), &Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice_Statics::NewProp_bIsEntryDevice,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "ConfigureForEntryDevice", Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice_Statics::AURACRONPCGPhaseManager_eventConfigureForEntryDevice_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice_Statics::AURACRONPCGPhaseManager_eventConfigureForEntryDevice_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execConfigureForEntryDevice)
{
	P_GET_UBOOL(Z_Param_bIsEntryDevice);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureForEntryDevice(Z_Param_bIsEntryDevice);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function ConfigureForEntryDevice ******************

// ********** Begin Class AAURACRONPCGPhaseManager Function FixDocumentationComplianceIssues *******
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics
{
	struct AURACRONPCGPhaseManager_eventFixDocumentationComplianceIssues_Parms
	{
		EAURACRONMapPhase Phase;
		bool bIsEntryDevice;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Corrigir problemas de conformidade com a documenta\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Corrigir problemas de conformidade com a documenta\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Phase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Phase;
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::NewProp_Phase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::NewProp_Phase = { "Phase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPhaseManager_eventFixDocumentationComplianceIssues_Parms, Phase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
void Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((AURACRONPCGPhaseManager_eventFixDocumentationComplianceIssues_Parms*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPhaseManager_eventFixDocumentationComplianceIssues_Parms), &Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::NewProp_Phase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::NewProp_Phase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::NewProp_bIsEntryDevice,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "FixDocumentationComplianceIssues", Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::AURACRONPCGPhaseManager_eventFixDocumentationComplianceIssues_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::AURACRONPCGPhaseManager_eventFixDocumentationComplianceIssues_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execFixDocumentationComplianceIssues)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_Phase);
	P_GET_UBOOL(Z_Param_bIsEntryDevice);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->FixDocumentationComplianceIssues(EAURACRONMapPhase(Z_Param_Phase),Z_Param_bIsEntryDevice);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function FixDocumentationComplianceIssues *********

// ********** Begin Class AAURACRONPCGPhaseManager Function ForcePhaseTransition *******************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition_Statics
{
	struct AURACRONPCGPhaseManager_eventForcePhaseTransition_Parms
	{
		EAURACRONMapPhase TargetPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\xa7""ar transi\xc3\xa7\xc3\xa3o para uma fase espec\xc3\xad""fica */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""ar transi\xc3\xa7\xc3\xa3o para uma fase espec\xc3\xad""fica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition_Statics::NewProp_TargetPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition_Statics::NewProp_TargetPhase = { "TargetPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPhaseManager_eventForcePhaseTransition_Parms, TargetPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition_Statics::NewProp_TargetPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition_Statics::NewProp_TargetPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "ForcePhaseTransition", Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition_Statics::AURACRONPCGPhaseManager_eventForcePhaseTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition_Statics::AURACRONPCGPhaseManager_eventForcePhaseTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execForcePhaseTransition)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_TargetPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForcePhaseTransition(EAURACRONMapPhase(Z_Param_TargetPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function ForcePhaseTransition *********************

// ********** Begin Class AAURACRONPCGPhaseManager Function ForceTransitionToPhase *****************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase_Statics
{
	struct AURACRONPCGPhaseManager_eventForceTransitionToPhase_Parms
	{
		EAURACRONMapPhase TargetPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\xa7""ar transi\xc3\xa7\xc3\xa3o para fase espec\xc3\xad""fica */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""ar transi\xc3\xa7\xc3\xa3o para fase espec\xc3\xad""fica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase_Statics::NewProp_TargetPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase_Statics::NewProp_TargetPhase = { "TargetPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPhaseManager_eventForceTransitionToPhase_Parms, TargetPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase_Statics::NewProp_TargetPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase_Statics::NewProp_TargetPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "ForceTransitionToPhase", Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase_Statics::AURACRONPCGPhaseManager_eventForceTransitionToPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase_Statics::AURACRONPCGPhaseManager_eventForceTransitionToPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execForceTransitionToPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_TargetPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceTransitionToPhase(EAURACRONMapPhase(Z_Param_TargetPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function ForceTransitionToPhase *******************

// ********** Begin Class AAURACRONPCGPhaseManager Function GetCurrentPhase ************************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase_Statics
{
	struct AURACRONPCGPhaseManager_eventGetCurrentPhase_Parms
	{
		EAURACRONMapPhase ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter fase atualmente ativa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter fase atualmente ativa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPhaseManager_eventGetCurrentPhase_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "GetCurrentPhase", Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase_Statics::AURACRONPCGPhaseManager_eventGetCurrentPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase_Statics::AURACRONPCGPhaseManager_eventGetCurrentPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execGetCurrentPhase)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONMapPhase*)Z_Param__Result=P_THIS->GetCurrentPhase();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function GetCurrentPhase **************************

// ********** Begin Class AAURACRONPCGPhaseManager Function GetNextPhase ***************************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase_Statics
{
	struct AURACRONPCGPhaseManager_eventGetNextPhase_Parms
	{
		EAURACRONMapPhase ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter pr\xc3\xb3xima fase na progress\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter pr\xc3\xb3xima fase na progress\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPhaseManager_eventGetNextPhase_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "GetNextPhase", Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase_Statics::AURACRONPCGPhaseManager_eventGetNextPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase_Statics::AURACRONPCGPhaseManager_eventGetNextPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execGetNextPhase)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONMapPhase*)Z_Param__Result=P_THIS->GetNextPhase();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function GetNextPhase *****************************

// ********** Begin Class AAURACRONPCGPhaseManager Function GetPhaseSettings ***********************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics
{
	struct AURACRONPCGPhaseManager_eventGetPhaseSettings_Parms
	{
		EAURACRONMapPhase Phase;
		FAURACRONPhaseSettings ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter configura\xc3\xa7\xc3\xb5""es de uma fase */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter configura\xc3\xa7\xc3\xb5""es de uma fase" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Phase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Phase;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::NewProp_Phase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::NewProp_Phase = { "Phase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPhaseManager_eventGetPhaseSettings_Parms, Phase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPhaseManager_eventGetPhaseSettings_Parms, ReturnValue), Z_Construct_UScriptStruct_FAURACRONPhaseSettings, METADATA_PARAMS(0, nullptr) }; // 928839804
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::NewProp_Phase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::NewProp_Phase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "GetPhaseSettings", Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::AURACRONPCGPhaseManager_eventGetPhaseSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::AURACRONPCGPhaseManager_eventGetPhaseSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execGetPhaseSettings)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_Phase);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAURACRONPhaseSettings*)Z_Param__Result=P_THIS->GetPhaseSettings(EAURACRONMapPhase(Z_Param_Phase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function GetPhaseSettings *************************

// ********** Begin Class AAURACRONPCGPhaseManager Function GetTimeRemainingInCurrentPhase *********
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTimeRemainingInCurrentPhase_Statics
{
	struct AURACRONPCGPhaseManager_eventGetTimeRemainingInCurrentPhase_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter tempo restante na fase atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter tempo restante na fase atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTimeRemainingInCurrentPhase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPhaseManager_eventGetTimeRemainingInCurrentPhase_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTimeRemainingInCurrentPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTimeRemainingInCurrentPhase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTimeRemainingInCurrentPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTimeRemainingInCurrentPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "GetTimeRemainingInCurrentPhase", Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTimeRemainingInCurrentPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTimeRemainingInCurrentPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTimeRemainingInCurrentPhase_Statics::AURACRONPCGPhaseManager_eventGetTimeRemainingInCurrentPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTimeRemainingInCurrentPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTimeRemainingInCurrentPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTimeRemainingInCurrentPhase_Statics::AURACRONPCGPhaseManager_eventGetTimeRemainingInCurrentPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTimeRemainingInCurrentPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTimeRemainingInCurrentPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execGetTimeRemainingInCurrentPhase)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTimeRemainingInCurrentPhase();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function GetTimeRemainingInCurrentPhase ***********

// ********** Begin Class AAURACRONPCGPhaseManager Function GetTotalElapsedTime ********************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTotalElapsedTime_Statics
{
	struct AURACRONPCGPhaseManager_eventGetTotalElapsedTime_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter tempo total decorrido desde o in\xc3\xad""cio */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter tempo total decorrido desde o in\xc3\xad""cio" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTotalElapsedTime_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPhaseManager_eventGetTotalElapsedTime_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTotalElapsedTime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTotalElapsedTime_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTotalElapsedTime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTotalElapsedTime_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "GetTotalElapsedTime", Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTotalElapsedTime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTotalElapsedTime_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTotalElapsedTime_Statics::AURACRONPCGPhaseManager_eventGetTotalElapsedTime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTotalElapsedTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTotalElapsedTime_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTotalElapsedTime_Statics::AURACRONPCGPhaseManager_eventGetTotalElapsedTime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTotalElapsedTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTotalElapsedTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execGetTotalElapsedTime)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTotalElapsedTime();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function GetTotalElapsedTime **********************

// ********** Begin Class AAURACRONPCGPhaseManager Function GetTransitionProgress ******************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTransitionProgress_Statics
{
	struct AURACRONPCGPhaseManager_eventGetTransitionProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter progresso da transi\xc3\xa7\xc3\xa3o atual (0.0 - 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter progresso da transi\xc3\xa7\xc3\xa3o atual (0.0 - 1.0)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTransitionProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPhaseManager_eventGetTransitionProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTransitionProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTransitionProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTransitionProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTransitionProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "GetTransitionProgress", Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTransitionProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTransitionProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTransitionProgress_Statics::AURACRONPCGPhaseManager_eventGetTransitionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTransitionProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTransitionProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTransitionProgress_Statics::AURACRONPCGPhaseManager_eventGetTransitionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTransitionProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTransitionProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execGetTransitionProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTransitionProgress();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function GetTransitionProgress ********************

// ********** Begin Class AAURACRONPCGPhaseManager Function InitializePhaseSystem ******************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_InitializePhaseSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializar sistema de fases */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar sistema de fases" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_InitializePhaseSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "InitializePhaseSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_InitializePhaseSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_InitializePhaseSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_InitializePhaseSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_InitializePhaseSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execInitializePhaseSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializePhaseSystem();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function InitializePhaseSystem ********************

// ********** Begin Class AAURACRONPCGPhaseManager Function IntegrateSystemsForAwakeningPhase ******
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase_Statics
{
	struct AURACRONPCGPhaseManager_eventIntegrateSystemsForAwakeningPhase_Parms
	{
		bool bIsEntryDevice;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integrar todos os sistemas para Fase 1: Despertar */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integrar todos os sistemas para Fase 1: Despertar" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((AURACRONPCGPhaseManager_eventIntegrateSystemsForAwakeningPhase_Parms*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPhaseManager_eventIntegrateSystemsForAwakeningPhase_Parms), &Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase_Statics::NewProp_bIsEntryDevice,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "IntegrateSystemsForAwakeningPhase", Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase_Statics::AURACRONPCGPhaseManager_eventIntegrateSystemsForAwakeningPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase_Statics::AURACRONPCGPhaseManager_eventIntegrateSystemsForAwakeningPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execIntegrateSystemsForAwakeningPhase)
{
	P_GET_UBOOL(Z_Param_bIsEntryDevice);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateSystemsForAwakeningPhase(Z_Param_bIsEntryDevice);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function IntegrateSystemsForAwakeningPhase ********

// ********** Begin Class AAURACRONPCGPhaseManager Function IntegrateSystemsForConvergencePhase ****
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase_Statics
{
	struct AURACRONPCGPhaseManager_eventIntegrateSystemsForConvergencePhase_Parms
	{
		bool bIsEntryDevice;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integrar todos os sistemas para Fase 2: Converg\xc3\xaancia */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integrar todos os sistemas para Fase 2: Converg\xc3\xaancia" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((AURACRONPCGPhaseManager_eventIntegrateSystemsForConvergencePhase_Parms*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPhaseManager_eventIntegrateSystemsForConvergencePhase_Parms), &Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase_Statics::NewProp_bIsEntryDevice,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "IntegrateSystemsForConvergencePhase", Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase_Statics::AURACRONPCGPhaseManager_eventIntegrateSystemsForConvergencePhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase_Statics::AURACRONPCGPhaseManager_eventIntegrateSystemsForConvergencePhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execIntegrateSystemsForConvergencePhase)
{
	P_GET_UBOOL(Z_Param_bIsEntryDevice);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateSystemsForConvergencePhase(Z_Param_bIsEntryDevice);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function IntegrateSystemsForConvergencePhase ******

// ********** Begin Class AAURACRONPCGPhaseManager Function IsInTransition *************************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition_Statics
{
	struct AURACRONPCGPhaseManager_eventIsInTransition_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se est\xc3\xa1 em transi\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se est\xc3\xa1 em transi\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGPhaseManager_eventIsInTransition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPhaseManager_eventIsInTransition_Parms), &Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "IsInTransition", Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition_Statics::AURACRONPCGPhaseManager_eventIsInTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition_Statics::AURACRONPCGPhaseManager_eventIsInTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execIsInTransition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInTransition();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function IsInTransition ***************************

// ********** Begin Class AAURACRONPCGPhaseManager Function OnPhaseTimerExpired ********************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_OnPhaseTimerExpired_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback para timer de fase (compatibilidade) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback para timer de fase (compatibilidade)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_OnPhaseTimerExpired_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "OnPhaseTimerExpired", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_OnPhaseTimerExpired_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_OnPhaseTimerExpired_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_OnPhaseTimerExpired()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_OnPhaseTimerExpired_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execOnPhaseTimerExpired)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPhaseTimerExpired();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function OnPhaseTimerExpired **********************

// ********** Begin Class AAURACRONPCGPhaseManager Function SetPhaseSystemPaused *******************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused_Statics
{
	struct AURACRONPCGPhaseManager_eventSetPhaseSystemPaused_Parms
	{
		bool bPaused;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pausar/Despausar o sistema de fases */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pausar/Despausar o sistema de fases" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bPaused_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPaused;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused_Statics::NewProp_bPaused_SetBit(void* Obj)
{
	((AURACRONPCGPhaseManager_eventSetPhaseSystemPaused_Parms*)Obj)->bPaused = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused_Statics::NewProp_bPaused = { "bPaused", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPhaseManager_eventSetPhaseSystemPaused_Parms), &Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused_Statics::NewProp_bPaused_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused_Statics::NewProp_bPaused,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "SetPhaseSystemPaused", Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused_Statics::AURACRONPCGPhaseManager_eventSetPhaseSystemPaused_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused_Statics::AURACRONPCGPhaseManager_eventSetPhaseSystemPaused_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execSetPhaseSystemPaused)
{
	P_GET_UBOOL(Z_Param_bPaused);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPhaseSystemPaused(Z_Param_bPaused);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function SetPhaseSystemPaused *********************

// ********** Begin Class AAURACRONPCGPhaseManager Function StartMapPhases *************************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_StartMapPhases_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Iniciar o ciclo de fases do mapa (do MapPhaseManager) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iniciar o ciclo de fases do mapa (do MapPhaseManager)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_StartMapPhases_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "StartMapPhases", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_StartMapPhases_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_StartMapPhases_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_StartMapPhases()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_StartMapPhases_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execStartMapPhases)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartMapPhases();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function StartMapPhases ***************************

// ********** Begin Class AAURACRONPCGPhaseManager Function StartPhaseProgression ******************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_StartPhaseProgression_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Iniciar progress\xc3\xa3o autom\xc3\xa1tica das fases */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iniciar progress\xc3\xa3o autom\xc3\xa1tica das fases" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_StartPhaseProgression_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "StartPhaseProgression", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_StartPhaseProgression_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_StartPhaseProgression_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_StartPhaseProgression()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_StartPhaseProgression_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execStartPhaseProgression)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartPhaseProgression();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function StartPhaseProgression ********************

// ********** Begin Class AAURACRONPCGPhaseManager Function StopPhaseProgression *******************
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_StopPhaseProgression_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Parar progress\xc3\xa3o autom\xc3\xa1tica */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parar progress\xc3\xa3o autom\xc3\xa1tica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_StopPhaseProgression_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "StopPhaseProgression", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_StopPhaseProgression_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_StopPhaseProgression_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_StopPhaseProgression()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_StopPhaseProgression_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execStopPhaseProgression)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopPhaseProgression();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function StopPhaseProgression *********************

// ********** Begin Class AAURACRONPCGPhaseManager Function VerifyDocumentationCompliance **********
struct Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics
{
	struct AURACRONPCGPhaseManager_eventVerifyDocumentationCompliance_Parms
	{
		EAURACRONMapPhase Phase;
		bool bIsEntryDevice;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar conformidade com a documenta\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar conformidade com a documenta\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Phase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Phase;
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::NewProp_Phase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::NewProp_Phase = { "Phase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPhaseManager_eventVerifyDocumentationCompliance_Parms, Phase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
void Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((AURACRONPCGPhaseManager_eventVerifyDocumentationCompliance_Parms*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPhaseManager_eventVerifyDocumentationCompliance_Parms), &Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGPhaseManager_eventVerifyDocumentationCompliance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPhaseManager_eventVerifyDocumentationCompliance_Parms), &Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::NewProp_Phase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::NewProp_Phase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::NewProp_bIsEntryDevice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPhaseManager, nullptr, "VerifyDocumentationCompliance", Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::AURACRONPCGPhaseManager_eventVerifyDocumentationCompliance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::AURACRONPCGPhaseManager_eventVerifyDocumentationCompliance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPhaseManager::execVerifyDocumentationCompliance)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_Phase);
	P_GET_UBOOL(Z_Param_bIsEntryDevice);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->VerifyDocumentationCompliance(EAURACRONMapPhase(Z_Param_Phase),Z_Param_bIsEntryDevice);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPhaseManager Function VerifyDocumentationCompliance ************

// ********** Begin Class AAURACRONPCGPhaseManager *************************************************
void AAURACRONPCGPhaseManager::StaticRegisterNativesAAURACRONPCGPhaseManager()
{
	UClass* Class = AAURACRONPCGPhaseManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AcceleratePhaseProgression", &AAURACRONPCGPhaseManager::execAcceleratePhaseProgression },
		{ "ApplyAwakeningPhaseSettings", &AAURACRONPCGPhaseManager::execApplyAwakeningPhaseSettings },
		{ "ApplyConvergencePhaseSettings", &AAURACRONPCGPhaseManager::execApplyConvergencePhaseSettings },
		{ "ApplyTemporaryPhaseEffect", &AAURACRONPCGPhaseManager::execApplyTemporaryPhaseEffect },
		{ "ConfigureEnvironmentsForDeviceType", &AAURACRONPCGPhaseManager::execConfigureEnvironmentsForDeviceType },
		{ "ConfigureForEntryDevice", &AAURACRONPCGPhaseManager::execConfigureForEntryDevice },
		{ "FixDocumentationComplianceIssues", &AAURACRONPCGPhaseManager::execFixDocumentationComplianceIssues },
		{ "ForcePhaseTransition", &AAURACRONPCGPhaseManager::execForcePhaseTransition },
		{ "ForceTransitionToPhase", &AAURACRONPCGPhaseManager::execForceTransitionToPhase },
		{ "GetCurrentPhase", &AAURACRONPCGPhaseManager::execGetCurrentPhase },
		{ "GetNextPhase", &AAURACRONPCGPhaseManager::execGetNextPhase },
		{ "GetPhaseSettings", &AAURACRONPCGPhaseManager::execGetPhaseSettings },
		{ "GetTimeRemainingInCurrentPhase", &AAURACRONPCGPhaseManager::execGetTimeRemainingInCurrentPhase },
		{ "GetTotalElapsedTime", &AAURACRONPCGPhaseManager::execGetTotalElapsedTime },
		{ "GetTransitionProgress", &AAURACRONPCGPhaseManager::execGetTransitionProgress },
		{ "InitializePhaseSystem", &AAURACRONPCGPhaseManager::execInitializePhaseSystem },
		{ "IntegrateSystemsForAwakeningPhase", &AAURACRONPCGPhaseManager::execIntegrateSystemsForAwakeningPhase },
		{ "IntegrateSystemsForConvergencePhase", &AAURACRONPCGPhaseManager::execIntegrateSystemsForConvergencePhase },
		{ "IsInTransition", &AAURACRONPCGPhaseManager::execIsInTransition },
		{ "OnPhaseTimerExpired", &AAURACRONPCGPhaseManager::execOnPhaseTimerExpired },
		{ "SetPhaseSystemPaused", &AAURACRONPCGPhaseManager::execSetPhaseSystemPaused },
		{ "StartMapPhases", &AAURACRONPCGPhaseManager::execStartMapPhases },
		{ "StartPhaseProgression", &AAURACRONPCGPhaseManager::execStartPhaseProgression },
		{ "StopPhaseProgression", &AAURACRONPCGPhaseManager::execStopPhaseProgression },
		{ "VerifyDocumentationCompliance", &AAURACRONPCGPhaseManager::execVerifyDocumentationCompliance },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGPhaseManager;
UClass* AAURACRONPCGPhaseManager::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGPhaseManager;
	if (!Z_Registration_Info_UClass_AAURACRONPCGPhaseManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGPhaseManager"),
			Z_Registration_Info_UClass_AAURACRONPCGPhaseManager.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGPhaseManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGPhaseManager.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGPhaseManager_NoRegister()
{
	return AAURACRONPCGPhaseManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Gerenciador consolidado das 4 fases evolutivas do mapa AURACRON\n * CONSOLIDADO: Combina AURACRONPCGPhaseManager e AURACRONMapPhaseManager\n * Controla progress\xc3\xa3o autom\xc3\xa1tica, transi\xc3\xa7\xc3\xb5""es suaves e efeitos de cada fase\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGPhaseManager.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerenciador consolidado das 4 fases evolutivas do mapa AURACRON\nCONSOLIDADO: Combina AURACRONPCGPhaseManager e AURACRONMapPhaseManager\nControla progress\xc3\xa3o autom\xc3\xa1tica, transi\xc3\xa7\xc3\xb5""es suaves e efeitos de cada fase" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMapPhaseChanged_MetaData[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando a fase muda */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando a fase muda" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPhaseTransitionProgress_MetaData[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado durante progresso de transi\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado durante progresso de transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMapContraction_MetaData[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando ocorre contra\xc3\xa7\xc3\xa3o do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando ocorre contra\xc3\xa7\xc3\xa3o do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseSettings_MetaData[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es das 4 fases */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es das 4 fases" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bValidateSystemIntegration_MetaData[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valida\xc3\xa7\xc3\xa3o de integra\xc3\xa7\xc3\xa3o entre sistemas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida\xc3\xa7\xc3\xa3o de integra\xc3\xa7\xc3\xa3o entre sistemas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseConfigurations_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es de fase (compatibilidade com MapPhaseManager) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de fase (compatibilidade com MapPhaseManager)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneSystem_MetaData[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancias aos sistemas integrados */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancias aos sistemas integrados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JungleSystem_MetaData[] = {
		{ "Category", "AURACRON|PhaseManager" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveSystem_MetaData[] = {
		{ "Category", "AURACRON|PhaseManager" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentManager_MetaData[] = {
		{ "Category", "AURACRON|PhaseManager" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoStartProgression_MetaData[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve iniciar progress\xc3\xa3o automaticamente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve iniciar progress\xc3\xa3o automaticamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bProgressionActive_MetaData[] = {
		{ "Category", "AURACRON|PhaseManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se a progress\xc3\xa3o est\xc3\xa1 ativa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se a progress\xc3\xa3o est\xc3\xa1 ativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase atualmente ativa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atualmente ativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase de destino durante transi\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase de destino durante transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NextPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pr\xc3\xb3xima fase (compatibilidade com MapPhaseManager) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pr\xc3\xb3xima fase (compatibilidade com MapPhaseManager)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInTransition_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se est\xc3\xa1 em transi\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se est\xc3\xa1 em transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalElapsedTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo total decorrido desde o in\xc3\xad""cio */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo total decorrido desde o in\xc3\xad""cio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeRemainingInPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo restante na fase atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo restante na fase atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTransitionDuration_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o da transi\xc3\xa7\xc3\xa3o atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o da transi\xc3\xa7\xc3\xa3o atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionProgress_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Progresso da transi\xc3\xa7\xc3\xa3o atual (0.0 - 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progresso da transi\xc3\xa7\xc3\xa3o atual (0.0 - 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProgressionSpeedMultiplier_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de velocidade da progress\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de velocidade da progress\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseProgressionTimer_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timer para progress\xc3\xa3o de fases */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timer para progress\xc3\xa3o de fases" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveTemporaryEffects_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos tempor\xc3\xa1rios ativos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos tempor\xc3\xa1rios ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPhaseTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo da fase atual (compatibilidade) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo da fase atual (compatibilidade)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsPaused_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o sistema est\xc3\xa1 pausado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o sistema est\xc3\xa1 pausado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseTimer_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timer de fase (compatibilidade) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timer de fase (compatibilidade)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionDuration_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o da transi\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o da transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGActorReferences_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancias aos atores PCG (usando utility class) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPhaseManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancias aos atores PCG (usando utility class)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMapPhaseChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPhaseTransitionProgress;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMapContraction;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PhaseSettings_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PhaseSettings_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PhaseSettings_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PhaseSettings;
	static void NewProp_bValidateSystemIntegration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bValidateSystemIntegration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PhaseConfigurations_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PhaseConfigurations_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PhaseConfigurations_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PhaseConfigurations;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LaneSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_JungleSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ObjectiveSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnvironmentManager;
	static void NewProp_bAutoStartProgression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoStartProgression;
	static void NewProp_bProgressionActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bProgressionActive;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentPhase;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetPhase;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NextPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NextPhase;
	static void NewProp_bIsInTransition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInTransition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalElapsedTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeRemainingInPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentTransitionDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProgressionSpeedMultiplier;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PhaseProgressionTimer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActiveTemporaryEffects_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActiveTemporaryEffects_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveTemporaryEffects;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentPhaseTime;
	static void NewProp_bIsPaused_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPaused;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PhaseTimer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionDuration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PCGActorReferences;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_AcceleratePhaseProgression, "AcceleratePhaseProgression" }, // 1469393329
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyAwakeningPhaseSettings, "ApplyAwakeningPhaseSettings" }, // 3905309671
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyConvergencePhaseSettings, "ApplyConvergencePhaseSettings" }, // 2704379482
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_ApplyTemporaryPhaseEffect, "ApplyTemporaryPhaseEffect" }, // 3058608373
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureEnvironmentsForDeviceType, "ConfigureEnvironmentsForDeviceType" }, // 4165372536
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_ConfigureForEntryDevice, "ConfigureForEntryDevice" }, // 954201432
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_FixDocumentationComplianceIssues, "FixDocumentationComplianceIssues" }, // 1872933408
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForcePhaseTransition, "ForcePhaseTransition" }, // 1341085185
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_ForceTransitionToPhase, "ForceTransitionToPhase" }, // 3643063647
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetCurrentPhase, "GetCurrentPhase" }, // 931838957
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetNextPhase, "GetNextPhase" }, // 151784067
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetPhaseSettings, "GetPhaseSettings" }, // 3400867225
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTimeRemainingInCurrentPhase, "GetTimeRemainingInCurrentPhase" }, // 4127273864
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTotalElapsedTime, "GetTotalElapsedTime" }, // 1471500581
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_GetTransitionProgress, "GetTransitionProgress" }, // 375437892
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_InitializePhaseSystem, "InitializePhaseSystem" }, // 2449601761
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForAwakeningPhase, "IntegrateSystemsForAwakeningPhase" }, // 2310890457
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_IntegrateSystemsForConvergencePhase, "IntegrateSystemsForConvergencePhase" }, // 1406971656
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_IsInTransition, "IsInTransition" }, // 94715783
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_OnPhaseTimerExpired, "OnPhaseTimerExpired" }, // 2055823144
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_SetPhaseSystemPaused, "SetPhaseSystemPaused" }, // 3394322469
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_StartMapPhases, "StartMapPhases" }, // 2615972929
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_StartPhaseProgression, "StartPhaseProgression" }, // 3167944282
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_StopPhaseProgression, "StopPhaseProgression" }, // 3732361343
		{ &Z_Construct_UFunction_AAURACRONPCGPhaseManager_VerifyDocumentationCompliance, "VerifyDocumentationCompliance" }, // 635881177
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGPhaseManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_OnMapPhaseChanged = { "OnMapPhaseChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, OnMapPhaseChanged), Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMapPhaseChanged_MetaData), NewProp_OnMapPhaseChanged_MetaData) }; // 2001878927
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_OnPhaseTransitionProgress = { "OnPhaseTransitionProgress", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, OnPhaseTransitionProgress), Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPhaseTransitionProgress_MetaData), NewProp_OnPhaseTransitionProgress_MetaData) }; // 508998678
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_OnMapContraction = { "OnMapContraction", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, OnMapContraction), Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMapContraction_MetaData), NewProp_OnMapContraction_MetaData) }; // 2278029625
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseSettings_ValueProp = { "PhaseSettings", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONPhaseSettings, METADATA_PARAMS(0, nullptr) }; // 928839804
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseSettings_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseSettings_Key_KeyProp = { "PhaseSettings_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseSettings = { "PhaseSettings", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, PhaseSettings), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseSettings_MetaData), NewProp_PhaseSettings_MetaData) }; // 2541365769 928839804
void Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bValidateSystemIntegration_SetBit(void* Obj)
{
	((AAURACRONPCGPhaseManager*)Obj)->bValidateSystemIntegration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bValidateSystemIntegration = { "bValidateSystemIntegration", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGPhaseManager), &Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bValidateSystemIntegration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bValidateSystemIntegration_MetaData), NewProp_bValidateSystemIntegration_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseConfigurations_ValueProp = { "PhaseConfigurations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONPhaseSettings, METADATA_PARAMS(0, nullptr) }; // 928839804
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseConfigurations_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseConfigurations_Key_KeyProp = { "PhaseConfigurations_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseConfigurations = { "PhaseConfigurations", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, PhaseConfigurations), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseConfigurations_MetaData), NewProp_PhaseConfigurations_MetaData) }; // 2541365769 928839804
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_LaneSystem = { "LaneSystem", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, LaneSystem), Z_Construct_UClass_AAURACRONPCGLaneSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneSystem_MetaData), NewProp_LaneSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_JungleSystem = { "JungleSystem", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, JungleSystem), Z_Construct_UClass_AAURACRONPCGJungleSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JungleSystem_MetaData), NewProp_JungleSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_ObjectiveSystem = { "ObjectiveSystem", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, ObjectiveSystem), Z_Construct_UClass_AAURACRONPCGObjectiveSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveSystem_MetaData), NewProp_ObjectiveSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_EnvironmentManager = { "EnvironmentManager", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, EnvironmentManager), Z_Construct_UClass_AAURACRONPCGEnvironmentManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentManager_MetaData), NewProp_EnvironmentManager_MetaData) };
void Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bAutoStartProgression_SetBit(void* Obj)
{
	((AAURACRONPCGPhaseManager*)Obj)->bAutoStartProgression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bAutoStartProgression = { "bAutoStartProgression", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGPhaseManager), &Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bAutoStartProgression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoStartProgression_MetaData), NewProp_bAutoStartProgression_MetaData) };
void Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bProgressionActive_SetBit(void* Obj)
{
	((AAURACRONPCGPhaseManager*)Obj)->bProgressionActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bProgressionActive = { "bProgressionActive", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGPhaseManager), &Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bProgressionActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bProgressionActive_MetaData), NewProp_bProgressionActive_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_CurrentPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_CurrentPhase = { "CurrentPhase", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, CurrentPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPhase_MetaData), NewProp_CurrentPhase_MetaData) }; // 2541365769
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_TargetPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_TargetPhase = { "TargetPhase", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, TargetPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetPhase_MetaData), NewProp_TargetPhase_MetaData) }; // 2541365769
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_NextPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_NextPhase = { "NextPhase", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, NextPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NextPhase_MetaData), NewProp_NextPhase_MetaData) }; // 2541365769
void Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bIsInTransition_SetBit(void* Obj)
{
	((AAURACRONPCGPhaseManager*)Obj)->bIsInTransition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bIsInTransition = { "bIsInTransition", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGPhaseManager), &Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bIsInTransition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInTransition_MetaData), NewProp_bIsInTransition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_TotalElapsedTime = { "TotalElapsedTime", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, TotalElapsedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalElapsedTime_MetaData), NewProp_TotalElapsedTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_TimeRemainingInPhase = { "TimeRemainingInPhase", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, TimeRemainingInPhase), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeRemainingInPhase_MetaData), NewProp_TimeRemainingInPhase_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_CurrentTransitionDuration = { "CurrentTransitionDuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, CurrentTransitionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTransitionDuration_MetaData), NewProp_CurrentTransitionDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_TransitionProgress = { "TransitionProgress", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, TransitionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionProgress_MetaData), NewProp_TransitionProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_ProgressionSpeedMultiplier = { "ProgressionSpeedMultiplier", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, ProgressionSpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProgressionSpeedMultiplier_MetaData), NewProp_ProgressionSpeedMultiplier_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseProgressionTimer = { "PhaseProgressionTimer", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, PhaseProgressionTimer), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseProgressionTimer_MetaData), NewProp_PhaseProgressionTimer_MetaData) }; // 3834150579
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_ActiveTemporaryEffects_ValueProp = { "ActiveTemporaryEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_ActiveTemporaryEffects_Key_KeyProp = { "ActiveTemporaryEffects_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_ActiveTemporaryEffects = { "ActiveTemporaryEffects", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, ActiveTemporaryEffects), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveTemporaryEffects_MetaData), NewProp_ActiveTemporaryEffects_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_CurrentPhaseTime = { "CurrentPhaseTime", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, CurrentPhaseTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPhaseTime_MetaData), NewProp_CurrentPhaseTime_MetaData) };
void Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bIsPaused_SetBit(void* Obj)
{
	((AAURACRONPCGPhaseManager*)Obj)->bIsPaused = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bIsPaused = { "bIsPaused", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGPhaseManager), &Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bIsPaused_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsPaused_MetaData), NewProp_bIsPaused_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseTimer = { "PhaseTimer", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, PhaseTimer), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseTimer_MetaData), NewProp_PhaseTimer_MetaData) }; // 3834150579
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_TransitionDuration = { "TransitionDuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, TransitionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionDuration_MetaData), NewProp_TransitionDuration_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PCGActorReferences = { "PCGActorReferences", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPhaseManager, PCGActorReferences), Z_Construct_UScriptStruct_FAURACRONPCGActorReferences, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGActorReferences_MetaData), NewProp_PCGActorReferences_MetaData) }; // 2729947204
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_OnMapPhaseChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_OnPhaseTransitionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_OnMapContraction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseSettings_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseSettings_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseSettings_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bValidateSystemIntegration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseConfigurations_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseConfigurations_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseConfigurations_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseConfigurations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_LaneSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_JungleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_ObjectiveSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_EnvironmentManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bAutoStartProgression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bProgressionActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_CurrentPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_CurrentPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_TargetPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_TargetPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_NextPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_NextPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bIsInTransition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_TotalElapsedTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_TimeRemainingInPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_CurrentTransitionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_TransitionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_ProgressionSpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseProgressionTimer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_ActiveTemporaryEffects_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_ActiveTemporaryEffects_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_ActiveTemporaryEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_CurrentPhaseTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_bIsPaused,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PhaseTimer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_TransitionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::NewProp_PCGActorReferences,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::ClassParams = {
	&AAURACRONPCGPhaseManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGPhaseManager()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGPhaseManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGPhaseManager.OuterSingleton, Z_Construct_UClass_AAURACRONPCGPhaseManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGPhaseManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGPhaseManager);
AAURACRONPCGPhaseManager::~AAURACRONPCGPhaseManager() {}
// ********** End Class AAURACRONPCGPhaseManager ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONPhaseSettings::StaticStruct, Z_Construct_UScriptStruct_FAURACRONPhaseSettings_Statics::NewStructOps, TEXT("AURACRONPhaseSettings"), &Z_Registration_Info_UScriptStruct_FAURACRONPhaseSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONPhaseSettings), 928839804U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGPhaseManager, AAURACRONPCGPhaseManager::StaticClass, TEXT("AAURACRONPCGPhaseManager"), &Z_Registration_Info_UClass_AAURACRONPCGPhaseManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGPhaseManager), 1158465582U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h__Script_AURACRON_2233902196(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPhaseManager_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
