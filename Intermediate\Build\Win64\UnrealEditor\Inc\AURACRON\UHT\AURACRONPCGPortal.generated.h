// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGPortal.h"

#ifdef AURACRON_AURACRONPCGPortal_generated_h
#error "AURACRONPCGPortal.generated.h already included, missing '#pragma once' in AURACRONPCGPortal.h"
#endif
#define AURACRON_AURACRONPCGPortal_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class ACharacter;
class UPrimitiveComponent;
enum class EAURACRONEnvironmentType : uint8;
enum class EAURACRONMapPhase : uint8;
enum class EAURACRONPortalType : uint8;
struct FAURACRONPortalSettings;
struct FHitResult;

// ********** Begin ScriptStruct FAURACRONPortalSettings *******************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h_33_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONPortalSettings;
// ********** End ScriptStruct FAURACRONPortalSettings *********************************************

// ********** Begin Class AAURACRONPCGPortal *******************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h_126_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnOverlapBegin); \
	DECLARE_FUNCTION(execIsUmbralPortal); \
	DECLARE_FUNCTION(execIsZephyrPortal); \
	DECLARE_FUNCTION(execIsRadiantPortal); \
	DECLARE_FUNCTION(execIsPortalVisible); \
	DECLARE_FUNCTION(execIsPortalActive); \
	DECLARE_FUNCTION(execGetDestinationRotation); \
	DECLARE_FUNCTION(execGetDestinationLocation); \
	DECLARE_FUNCTION(execGetCurrentEnvironment); \
	DECLARE_FUNCTION(execGetPortalType); \
	DECLARE_FUNCTION(execStartFadeInEffect); \
	DECLARE_FUNCTION(execStartFadeOutEffect); \
	DECLARE_FUNCTION(execTeleportPlayerToDestination); \
	DECLARE_FUNCTION(execApplyTeleportEffect); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execSetPortalVisibility); \
	DECLARE_FUNCTION(execDeactivatePortal); \
	DECLARE_FUNCTION(execActivatePortal); \
	DECLARE_FUNCTION(execCreateUmbralPortal); \
	DECLARE_FUNCTION(execCreateZephyrPortal); \
	DECLARE_FUNCTION(execCreateRadiantPortal); \
	DECLARE_FUNCTION(execInitializePortalWithType); \
	DECLARE_FUNCTION(execInitializePortal);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPortal_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h_126_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGPortal(); \
	friend struct Z_Construct_UClass_AAURACRONPCGPortal_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPortal_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGPortal, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGPortal_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGPortal)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h_126_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGPortal(AAURACRONPCGPortal&&) = delete; \
	AAURACRONPCGPortal(const AAURACRONPCGPortal&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGPortal); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGPortal); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGPortal) \
	NO_API virtual ~AAURACRONPCGPortal();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h_123_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h_126_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h_126_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h_126_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h_126_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGPortal;

// ********** End Class AAURACRONPCGPortal *********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h

// ********** Begin Enum EAURACRONPortalType *******************************************************
#define FOREACH_ENUM_EAURACRONPORTALTYPE(op) \
	op(EAURACRONPortalType::RadiantPlains) \
	op(EAURACRONPortalType::ZephyrFirmament) \
	op(EAURACRONPortalType::PurgatoryRealm) 

enum class EAURACRONPortalType : uint8;
template<> struct TIsUEnumClass<EAURACRONPortalType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONPortalType>();
// ********** End Enum EAURACRONPortalType *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
