// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGTypes.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGTypes() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONMapTypeConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAURACRONMapTacticalAdvantages ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages;
class UScriptStruct* FAURACRONMapTacticalAdvantages::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONMapTacticalAdvantages"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para vantagens t\xc3\xa1ticas espec\xc3\xad""ficas de cada mapa\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para vantagens t\xc3\xa1ticas espec\xc3\xad""ficas de cada mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeedBonus_MetaData[] = {
		{ "Category", "AURACRONMapTacticalAdvantages" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** B\xc3\xb4nus de velocidade de movimento */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\xb4nus de velocidade de movimento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityRangeBonus_MetaData[] = {
		{ "Category", "AURACRONMapTacticalAdvantages" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** B\xc3\xb4nus de alcance de habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\xb4nus de alcance de habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AreaOfEffectBonus_MetaData[] = {
		{ "Category", "AURACRONMapTacticalAdvantages" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** B\xc3\xb4nus de \xc3\xa1rea de efeito */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\xb4nus de \xc3\xa1rea de efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisionRangeBonus_MetaData[] = {
		{ "Category", "AURACRONMapTacticalAdvantages" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** B\xc3\xb4nus de vis\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\xb4nus de vis\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceRegenBonus_MetaData[] = {
		{ "Category", "AURACRONMapTacticalAdvantages" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** B\xc3\xb4nus de regenera\xc3\xa7\xc3\xa3o de recursos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\xb4nus de regenera\xc3\xa7\xc3\xa3o de recursos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpecificMechanics_MetaData[] = {
		{ "Category", "AURACRONMapTacticalAdvantages" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mec\xc3\xa2nicas espec\xc3\xad""ficas do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mec\xc3\xa2nicas espec\xc3\xad""ficas do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "AURACRONMapTacticalAdvantages" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\xa7\xc3\xa3o das vantagens t\xc3\xa1ticas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\xa7\xc3\xa3o das vantagens t\xc3\xa1ticas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeedBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityRangeBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AreaOfEffectBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisionRangeBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ResourceRegenBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpecificMechanics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SpecificMechanics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_SpecificMechanics;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONMapTacticalAdvantages>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_MovementSpeedBonus = { "MovementSpeedBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTacticalAdvantages, MovementSpeedBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeedBonus_MetaData), NewProp_MovementSpeedBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_AbilityRangeBonus = { "AbilityRangeBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTacticalAdvantages, AbilityRangeBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityRangeBonus_MetaData), NewProp_AbilityRangeBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_AreaOfEffectBonus = { "AreaOfEffectBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTacticalAdvantages, AreaOfEffectBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AreaOfEffectBonus_MetaData), NewProp_AreaOfEffectBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_VisionRangeBonus = { "VisionRangeBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTacticalAdvantages, VisionRangeBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisionRangeBonus_MetaData), NewProp_VisionRangeBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_ResourceRegenBonus = { "ResourceRegenBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTacticalAdvantages, ResourceRegenBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceRegenBonus_MetaData), NewProp_ResourceRegenBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_SpecificMechanics_ValueProp = { "SpecificMechanics", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_SpecificMechanics_Key_KeyProp = { "SpecificMechanics_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_SpecificMechanics = { "SpecificMechanics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTacticalAdvantages, SpecificMechanics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpecificMechanics_MetaData), NewProp_SpecificMechanics_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTacticalAdvantages, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_MovementSpeedBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_AbilityRangeBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_AreaOfEffectBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_VisionRangeBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_ResourceRegenBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_SpecificMechanics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_SpecificMechanics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_SpecificMechanics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_Description,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONMapTacticalAdvantages",
	Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::PropPointers),
	sizeof(FAURACRONMapTacticalAdvantages),
	alignof(FAURACRONMapTacticalAdvantages),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONMapTacticalAdvantages **************************************

// ********** Begin ScriptStruct FAURACRONMapTypeConfig ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONMapTypeConfig;
class UScriptStruct* FAURACRONMapTypeConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONMapTypeConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONMapTypeConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONMapTypeConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONMapTypeConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONMapTypeConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas de cada tipo de mapa\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas de cada tipo de mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentType_MetaData[] = {
		{ "Category", "AURACRONMapTypeConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de ambiente do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de ambiente do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Layout_MetaData[] = {
		{ "Category", "AURACRONMapTypeConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Layout do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layout do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpecialResources_MetaData[] = {
		{ "Category", "AURACRONMapTypeConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recursos especiais do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recursos especiais do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Objectives_MetaData[] = {
		{ "Category", "AURACRONMapTypeConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Objetivos espec\xc3\xad""ficos do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objetivos espec\xc3\xad""ficos do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrategicAdvantage_MetaData[] = {
		{ "Category", "AURACRONMapTypeConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Vantagens estrat\xc3\xa9gicas do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vantagens estrat\xc3\xa9gicas do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TacticalAdvantages_MetaData[] = {
		{ "Category", "AURACRONMapTypeConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Vantagens t\xc3\xa1ticas espec\xc3\xad""ficas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vantagens t\xc3\xa1ticas espec\xc3\xad""ficas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseHeight_MetaData[] = {
		{ "Category", "AURACRONMapTypeConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Altura base do terreno */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Altura base do terreno" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DominantColor_MetaData[] = {
		{ "Category", "AURACRONMapTypeConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor dominante do ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor dominante do ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Layout;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SpecialResources_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpecialResources;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Objectives_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Objectives;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StrategicAdvantage;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TacticalAdvantages;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseHeight;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DominantColor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONMapTypeConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_EnvironmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTypeConfig, EnvironmentType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentType_MetaData), NewProp_EnvironmentType_MetaData) }; // 2161956974
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_Layout = { "Layout", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTypeConfig, Layout), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Layout_MetaData), NewProp_Layout_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_SpecialResources_Inner = { "SpecialResources", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_SpecialResources = { "SpecialResources", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTypeConfig, SpecialResources), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpecialResources_MetaData), NewProp_SpecialResources_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_Objectives_Inner = { "Objectives", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_Objectives = { "Objectives", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTypeConfig, Objectives), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Objectives_MetaData), NewProp_Objectives_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_StrategicAdvantage = { "StrategicAdvantage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTypeConfig, StrategicAdvantage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrategicAdvantage_MetaData), NewProp_StrategicAdvantage_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_TacticalAdvantages = { "TacticalAdvantages", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTypeConfig, TacticalAdvantages), Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TacticalAdvantages_MetaData), NewProp_TacticalAdvantages_MetaData) }; // 3590097867
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_BaseHeight = { "BaseHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTypeConfig, BaseHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseHeight_MetaData), NewProp_BaseHeight_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_DominantColor = { "DominantColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTypeConfig, DominantColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DominantColor_MetaData), NewProp_DominantColor_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_EnvironmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_EnvironmentType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_Layout,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_SpecialResources_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_SpecialResources,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_Objectives_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_Objectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_StrategicAdvantage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_TacticalAdvantages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_BaseHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewProp_DominantColor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONMapTypeConfig",
	Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::PropPointers),
	sizeof(FAURACRONMapTypeConfig),
	alignof(FAURACRONMapTypeConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONMapTypeConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONMapTypeConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONMapTypeConfig.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONMapTypeConfig.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONMapTypeConfig **********************************************

// ********** Begin ScriptStruct FAURACRONVisualEffectSettings *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONVisualEffectSettings;
class UScriptStruct* FAURACRONVisualEffectSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONVisualEffectSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONVisualEffectSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONVisualEffectSettings"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONVisualEffectSettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\xa7\xc3\xb5""es de efeitos visuais\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xb5""es de efeitos visuais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Intensity_MetaData[] = {
		{ "Category", "AURACRONVisualEffectSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Duration_MetaData[] = {
		{ "Category", "AURACRONVisualEffectSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o do efeito em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do efeito em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityScale_MetaData[] = {
		{ "Category", "AURACRONVisualEffectSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala de qualidade para ajuste de performance */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala de qualidade para ajuste de performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrimaryColor_MetaData[] = {
		{ "Category", "AURACRONVisualEffectSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor principal do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor principal do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SecondaryColor_MetaData[] = {
		{ "Category", "AURACRONVisualEffectSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor secund\xc3\xa1ria do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor secund\xc3\xa1ria do efeito" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QualityScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PrimaryColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SecondaryColor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONVisualEffectSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONVisualEffectSettings, Intensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Intensity_MetaData), NewProp_Intensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONVisualEffectSettings, Duration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Duration_MetaData), NewProp_Duration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::NewProp_QualityScale = { "QualityScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONVisualEffectSettings, QualityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityScale_MetaData), NewProp_QualityScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::NewProp_PrimaryColor = { "PrimaryColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONVisualEffectSettings, PrimaryColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrimaryColor_MetaData), NewProp_PrimaryColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::NewProp_SecondaryColor = { "SecondaryColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONVisualEffectSettings, SecondaryColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SecondaryColor_MetaData), NewProp_SecondaryColor_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::NewProp_QualityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::NewProp_PrimaryColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::NewProp_SecondaryColor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONVisualEffectSettings",
	Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::PropPointers),
	sizeof(FAURACRONVisualEffectSettings),
	alignof(FAURACRONVisualEffectSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONVisualEffectSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONVisualEffectSettings.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONVisualEffectSettings.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONVisualEffectSettings ***************************************

// ********** Begin ScriptStruct FAURACRONEnvironmentConfig ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentConfig;
class UScriptStruct* FAURACRONEnvironmentConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONEnvironmentConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\xa7\xc3\xb5""es de ambiente\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xb5""es de ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentType_MetaData[] = {
		{ "Category", "AURACRONEnvironmentConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPhase_MetaData[] = {
		{ "Category", "AURACRONEnvironmentConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientLightIntensity_MetaData[] = {
		{ "Category", "AURACRONEnvironmentConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade da ilumina\xc3\xa7\xc3\xa3o ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da ilumina\xc3\xa7\xc3\xa3o ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientLightColor_MetaData[] = {
		{ "Category", "AURACRONEnvironmentConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor da ilumina\xc3\xa7\xc3\xa3o ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor da ilumina\xc3\xa7\xc3\xa3o ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrimaryColor_MetaData[] = {
		{ "Category", "AURACRONEnvironmentConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor prim\xc3\xa1ria do ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor prim\xc3\xa1ria do ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightIntensity_MetaData[] = {
		{ "Category", "AURACRONEnvironmentConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade da luz */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da luz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialRoughness_MetaData[] = {
		{ "Category", "AURACRONEnvironmentConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rugosidade do material */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rugosidade do material" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivityScale_MetaData[] = {
		{ "Category", "AURACRONEnvironmentConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala de atividade */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala de atividade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FogIntensity_MetaData[] = {
		{ "Category", "AURACRONEnvironmentConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade da n\xc3\xa9voa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da n\xc3\xa9voa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FogColor_MetaData[] = {
		{ "Category", "AURACRONEnvironmentConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor da n\xc3\xa9voa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTypes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor da n\xc3\xa9voa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AmbientLightIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AmbientLightColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PrimaryColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LightIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaterialRoughness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivityScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FogIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FogColor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONEnvironmentConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_EnvironmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentConfig, EnvironmentType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentType_MetaData), NewProp_EnvironmentType_MetaData) }; // 2161956974
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_CurrentPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_CurrentPhase = { "CurrentPhase", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentConfig, CurrentPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPhase_MetaData), NewProp_CurrentPhase_MetaData) }; // 2541365769
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_AmbientLightIntensity = { "AmbientLightIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentConfig, AmbientLightIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientLightIntensity_MetaData), NewProp_AmbientLightIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_AmbientLightColor = { "AmbientLightColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentConfig, AmbientLightColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientLightColor_MetaData), NewProp_AmbientLightColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_PrimaryColor = { "PrimaryColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentConfig, PrimaryColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrimaryColor_MetaData), NewProp_PrimaryColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_LightIntensity = { "LightIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentConfig, LightIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightIntensity_MetaData), NewProp_LightIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_MaterialRoughness = { "MaterialRoughness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentConfig, MaterialRoughness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialRoughness_MetaData), NewProp_MaterialRoughness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_ActivityScale = { "ActivityScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentConfig, ActivityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivityScale_MetaData), NewProp_ActivityScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_FogIntensity = { "FogIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentConfig, FogIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FogIntensity_MetaData), NewProp_FogIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_FogColor = { "FogColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentConfig, FogColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FogColor_MetaData), NewProp_FogColor_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_EnvironmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_EnvironmentType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_CurrentPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_CurrentPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_AmbientLightIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_AmbientLightColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_PrimaryColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_LightIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_MaterialRoughness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_ActivityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_FogIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewProp_FogColor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONEnvironmentConfig",
	Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::PropPointers),
	sizeof(FAURACRONEnvironmentConfig),
	alignof(FAURACRONEnvironmentConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentConfig.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentConfig.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONEnvironmentConfig ******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTypes_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONMapTacticalAdvantages::StaticStruct, Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewStructOps, TEXT("AURACRONMapTacticalAdvantages"), &Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONMapTacticalAdvantages), 3590097867U) },
		{ FAURACRONMapTypeConfig::StaticStruct, Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics::NewStructOps, TEXT("AURACRONMapTypeConfig"), &Z_Registration_Info_UScriptStruct_FAURACRONMapTypeConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONMapTypeConfig), 2407115336U) },
		{ FAURACRONVisualEffectSettings::StaticStruct, Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics::NewStructOps, TEXT("AURACRONVisualEffectSettings"), &Z_Registration_Info_UScriptStruct_FAURACRONVisualEffectSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONVisualEffectSettings), 2145445234U) },
		{ FAURACRONEnvironmentConfig::StaticStruct, Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics::NewStructOps, TEXT("AURACRONEnvironmentConfig"), &Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONEnvironmentConfig), 922655277U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTypes_h__Script_AURACRON_4226329493(TEXT("/Script/AURACRON"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTypes_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTypes_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
