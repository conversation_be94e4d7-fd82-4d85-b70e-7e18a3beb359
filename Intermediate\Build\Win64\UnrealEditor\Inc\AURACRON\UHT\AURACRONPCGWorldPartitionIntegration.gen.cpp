// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGWorldPartitionIntegration.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGWorldPartitionIntegration() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FGuid();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UCurveFloat_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorldPartition_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FStreamingSourceShape();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAURACRONStreamingQualityProfile ******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile;
static UEnum* EAURACRONStreamingQualityProfile_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONStreamingQualityProfile"));
	}
	return Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONStreamingQualityProfile>()
{
	return EAURACRONStreamingQualityProfile_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Perfis de qualidade para streaming */" },
#endif
		{ "Custom.DisplayName", "Personalizado" },
		{ "Custom.Name", "EAURACRONStreamingQualityProfile::Custom" },
		{ "High.DisplayName", "Alta Qualidade" },
		{ "High.Name", "EAURACRONStreamingQualityProfile::High" },
		{ "Low.DisplayName", "Baixa Qualidade" },
		{ "Low.Name", "EAURACRONStreamingQualityProfile::Low" },
		{ "Medium.DisplayName", "M\xc3\xa9""dia Qualidade" },
		{ "Medium.Name", "EAURACRONStreamingQualityProfile::Medium" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Perfis de qualidade para streaming" },
#endif
		{ "Ultra.DisplayName", "Ultra Qualidade" },
		{ "Ultra.Name", "EAURACRONStreamingQualityProfile::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONStreamingQualityProfile::Low", (int64)EAURACRONStreamingQualityProfile::Low },
		{ "EAURACRONStreamingQualityProfile::Medium", (int64)EAURACRONStreamingQualityProfile::Medium },
		{ "EAURACRONStreamingQualityProfile::High", (int64)EAURACRONStreamingQualityProfile::High },
		{ "EAURACRONStreamingQualityProfile::Ultra", (int64)EAURACRONStreamingQualityProfile::Ultra },
		{ "EAURACRONStreamingQualityProfile::Custom", (int64)EAURACRONStreamingQualityProfile::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONStreamingQualityProfile",
	"EAURACRONStreamingQualityProfile",
	Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile()
{
	if (!Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile.InnerSingleton;
}
// ********** End Enum EAURACRONStreamingQualityProfile ********************************************

// ********** Begin ScriptStruct FAURACRONStreamingPerformanceStats ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONStreamingPerformanceStats;
class UScriptStruct* FAURACRONStreamingPerformanceStats::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONStreamingPerformanceStats.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONStreamingPerformanceStats.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONStreamingPerformanceStats"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONStreamingPerformanceStats.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estat\xc3\xadsticas de desempenho de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estat\xc3\xadsticas de desempenho de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveStreamingElements_MetaData[] = {
		{ "Category", "Streaming|Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero de elementos atualmente em streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero de elementos atualmente em streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Streaming|Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mem\xc3\xb3ria total utilizada pelo streaming (em MB) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mem\xc3\xb3ria total utilizada pelo streaming (em MB)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageLoadTimeMS_MetaData[] = {
		{ "Category", "Streaming|Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo m\xc3\xa9""dio de carregamento (em ms) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo m\xc3\xa9""dio de carregamento (em ms)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingHitchCount_MetaData[] = {
		{ "Category", "Streaming|Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero de hitches detectados durante streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero de hitches detectados durante streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeSinceLastUpdate_MetaData[] = {
		{ "Category", "Streaming|Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo desde a \xc3\xbaltima atualiza\xc3\xa7\xc3\xa3o de estat\xc3\xadsticas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo desde a \xc3\xbaltima atualiza\xc3\xa7\xc3\xa3o de estat\xc3\xadsticas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementsLoadedLastUpdate_MetaData[] = {
		{ "Category", "Streaming|Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero de elementos carregados na \xc3\xbaltima atualiza\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero de elementos carregados na \xc3\xbaltima atualiza\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementsUnloadedLastUpdate_MetaData[] = {
		{ "Category", "Streaming|Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero de elementos descarregados na \xc3\xbaltima atualiza\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero de elementos descarregados na \xc3\xbaltima atualiza\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveStreamingElements;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageLoadTimeMS;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamingHitchCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeSinceLastUpdate;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ElementsLoadedLastUpdate;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ElementsUnloadedLastUpdate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONStreamingPerformanceStats>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::NewProp_ActiveStreamingElements = { "ActiveStreamingElements", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONStreamingPerformanceStats, ActiveStreamingElements), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveStreamingElements_MetaData), NewProp_ActiveStreamingElements_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONStreamingPerformanceStats, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::NewProp_AverageLoadTimeMS = { "AverageLoadTimeMS", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONStreamingPerformanceStats, AverageLoadTimeMS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageLoadTimeMS_MetaData), NewProp_AverageLoadTimeMS_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::NewProp_StreamingHitchCount = { "StreamingHitchCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONStreamingPerformanceStats, StreamingHitchCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingHitchCount_MetaData), NewProp_StreamingHitchCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::NewProp_TimeSinceLastUpdate = { "TimeSinceLastUpdate", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONStreamingPerformanceStats, TimeSinceLastUpdate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeSinceLastUpdate_MetaData), NewProp_TimeSinceLastUpdate_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::NewProp_ElementsLoadedLastUpdate = { "ElementsLoadedLastUpdate", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONStreamingPerformanceStats, ElementsLoadedLastUpdate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementsLoadedLastUpdate_MetaData), NewProp_ElementsLoadedLastUpdate_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::NewProp_ElementsUnloadedLastUpdate = { "ElementsUnloadedLastUpdate", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONStreamingPerformanceStats, ElementsUnloadedLastUpdate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementsUnloadedLastUpdate_MetaData), NewProp_ElementsUnloadedLastUpdate_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::NewProp_ActiveStreamingElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::NewProp_AverageLoadTimeMS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::NewProp_StreamingHitchCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::NewProp_TimeSinceLastUpdate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::NewProp_ElementsLoadedLastUpdate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::NewProp_ElementsUnloadedLastUpdate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONStreamingPerformanceStats",
	Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::PropPointers),
	sizeof(FAURACRONStreamingPerformanceStats),
	alignof(FAURACRONStreamingPerformanceStats),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONStreamingPerformanceStats.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONStreamingPerformanceStats.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONStreamingPerformanceStats.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONStreamingPerformanceStats **********************************

// ********** Begin ScriptStruct FAURACRONHardwareStreamingConfig **********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONHardwareStreamingConfig;
class UScriptStruct* FAURACRONHardwareStreamingConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONHardwareStreamingConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONHardwareStreamingConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONHardwareStreamingConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONHardwareStreamingConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es de hardware para streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de hardware para streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityProfile_MetaData[] = {
		{ "Category", "Streaming|Hardware" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Perfil de qualidade atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Perfil de qualidade atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HardwareDistanceMultiplier_MetaData[] = {
		{ "Category", "Streaming|Hardware" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de dist\xc3\xa2ncia de streaming baseado no hardware */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de dist\xc3\xa2ncia de streaming baseado no hardware" },
#endif
		{ "UIMax", "2.0" },
		{ "UIMin", "0.1" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsLowEndHardware_MetaData[] = {
		{ "Category", "Streaming|Hardware" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Indica se o hardware \xc3\xa9 considerado de baixo desempenho */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Indica se o hardware \xc3\xa9 considerado de baixo desempenho" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryBudgetMB_MetaData[] = {
		{ "Category", "Streaming|Hardware" },
		{ "ClampMin", "64" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Limite de mem\xc3\xb3ria para streaming (em MB) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limite de mem\xc3\xb3ria para streaming (em MB)" },
#endif
		{ "UIMin", "64" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentStreamingElements_MetaData[] = {
		{ "Category", "Streaming|Hardware" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xa1ximo de elementos em streaming simult\xc3\xa2neo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de elementos em streaming simult\xc3\xa2neo" },
#endif
		{ "UIMin", "1" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAsyncStreaming_MetaData[] = {
		{ "Category", "Streaming|Hardware" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar streaming ass\xc3\xadncrono */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar streaming ass\xc3\xadncrono" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPrioritizeVisibleElements_MetaData[] = {
		{ "Category", "Streaming|Hardware" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Priorizar streaming baseado em visibilidade */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Priorizar streaming baseado em visibilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDynamicLODs_MetaData[] = {
		{ "Category", "Streaming|Hardware" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar LODs din\xc3\xa2micos baseados em hardware */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar LODs din\xc3\xa2micos baseados em hardware" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseLODLevel_MetaData[] = {
		{ "Category", "Streaming|Hardware" },
		{ "ClampMax", "5" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xadvel de LOD base para hardware atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xadvel de LOD base para hardware atual" },
#endif
		{ "UIMax", "5" },
		{ "UIMin", "0" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_QualityProfile_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_QualityProfile;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HardwareDistanceMultiplier;
	static void NewProp_bIsLowEndHardware_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsLowEndHardware;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MemoryBudgetMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentStreamingElements;
	static void NewProp_bUseAsyncStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAsyncStreaming;
	static void NewProp_bPrioritizeVisibleElements_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPrioritizeVisibleElements;
	static void NewProp_bUseDynamicLODs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDynamicLODs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BaseLODLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONHardwareStreamingConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_QualityProfile_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_QualityProfile = { "QualityProfile", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONHardwareStreamingConfig, QualityProfile), Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityProfile_MetaData), NewProp_QualityProfile_MetaData) }; // 1554993972
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_HardwareDistanceMultiplier = { "HardwareDistanceMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONHardwareStreamingConfig, HardwareDistanceMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HardwareDistanceMultiplier_MetaData), NewProp_HardwareDistanceMultiplier_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bIsLowEndHardware_SetBit(void* Obj)
{
	((FAURACRONHardwareStreamingConfig*)Obj)->bIsLowEndHardware = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bIsLowEndHardware = { "bIsLowEndHardware", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONHardwareStreamingConfig), &Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bIsLowEndHardware_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsLowEndHardware_MetaData), NewProp_bIsLowEndHardware_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_MemoryBudgetMB = { "MemoryBudgetMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONHardwareStreamingConfig, MemoryBudgetMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryBudgetMB_MetaData), NewProp_MemoryBudgetMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_MaxConcurrentStreamingElements = { "MaxConcurrentStreamingElements", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONHardwareStreamingConfig, MaxConcurrentStreamingElements), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentStreamingElements_MetaData), NewProp_MaxConcurrentStreamingElements_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bUseAsyncStreaming_SetBit(void* Obj)
{
	((FAURACRONHardwareStreamingConfig*)Obj)->bUseAsyncStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bUseAsyncStreaming = { "bUseAsyncStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONHardwareStreamingConfig), &Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bUseAsyncStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAsyncStreaming_MetaData), NewProp_bUseAsyncStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bPrioritizeVisibleElements_SetBit(void* Obj)
{
	((FAURACRONHardwareStreamingConfig*)Obj)->bPrioritizeVisibleElements = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bPrioritizeVisibleElements = { "bPrioritizeVisibleElements", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONHardwareStreamingConfig), &Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bPrioritizeVisibleElements_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPrioritizeVisibleElements_MetaData), NewProp_bPrioritizeVisibleElements_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bUseDynamicLODs_SetBit(void* Obj)
{
	((FAURACRONHardwareStreamingConfig*)Obj)->bUseDynamicLODs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bUseDynamicLODs = { "bUseDynamicLODs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONHardwareStreamingConfig), &Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bUseDynamicLODs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDynamicLODs_MetaData), NewProp_bUseDynamicLODs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_BaseLODLevel = { "BaseLODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONHardwareStreamingConfig, BaseLODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseLODLevel_MetaData), NewProp_BaseLODLevel_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_QualityProfile_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_QualityProfile,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_HardwareDistanceMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bIsLowEndHardware,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_MemoryBudgetMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_MaxConcurrentStreamingElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bUseAsyncStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bPrioritizeVisibleElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_bUseDynamicLODs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewProp_BaseLODLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONHardwareStreamingConfig",
	Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::PropPointers),
	sizeof(FAURACRONHardwareStreamingConfig),
	alignof(FAURACRONHardwareStreamingConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONHardwareStreamingConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONHardwareStreamingConfig.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONHardwareStreamingConfig.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONHardwareStreamingConfig ************************************

// ********** Begin ScriptStruct FAURACRONPCGStreamingConfig ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig;
class UScriptStruct* FAURACRONPCGStreamingConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONPCGStreamingConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xb5""es de streaming para elementos PCG\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de streaming para elementos PCG" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingDistance_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia de carregamento */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia de carregamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnloadingDistance_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia de descarregamento */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia de descarregamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingPriority_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
		{ "ClampMax", "100" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAsyncStreaming_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve usar streaming ass\xc3\xadncrono */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve usar streaming ass\xc3\xadncrono" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridSize_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho do grid de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho do grid de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingDistance_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bStartActive_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve come\xc3\xa7""ar ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve come\xc3\xa7""ar ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODLevel_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
		{ "ClampMax", "3" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xadvel de detalhe para streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xadvel de detalhe para streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOptimizeForLowEndHardware_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Otimiza\xc3\xa7\xc3\xa3o para hardware de baixo desempenho */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Otimiza\xc3\xa7\xc3\xa3o para hardware de baixo desempenho" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoadingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UnloadingDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamingPriority;
	static void NewProp_bUseAsyncStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAsyncStreaming;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GridSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingDistance;
	static void NewProp_bStartActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bStartActive;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static void NewProp_bOptimizeForLowEndHardware_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOptimizeForLowEndHardware;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONPCGStreamingConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_LoadingDistance = { "LoadingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingConfig, LoadingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingDistance_MetaData), NewProp_LoadingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_UnloadingDistance = { "UnloadingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingConfig, UnloadingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnloadingDistance_MetaData), NewProp_UnloadingDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_StreamingPriority = { "StreamingPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingConfig, StreamingPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingPriority_MetaData), NewProp_StreamingPriority_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bUseAsyncStreaming_SetBit(void* Obj)
{
	((FAURACRONPCGStreamingConfig*)Obj)->bUseAsyncStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bUseAsyncStreaming = { "bUseAsyncStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGStreamingConfig), &Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bUseAsyncStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAsyncStreaming_MetaData), NewProp_bUseAsyncStreaming_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_GridSize = { "GridSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingConfig, GridSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridSize_MetaData), NewProp_GridSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_StreamingDistance = { "StreamingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingConfig, StreamingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingDistance_MetaData), NewProp_StreamingDistance_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bStartActive_SetBit(void* Obj)
{
	((FAURACRONPCGStreamingConfig*)Obj)->bStartActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bStartActive = { "bStartActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGStreamingConfig), &Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bStartActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bStartActive_MetaData), NewProp_bStartActive_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingConfig, LODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODLevel_MetaData), NewProp_LODLevel_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bOptimizeForLowEndHardware_SetBit(void* Obj)
{
	((FAURACRONPCGStreamingConfig*)Obj)->bOptimizeForLowEndHardware = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bOptimizeForLowEndHardware = { "bOptimizeForLowEndHardware", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGStreamingConfig), &Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bOptimizeForLowEndHardware_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOptimizeForLowEndHardware_MetaData), NewProp_bOptimizeForLowEndHardware_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_LoadingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_UnloadingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_StreamingPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bUseAsyncStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_GridSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_StreamingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bStartActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bOptimizeForLowEndHardware,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONPCGStreamingConfig",
	Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::PropPointers),
	sizeof(FAURACRONPCGStreamingConfig),
	alignof(FAURACRONPCGStreamingConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONPCGStreamingConfig *****************************************

// ********** Begin ScriptStruct FAURACRONPCGWorldPartitionStreamingConfig *************************
static_assert(std::is_polymorphic<FAURACRONPCGWorldPartitionStreamingConfig>() == std::is_polymorphic<FAURACRONPCGStreamingConfig>(), "USTRUCT FAURACRONPCGWorldPartitionStreamingConfig cannot be polymorphic unless super FAURACRONPCGStreamingConfig is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig;
class UScriptStruct* FAURACRONPCGWorldPartitionStreamingConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONPCGWorldPartitionStreamingConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xb5""es de streaming espec\xc3\xad""ficas para World Partition UE 5.6\n * Usa FStreamingSourceShape ao inv\xc3\xa9s de EStreamingSourceShapeType\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de streaming espec\xc3\xad""ficas para World Partition UE 5.6\nUsa FStreamingSourceShape ao inv\xc3\xa9s de EStreamingSourceShapeType" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingShape_MetaData[] = {
		{ "Category", "AURACRONPCGWorldPartitionStreamingConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xa3o da forma de streaming usando UE 5.6 API */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o da forma de streaming usando UE 5.6 API" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShapeRotation_MetaData[] = {
		{ "Category", "AURACRONPCGWorldPartitionStreamingConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rota\xc3\xa7\xc3\xa3o da forma de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rota\xc3\xa7\xc3\xa3o da forma de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseVisibilityStreaming_MetaData[] = {
		{ "Category", "AURACRONPCGWorldPartitionStreamingConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve usar streaming baseado em visibilidade */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve usar streaming baseado em visibilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseGameplayStreaming_MetaData[] = {
		{ "Category", "AURACRONPCGWorldPartitionStreamingConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve usar streaming baseado em gameplay */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve usar streaming baseado em gameplay" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPartitionStreamingPriority_MetaData[] = {
		{ "Category", "AURACRONPCGWorldPartitionStreamingConfig" },
		{ "ClampMax", "100" },
		{ "ClampMin", "-100" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade de streaming para World Partition */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade de streaming para World Partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlatformSpecificSettings_MetaData[] = {
		{ "Category", "AURACRONPCGWorldPartitionStreamingConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Otimiza\xc3\xa7\xc3\xa3o para diferentes plataformas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Otimiza\xc3\xa7\xc3\xa3o para diferentes plataformas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingShape;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ShapeRotation;
	static void NewProp_bUseVisibilityStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseVisibilityStreaming;
	static void NewProp_bUseGameplayStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseGameplayStreaming;
	static const UECodeGen_Private::FIntPropertyParams NewProp_WorldPartitionStreamingPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlatformSpecificSettings_ValueProp;
	static const UECodeGen_Private::FNamePropertyParams NewProp_PlatformSpecificSettings_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlatformSpecificSettings;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONPCGWorldPartitionStreamingConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_StreamingShape = { "StreamingShape", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGWorldPartitionStreamingConfig, StreamingShape), Z_Construct_UScriptStruct_FStreamingSourceShape, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingShape_MetaData), NewProp_StreamingShape_MetaData) }; // 1551689013
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_ShapeRotation = { "ShapeRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGWorldPartitionStreamingConfig, ShapeRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShapeRotation_MetaData), NewProp_ShapeRotation_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_bUseVisibilityStreaming_SetBit(void* Obj)
{
	((FAURACRONPCGWorldPartitionStreamingConfig*)Obj)->bUseVisibilityStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_bUseVisibilityStreaming = { "bUseVisibilityStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGWorldPartitionStreamingConfig), &Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_bUseVisibilityStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseVisibilityStreaming_MetaData), NewProp_bUseVisibilityStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_bUseGameplayStreaming_SetBit(void* Obj)
{
	((FAURACRONPCGWorldPartitionStreamingConfig*)Obj)->bUseGameplayStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_bUseGameplayStreaming = { "bUseGameplayStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGWorldPartitionStreamingConfig), &Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_bUseGameplayStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseGameplayStreaming_MetaData), NewProp_bUseGameplayStreaming_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_WorldPartitionStreamingPriority = { "WorldPartitionStreamingPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGWorldPartitionStreamingConfig, WorldPartitionStreamingPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPartitionStreamingPriority_MetaData), NewProp_WorldPartitionStreamingPriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_PlatformSpecificSettings_ValueProp = { "PlatformSpecificSettings", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_PlatformSpecificSettings_Key_KeyProp = { "PlatformSpecificSettings_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_PlatformSpecificSettings = { "PlatformSpecificSettings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGWorldPartitionStreamingConfig, PlatformSpecificSettings), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlatformSpecificSettings_MetaData), NewProp_PlatformSpecificSettings_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_StreamingShape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_ShapeRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_bUseVisibilityStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_bUseGameplayStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_WorldPartitionStreamingPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_PlatformSpecificSettings_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_PlatformSpecificSettings_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewProp_PlatformSpecificSettings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig,
	&NewStructOps,
	"AURACRONPCGWorldPartitionStreamingConfig",
	Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::PropPointers),
	sizeof(FAURACRONPCGWorldPartitionStreamingConfig),
	alignof(FAURACRONPCGWorldPartitionStreamingConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONPCGWorldPartitionStreamingConfig ***************************

// ********** Begin ScriptStruct FAURACRONPCGStreamingEntry ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry;
class UScriptStruct* FAURACRONPCGStreamingEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONPCGStreamingEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Entrada de streaming para elemento PCG\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Entrada de streaming para elemento PCG" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGElement_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingEntry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elemento PCG */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elemento PCG" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingConfig_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingEntry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xa3o de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCurrentlyStreamed_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingEntry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se est\xc3\xa1 atualmente sendo streamed */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se est\xc3\xa1 atualmente sendo streamed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingEntry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x9altimo tempo de atualiza\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x9altimo tempo de atualiza\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGElement;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingConfig;
	static void NewProp_bIsCurrentlyStreamed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCurrentlyStreamed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONPCGStreamingEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_PCGElement = { "PCGElement", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingEntry, PCGElement), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGElement_MetaData), NewProp_PCGElement_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_StreamingConfig = { "StreamingConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingEntry, StreamingConfig), Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingConfig_MetaData), NewProp_StreamingConfig_MetaData) }; // 3330627406
void Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_bIsCurrentlyStreamed_SetBit(void* Obj)
{
	((FAURACRONPCGStreamingEntry*)Obj)->bIsCurrentlyStreamed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_bIsCurrentlyStreamed = { "bIsCurrentlyStreamed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGStreamingEntry), &Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_bIsCurrentlyStreamed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCurrentlyStreamed_MetaData), NewProp_bIsCurrentlyStreamed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingEntry, LastUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_PCGElement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_StreamingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_bIsCurrentlyStreamed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONPCGStreamingEntry",
	Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::PropPointers),
	sizeof(FAURACRONPCGStreamingEntry),
	alignof(FAURACRONPCGStreamingEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONPCGStreamingEntry ******************************************

// ********** Begin ScriptStruct FAURACRONPCGStreamingRegion ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion;
class UScriptStruct* FAURACRONPCGStreamingRegion::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONPCGStreamingRegion"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Regi\xc3\xa3o de streaming\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regi\xc3\xa3o de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegionName_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingRegion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome da regi\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome da regi\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingRegion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Centro da regi\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Centro da regi\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Radius_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingRegion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio da regi\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio da regi\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingRegion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se a regi\xc3\xa3o est\xc3\xa1 ativa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se a regi\xc3\xa3o est\xc3\xa1 ativa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RegionName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONPCGStreamingRegion>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_RegionName = { "RegionName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingRegion, RegionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegionName_MetaData), NewProp_RegionName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingRegion, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingRegion, Radius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Radius_MetaData), NewProp_Radius_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAURACRONPCGStreamingRegion*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGStreamingRegion), &Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_RegionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_bIsActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONPCGStreamingRegion",
	Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::PropPointers),
	sizeof(FAURACRONPCGStreamingRegion),
	alignof(FAURACRONPCGStreamingRegion),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONPCGStreamingRegion *****************************************

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration Function ApplyOptimizationToAllElements 
struct Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ApplyOptimizationToAllElements_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|WorldPartition|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar configura\xc3\xa7\xc3\xb5""es de otimiza\xc3\xa7\xc3\xa3o para todos os elementos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar configura\xc3\xa7\xc3\xb5""es de otimiza\xc3\xa7\xc3\xa3o para todos os elementos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ApplyOptimizationToAllElements_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration, nullptr, "ApplyOptimizationToAllElements", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ApplyOptimizationToAllElements_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ApplyOptimizationToAllElements_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ApplyOptimizationToAllElements()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ApplyOptimizationToAllElements_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGWorldPartitionIntegration::execApplyOptimizationToAllElements)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyOptimizationToAllElements();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGWorldPartitionIntegration Function ApplyOptimizationToAllElements 

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration Function ConfigureHardwareOptimizations 
struct Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations_Statics
{
	struct AURACRONPCGWorldPartitionIntegration_eventConfigureHardwareOptimizations_Parms
	{
		bool bIsLowEndHardware;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|WorldPartition|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar otimiza\xc3\xa7\xc3\xb5""es para hardware espec\xc3\xad""fico */" },
#endif
		{ "CPP_Default_bIsLowEndHardware", "false" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar otimiza\xc3\xa7\xc3\xb5""es para hardware espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsLowEndHardware_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsLowEndHardware;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations_Statics::NewProp_bIsLowEndHardware_SetBit(void* Obj)
{
	((AURACRONPCGWorldPartitionIntegration_eventConfigureHardwareOptimizations_Parms*)Obj)->bIsLowEndHardware = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations_Statics::NewProp_bIsLowEndHardware = { "bIsLowEndHardware", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGWorldPartitionIntegration_eventConfigureHardwareOptimizations_Parms), &Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations_Statics::NewProp_bIsLowEndHardware_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations_Statics::NewProp_bIsLowEndHardware,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration, nullptr, "ConfigureHardwareOptimizations", Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations_Statics::AURACRONPCGWorldPartitionIntegration_eventConfigureHardwareOptimizations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations_Statics::AURACRONPCGWorldPartitionIntegration_eventConfigureHardwareOptimizations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGWorldPartitionIntegration::execConfigureHardwareOptimizations)
{
	P_GET_UBOOL(Z_Param_bIsLowEndHardware);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureHardwareOptimizations(Z_Param_bIsLowEndHardware);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGWorldPartitionIntegration Function ConfigureHardwareOptimizations 

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration Function DetectHardwareCapabilities 
struct Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_DetectHardwareCapabilities_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|WorldPartition|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Detectar capacidades do hardware e configurar otimiza\xc3\xa7\xc3\xb5""es */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Detectar capacidades do hardware e configurar otimiza\xc3\xa7\xc3\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_DetectHardwareCapabilities_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration, nullptr, "DetectHardwareCapabilities", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_DetectHardwareCapabilities_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_DetectHardwareCapabilities_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_DetectHardwareCapabilities()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_DetectHardwareCapabilities_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGWorldPartitionIntegration::execDetectHardwareCapabilities)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DetectHardwareCapabilities();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGWorldPartitionIntegration Function DetectHardwareCapabilities **

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration Function GetHardwareStreamingDistanceMultiplier 
struct Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetHardwareStreamingDistanceMultiplier_Statics
{
	struct AURACRONPCGWorldPartitionIntegration_eventGetHardwareStreamingDistanceMultiplier_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|WorldPartition|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter multiplicador de dist\xc3\xa2ncia de streaming baseado no hardware */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter multiplicador de dist\xc3\xa2ncia de streaming baseado no hardware" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetHardwareStreamingDistanceMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGWorldPartitionIntegration_eventGetHardwareStreamingDistanceMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetHardwareStreamingDistanceMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetHardwareStreamingDistanceMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetHardwareStreamingDistanceMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetHardwareStreamingDistanceMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration, nullptr, "GetHardwareStreamingDistanceMultiplier", Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetHardwareStreamingDistanceMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetHardwareStreamingDistanceMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetHardwareStreamingDistanceMultiplier_Statics::AURACRONPCGWorldPartitionIntegration_eventGetHardwareStreamingDistanceMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetHardwareStreamingDistanceMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetHardwareStreamingDistanceMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetHardwareStreamingDistanceMultiplier_Statics::AURACRONPCGWorldPartitionIntegration_eventGetHardwareStreamingDistanceMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetHardwareStreamingDistanceMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetHardwareStreamingDistanceMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGWorldPartitionIntegration::execGetHardwareStreamingDistanceMultiplier)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetHardwareStreamingDistanceMultiplier();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGWorldPartitionIntegration Function GetHardwareStreamingDistanceMultiplier 

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration Function GetStreamingPerformanceStats 
struct Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetStreamingPerformanceStats_Statics
{
	struct AURACRONPCGWorldPartitionIntegration_eventGetStreamingPerformanceStats_Parms
	{
		FAURACRONStreamingPerformanceStats ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|WorldPartition|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter estat\xc3\xadsticas de desempenho de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estat\xc3\xadsticas de desempenho de streaming" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetStreamingPerformanceStats_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGWorldPartitionIntegration_eventGetStreamingPerformanceStats_Parms, ReturnValue), Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats, METADATA_PARAMS(0, nullptr) }; // 1612994734
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetStreamingPerformanceStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetStreamingPerformanceStats_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetStreamingPerformanceStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetStreamingPerformanceStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration, nullptr, "GetStreamingPerformanceStats", Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetStreamingPerformanceStats_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetStreamingPerformanceStats_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetStreamingPerformanceStats_Statics::AURACRONPCGWorldPartitionIntegration_eventGetStreamingPerformanceStats_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetStreamingPerformanceStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetStreamingPerformanceStats_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetStreamingPerformanceStats_Statics::AURACRONPCGWorldPartitionIntegration_eventGetStreamingPerformanceStats_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetStreamingPerformanceStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetStreamingPerformanceStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGWorldPartitionIntegration::execGetStreamingPerformanceStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAURACRONStreamingPerformanceStats*)Z_Param__Result=P_THIS->GetStreamingPerformanceStats();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGWorldPartitionIntegration Function GetStreamingPerformanceStats 

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration Function InitializeWorldPartitionIntegration 
struct Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_InitializeWorldPartitionIntegration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializar integra\xc3\xa7\xc3\xa3o com World Partition */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar integra\xc3\xa7\xc3\xa3o com World Partition" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_InitializeWorldPartitionIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration, nullptr, "InitializeWorldPartitionIntegration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_InitializeWorldPartitionIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_InitializeWorldPartitionIntegration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_InitializeWorldPartitionIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_InitializeWorldPartitionIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGWorldPartitionIntegration::execInitializeWorldPartitionIntegration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeWorldPartitionIntegration();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGWorldPartitionIntegration Function InitializeWorldPartitionIntegration 

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration Function OptimizeStreamingForCurrentPlatform 
struct Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_OptimizeStreamingForCurrentPlatform_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|WorldPartition|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Otimizar streaming para plataforma atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Otimizar streaming para plataforma atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_OptimizeStreamingForCurrentPlatform_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration, nullptr, "OptimizeStreamingForCurrentPlatform", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_OptimizeStreamingForCurrentPlatform_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_OptimizeStreamingForCurrentPlatform_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_OptimizeStreamingForCurrentPlatform()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_OptimizeStreamingForCurrentPlatform_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGWorldPartitionIntegration::execOptimizeStreamingForCurrentPlatform)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeStreamingForCurrentPlatform();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGWorldPartitionIntegration Function OptimizeStreamingForCurrentPlatform 

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration Function RegisterPCGElementForStreaming 
struct Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics
{
	struct AURACRONPCGWorldPartitionIntegration_eventRegisterPCGElementForStreaming_Parms
	{
		AActor* Element;
		FAURACRONPCGStreamingConfig Config;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Registrar elemento PCG para streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar elemento PCG para streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Element;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::NewProp_Element = { "Element", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGWorldPartitionIntegration_eventRegisterPCGElementForStreaming_Parms, Element), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGWorldPartitionIntegration_eventRegisterPCGElementForStreaming_Parms, Config), Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 3330627406
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::NewProp_Element,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::NewProp_Config,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration, nullptr, "RegisterPCGElementForStreaming", Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::AURACRONPCGWorldPartitionIntegration_eventRegisterPCGElementForStreaming_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::AURACRONPCGWorldPartitionIntegration_eventRegisterPCGElementForStreaming_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGWorldPartitionIntegration::execRegisterPCGElementForStreaming)
{
	P_GET_OBJECT(AActor,Z_Param_Element);
	P_GET_STRUCT_REF(FAURACRONPCGStreamingConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterPCGElementForStreaming(Z_Param_Element,Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGWorldPartitionIntegration Function RegisterPCGElementForStreaming 

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration Function SetStreamingQualityProfile 
struct Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile_Statics
{
	struct AURACRONPCGWorldPartitionIntegration_eventSetStreamingQualityProfile_Parms
	{
		EAURACRONStreamingQualityProfile QualityProfile;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|WorldPartition|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir perfil de qualidade para streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir perfil de qualidade para streaming" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_QualityProfile_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_QualityProfile;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile_Statics::NewProp_QualityProfile_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile_Statics::NewProp_QualityProfile = { "QualityProfile", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGWorldPartitionIntegration_eventSetStreamingQualityProfile_Parms, QualityProfile), Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile, METADATA_PARAMS(0, nullptr) }; // 1554993972
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile_Statics::NewProp_QualityProfile_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile_Statics::NewProp_QualityProfile,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration, nullptr, "SetStreamingQualityProfile", Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile_Statics::AURACRONPCGWorldPartitionIntegration_eventSetStreamingQualityProfile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile_Statics::AURACRONPCGWorldPartitionIntegration_eventSetStreamingQualityProfile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGWorldPartitionIntegration::execSetStreamingQualityProfile)
{
	P_GET_ENUM(EAURACRONStreamingQualityProfile,Z_Param_QualityProfile);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetStreamingQualityProfile(EAURACRONStreamingQualityProfile(Z_Param_QualityProfile));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGWorldPartitionIntegration Function SetStreamingQualityProfile **

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration Function UpdateStreamingForPlayerLocation 
struct Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics
{
	struct AURACRONPCGWorldPartitionIntegration_eventUpdateStreamingForPlayerLocation_Parms
	{
		FVector PlayerLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar streaming baseado na posi\xc3\xa7\xc3\xa3o do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar streaming baseado na posi\xc3\xa7\xc3\xa3o do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::NewProp_PlayerLocation = { "PlayerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGWorldPartitionIntegration_eventUpdateStreamingForPlayerLocation_Parms, PlayerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerLocation_MetaData), NewProp_PlayerLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::NewProp_PlayerLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration, nullptr, "UpdateStreamingForPlayerLocation", Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::AURACRONPCGWorldPartitionIntegration_eventUpdateStreamingForPlayerLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::AURACRONPCGWorldPartitionIntegration_eventUpdateStreamingForPlayerLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGWorldPartitionIntegration::execUpdateStreamingForPlayerLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_PlayerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateStreamingForPlayerLocation(Z_Param_Out_PlayerLocation);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGWorldPartitionIntegration Function UpdateStreamingForPlayerLocation 

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration ************************************
void AAURACRONPCGWorldPartitionIntegration::StaticRegisterNativesAAURACRONPCGWorldPartitionIntegration()
{
	UClass* Class = AAURACRONPCGWorldPartitionIntegration::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyOptimizationToAllElements", &AAURACRONPCGWorldPartitionIntegration::execApplyOptimizationToAllElements },
		{ "ConfigureHardwareOptimizations", &AAURACRONPCGWorldPartitionIntegration::execConfigureHardwareOptimizations },
		{ "DetectHardwareCapabilities", &AAURACRONPCGWorldPartitionIntegration::execDetectHardwareCapabilities },
		{ "GetHardwareStreamingDistanceMultiplier", &AAURACRONPCGWorldPartitionIntegration::execGetHardwareStreamingDistanceMultiplier },
		{ "GetStreamingPerformanceStats", &AAURACRONPCGWorldPartitionIntegration::execGetStreamingPerformanceStats },
		{ "InitializeWorldPartitionIntegration", &AAURACRONPCGWorldPartitionIntegration::execInitializeWorldPartitionIntegration },
		{ "OptimizeStreamingForCurrentPlatform", &AAURACRONPCGWorldPartitionIntegration::execOptimizeStreamingForCurrentPlatform },
		{ "RegisterPCGElementForStreaming", &AAURACRONPCGWorldPartitionIntegration::execRegisterPCGElementForStreaming },
		{ "SetStreamingQualityProfile", &AAURACRONPCGWorldPartitionIntegration::execSetStreamingQualityProfile },
		{ "UpdateStreamingForPlayerLocation", &AAURACRONPCGWorldPartitionIntegration::execUpdateStreamingForPlayerLocation },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration;
UClass* AAURACRONPCGWorldPartitionIntegration::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGWorldPartitionIntegration;
	if (!Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGWorldPartitionIntegration"),
			Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGWorldPartitionIntegration,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_NoRegister()
{
	return AAURACRONPCGWorldPartitionIntegration::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Integra\xc3\xa7\xc3\xa3o do sistema PCG com World Partition do UE 5.6\n * Gerencia streaming eficiente de conte\xc3\xba""do procedural\n * Suporta otimiza\xc3\xa7\xc3\xb5""es para diferentes hardwares\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGWorldPartitionIntegration.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integra\xc3\xa7\xc3\xa3o do sistema PCG com World Partition do UE 5.6\nGerencia streaming eficiente de conte\xc3\xba""do procedural\nSuporta otimiza\xc3\xa7\xc3\xb5""es para diferentes hardwares" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultStreamingConfig_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es padr\xc3\xa3o de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es padr\xc3\xa3o de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingRadius_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio de streaming" },
#endif
		{ "UIMin", "1000.0" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpdateInterval_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo de atualiza\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de atualiza\xc3\xa7\xc3\xa3o" },
#endif
		{ "UIMin", "0.1" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoUpdateStreaming_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve atualizar streaming automaticamente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve atualizar streaming automaticamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HardwareConfig_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es de hardware para streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de hardware para streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceStats_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estat\xc3\xadsticas de desempenho de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estat\xc3\xadsticas de desempenho de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StatsUpdateInterval_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition|Optimization" },
		{ "ClampMin", "0.5" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo de atualiza\xc3\xa7\xc3\xa3o das estat\xc3\xadsticas de desempenho (em segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de atualiza\xc3\xa7\xc3\xa3o das estat\xc3\xadsticas de desempenho (em segundos)" },
#endif
		{ "UIMin", "0.5" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDynamicOptimizations_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativar otimiza\xc3\xa7\xc3\xb5""es din\xc3\xa2micas baseadas em desempenho */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar otimiza\xc3\xa7\xc3\xb5""es din\xc3\xa2micas baseadas em desempenho" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LowFPSThreshold_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition|Optimization" },
		{ "ClampMax", "60" },
		{ "ClampMin", "15" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Limite de FPS para ativar otimiza\xc3\xa7\xc3\xb5""es adicionais */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limite de FPS para ativar otimiza\xc3\xa7\xc3\xb5""es adicionais" },
#endif
		{ "UIMax", "60" },
		{ "UIMin", "15" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FPSToStreamingDistanceCurve_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Curva de ajuste de dist\xc3\xa2ncia de streaming baseada em FPS */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Curva de ajuste de dist\xc3\xa2ncia de streaming baseada em FPS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePlatformSpecificOptimizations_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar otimiza\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas para plataforma */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar otimiza\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas para plataforma" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinTimeBetweenOptimizationAdjustments_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition|Optimization" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo m\xc3\xadnimo entre adapta\xc3\xa7\xc3\xb5""es de otimiza\xc3\xa7\xc3\xa3o (em segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo m\xc3\xadnimo entre adapta\xc3\xa7\xc3\xb5""es de otimiza\xc3\xa7\xc3\xa3o (em segundos)" },
#endif
		{ "UIMin", "1.0" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPartition_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancia ao World Partition */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancia ao World Partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingElements_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elementos registrados para streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elementos registrados para streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredElements_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elementos registrados com ID */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elementos registrados com ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingRegions_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Regi\xc3\xb5""es de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regi\xc3\xb5""es de streaming" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultStreamingConfig;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UpdateInterval;
	static void NewProp_bAutoUpdateStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoUpdateStreaming;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HardwareConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PerformanceStats;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StatsUpdateInterval;
	static void NewProp_bEnableDynamicOptimizations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDynamicOptimizations;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LowFPSThreshold;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FPSToStreamingDistanceCurve;
	static void NewProp_bUsePlatformSpecificOptimizations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePlatformSpecificOptimizations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinTimeBetweenOptimizationAdjustments;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldPartition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingElements_ValueProp;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_StreamingElements_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_StreamingElements;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RegisteredElements_ValueProp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RegisteredElements_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RegisteredElements;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingRegions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_StreamingRegions;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ApplyOptimizationToAllElements, "ApplyOptimizationToAllElements" }, // 3613672233
		{ &Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_ConfigureHardwareOptimizations, "ConfigureHardwareOptimizations" }, // 454119032
		{ &Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_DetectHardwareCapabilities, "DetectHardwareCapabilities" }, // 1053825745
		{ &Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetHardwareStreamingDistanceMultiplier, "GetHardwareStreamingDistanceMultiplier" }, // 1691380470
		{ &Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_GetStreamingPerformanceStats, "GetStreamingPerformanceStats" }, // 1336381037
		{ &Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_InitializeWorldPartitionIntegration, "InitializeWorldPartitionIntegration" }, // 1205790007
		{ &Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_OptimizeStreamingForCurrentPlatform, "OptimizeStreamingForCurrentPlatform" }, // 3552472459
		{ &Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming, "RegisterPCGElementForStreaming" }, // 342353863
		{ &Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_SetStreamingQualityProfile, "SetStreamingQualityProfile" }, // 1215340180
		{ &Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation, "UpdateStreamingForPlayerLocation" }, // 1349655335
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGWorldPartitionIntegration>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_DefaultStreamingConfig = { "DefaultStreamingConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, DefaultStreamingConfig), Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultStreamingConfig_MetaData), NewProp_DefaultStreamingConfig_MetaData) }; // 3330627406
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingRadius = { "StreamingRadius", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, StreamingRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingRadius_MetaData), NewProp_StreamingRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_UpdateInterval = { "UpdateInterval", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, UpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpdateInterval_MetaData), NewProp_UpdateInterval_MetaData) };
void Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bAutoUpdateStreaming_SetBit(void* Obj)
{
	((AAURACRONPCGWorldPartitionIntegration*)Obj)->bAutoUpdateStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bAutoUpdateStreaming = { "bAutoUpdateStreaming", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGWorldPartitionIntegration), &Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bAutoUpdateStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoUpdateStreaming_MetaData), NewProp_bAutoUpdateStreaming_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_HardwareConfig = { "HardwareConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, HardwareConfig), Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HardwareConfig_MetaData), NewProp_HardwareConfig_MetaData) }; // 2252076375
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_PerformanceStats = { "PerformanceStats", nullptr, (EPropertyFlags)0x0020080000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, PerformanceStats), Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceStats_MetaData), NewProp_PerformanceStats_MetaData) }; // 1612994734
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StatsUpdateInterval = { "StatsUpdateInterval", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, StatsUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StatsUpdateInterval_MetaData), NewProp_StatsUpdateInterval_MetaData) };
void Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bEnableDynamicOptimizations_SetBit(void* Obj)
{
	((AAURACRONPCGWorldPartitionIntegration*)Obj)->bEnableDynamicOptimizations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bEnableDynamicOptimizations = { "bEnableDynamicOptimizations", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGWorldPartitionIntegration), &Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bEnableDynamicOptimizations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDynamicOptimizations_MetaData), NewProp_bEnableDynamicOptimizations_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_LowFPSThreshold = { "LowFPSThreshold", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, LowFPSThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LowFPSThreshold_MetaData), NewProp_LowFPSThreshold_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_FPSToStreamingDistanceCurve = { "FPSToStreamingDistanceCurve", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, FPSToStreamingDistanceCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FPSToStreamingDistanceCurve_MetaData), NewProp_FPSToStreamingDistanceCurve_MetaData) };
void Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bUsePlatformSpecificOptimizations_SetBit(void* Obj)
{
	((AAURACRONPCGWorldPartitionIntegration*)Obj)->bUsePlatformSpecificOptimizations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bUsePlatformSpecificOptimizations = { "bUsePlatformSpecificOptimizations", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGWorldPartitionIntegration), &Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bUsePlatformSpecificOptimizations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePlatformSpecificOptimizations_MetaData), NewProp_bUsePlatformSpecificOptimizations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_MinTimeBetweenOptimizationAdjustments = { "MinTimeBetweenOptimizationAdjustments", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, MinTimeBetweenOptimizationAdjustments), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinTimeBetweenOptimizationAdjustments_MetaData), NewProp_MinTimeBetweenOptimizationAdjustments_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_WorldPartition = { "WorldPartition", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, WorldPartition), Z_Construct_UClass_UWorldPartition_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPartition_MetaData), NewProp_WorldPartition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingElements_ValueProp = { "StreamingElements", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, METADATA_PARAMS(0, nullptr) }; // 3330627406
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingElements_Key_KeyProp = { "StreamingElements_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingElements = { "StreamingElements", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, StreamingElements), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingElements_MetaData), NewProp_StreamingElements_MetaData) }; // 3330627406
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_RegisteredElements_ValueProp = { "RegisteredElements", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry, METADATA_PARAMS(0, nullptr) }; // 2197663406
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_RegisteredElements_Key_KeyProp = { "RegisteredElements_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_RegisteredElements = { "RegisteredElements", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, RegisteredElements), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredElements_MetaData), NewProp_RegisteredElements_MetaData) }; // 2197663406
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingRegions_Inner = { "StreamingRegions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion, METADATA_PARAMS(0, nullptr) }; // 4185464151
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingRegions = { "StreamingRegions", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, StreamingRegions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingRegions_MetaData), NewProp_StreamingRegions_MetaData) }; // 4185464151
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_DefaultStreamingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_UpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bAutoUpdateStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_HardwareConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_PerformanceStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StatsUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bEnableDynamicOptimizations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_LowFPSThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_FPSToStreamingDistanceCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bUsePlatformSpecificOptimizations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_MinTimeBetweenOptimizationAdjustments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_WorldPartition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingElements_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingElements_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_RegisteredElements_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_RegisteredElements_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_RegisteredElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingRegions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingRegions,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::ClassParams = {
	&AAURACRONPCGWorldPartitionIntegration::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration.OuterSingleton, Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGWorldPartitionIntegration);
AAURACRONPCGWorldPartitionIntegration::~AAURACRONPCGWorldPartitionIntegration() {}
// ********** End Class AAURACRONPCGWorldPartitionIntegration **************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAURACRONStreamingQualityProfile_StaticEnum, TEXT("EAURACRONStreamingQualityProfile"), &Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1554993972U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONStreamingPerformanceStats::StaticStruct, Z_Construct_UScriptStruct_FAURACRONStreamingPerformanceStats_Statics::NewStructOps, TEXT("AURACRONStreamingPerformanceStats"), &Z_Registration_Info_UScriptStruct_FAURACRONStreamingPerformanceStats, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONStreamingPerformanceStats), 1612994734U) },
		{ FAURACRONHardwareStreamingConfig::StaticStruct, Z_Construct_UScriptStruct_FAURACRONHardwareStreamingConfig_Statics::NewStructOps, TEXT("AURACRONHardwareStreamingConfig"), &Z_Registration_Info_UScriptStruct_FAURACRONHardwareStreamingConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONHardwareStreamingConfig), 2252076375U) },
		{ FAURACRONPCGStreamingConfig::StaticStruct, Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewStructOps, TEXT("AURACRONPCGStreamingConfig"), &Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONPCGStreamingConfig), 3330627406U) },
		{ FAURACRONPCGWorldPartitionStreamingConfig::StaticStruct, Z_Construct_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig_Statics::NewStructOps, TEXT("AURACRONPCGWorldPartitionStreamingConfig"), &Z_Registration_Info_UScriptStruct_FAURACRONPCGWorldPartitionStreamingConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONPCGWorldPartitionStreamingConfig), 1435596736U) },
		{ FAURACRONPCGStreamingEntry::StaticStruct, Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewStructOps, TEXT("AURACRONPCGStreamingEntry"), &Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONPCGStreamingEntry), 2197663406U) },
		{ FAURACRONPCGStreamingRegion::StaticStruct, Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewStructOps, TEXT("AURACRONPCGStreamingRegion"), &Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONPCGStreamingRegion), 4185464151U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration, AAURACRONPCGWorldPartitionIntegration::StaticClass, TEXT("AAURACRONPCGWorldPartitionIntegration"), &Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGWorldPartitionIntegration), 3468123508U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h__Script_AURACRON_2159570195(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h__Script_AURACRON_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
