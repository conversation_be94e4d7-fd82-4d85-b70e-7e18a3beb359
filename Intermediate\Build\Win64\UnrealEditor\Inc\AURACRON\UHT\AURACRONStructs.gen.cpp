// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AURACRONStructs.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONStructs() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONBuffType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONProceduralObjective();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FPrismalFlowSegment();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FPrismalFlowSegment ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPrismalFlowSegment;
class UScriptStruct* FPrismalFlowSegment::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPrismalFlowSegment, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("PrismalFlowSegment"));
	}
	return Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Segmento do Fluxo Prismal - O n\xc3\xba""cleo serpentino do mapa\n * Representa uma se\xc3\xa7\xc3\xa3o do rio de energia que serpenteia pelos ambientes\n */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Segmento do Fluxo Prismal - O n\xc3\xba""cleo serpentino do mapa\nRepresenta uma se\xc3\xa7\xc3\xa3o do rio de energia que serpenteia pelos ambientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPosition_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o mundial do segmento */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o mundial do segmento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowDirection_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dire\xc3\xa7\xc3\xa3o do fluxo de energia */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dire\xc3\xa7\xc3\xa3o do fluxo de energia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Width_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
		{ "ClampMax", "50.0" },
		{ "ClampMin", "20.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Largura do segmento (20-50 unidades) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Largura do segmento (20-50 unidades)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSpeed_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade do fluxo - afeta movimento e habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade do fluxo - afeta movimento e habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnergyIntensity_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade da energia prism\xc3\xa1tica */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da energia prism\xc3\xa1tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentColor_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor atual baseada na equipe controladora */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor atual baseada na equipe controladora" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControllingTeam_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Equipe que controla este segmento (-1 = neutro, 0 = equipe A, 1 = equipe B) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Equipe que controla este segmento (-1 = neutro, 0 = equipe A, 1 = equipe B)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCalmFlow_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se este segmento est\xc3\xa1 em estado calmo (para estrat\xc3\xa9gias) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se este segmento est\xc3\xa1 em estado calmo (para estrat\xc3\xa9gias)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControlTimeRemaining_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo restante de controle de equipe */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo restante de controle de equipe" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnergyIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentColor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ControllingTeam;
	static void NewProp_bIsCalmFlow_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCalmFlow;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ControlTimeRemaining;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPrismalFlowSegment>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_WorldPosition = { "WorldPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, WorldPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPosition_MetaData), NewProp_WorldPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_FlowDirection = { "FlowDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, FlowDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowDirection_MetaData), NewProp_FlowDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, Width), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Width_MetaData), NewProp_Width_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_FlowSpeed = { "FlowSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, FlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSpeed_MetaData), NewProp_FlowSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_EnergyIntensity = { "EnergyIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, EnergyIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnergyIntensity_MetaData), NewProp_EnergyIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_CurrentColor = { "CurrentColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, CurrentColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentColor_MetaData), NewProp_CurrentColor_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_ControllingTeam = { "ControllingTeam", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, ControllingTeam), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControllingTeam_MetaData), NewProp_ControllingTeam_MetaData) };
void Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_bIsCalmFlow_SetBit(void* Obj)
{
	((FPrismalFlowSegment*)Obj)->bIsCalmFlow = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_bIsCalmFlow = { "bIsCalmFlow", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPrismalFlowSegment), &Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_bIsCalmFlow_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCalmFlow_MetaData), NewProp_bIsCalmFlow_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_ControlTimeRemaining = { "ControlTimeRemaining", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, ControlTimeRemaining), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControlTimeRemaining_MetaData), NewProp_ControlTimeRemaining_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_WorldPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_FlowDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_FlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_EnergyIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_CurrentColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_ControllingTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_bIsCalmFlow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_ControlTimeRemaining,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"PrismalFlowSegment",
	Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers),
	sizeof(FPrismalFlowSegment),
	alignof(FPrismalFlowSegment),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPrismalFlowSegment()
{
	if (!Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.InnerSingleton, Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.InnerSingleton;
}
// ********** End ScriptStruct FPrismalFlowSegment *************************************************

// ********** Begin ScriptStruct FAURACRONProceduralObjective **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective;
class UScriptStruct* FAURACRONProceduralObjective::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONProceduralObjective, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONProceduralObjective"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Objetivo Procedural - Sistema de gera\xc3\xa7\xc3\xa3o din\xc3\xa2mica de objetivos\n * Baseado no estado da partida para balanceamento autom\xc3\xa1tico\n */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objetivo Procedural - Sistema de gera\xc3\xa7\xc3\xa3o din\xc3\xa2mica de objetivos\nBaseado no estado da partida para balanceamento autom\xc3\xa1tico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveType_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do objetivo procedural */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do objetivo procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPosition_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o mundial do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o mundial do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Environment_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ambiente onde o objetivo est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ambiente onde o objetivo est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentType_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de ambiente (alias para compatibilidade) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de ambiente (alias para compatibilidade)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControllingTeam_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Equipe que controla o objetivo (-1 = neutro) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Equipe que controla o objetivo (-1 = neutro)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccumulatedValue_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valor acumulado para objetivos que requerem progresso */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valor acumulado para objetivos que requerem progresso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxValue_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valor m\xc3\xa1ximo necess\xc3\xa1rio para completar o objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valor m\xc3\xa1ximo necess\xc3\xa1rio para completar o objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifeTime_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de vida do objetivo (0 = permanente) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de vida do objetivo (0 = permanente)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RemainingLifeTime_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo restante de vida */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo restante de vida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GoldReward_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensa em ouro para a equipe */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensa em ouro para a equipe" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExperienceReward_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensa em experi\xc3\xaancia para a equipe */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensa em experi\xc3\xaancia para a equipe" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BuffType_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de buff concedido ao completar */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de buff concedido ao completar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BuffMagnitude_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Magnitude do buff */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Magnitude do buff" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BuffDuration_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o do buff em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do buff em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o objetivo est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o objetivo est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCompleted_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o objetivo foi completado */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o objetivo foi completado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade do objetivo (maior = mais importante) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade do objetivo (maior = mais importante)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrategicValue_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valor estrat\xc3\xa9gico do objetivo (0.0 - 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valor estrat\xc3\xa9gico do objetivo (0.0 - 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveCategory_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Categoria do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Categoria do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentState_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado atual do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado atual do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeAlive_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo que o objetivo est\xc3\xa1 vivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo que o objetivo est\xc3\xa1 vivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldPosition;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ControllingTeam;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccumulatedValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LifeTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RemainingLifeTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GoldReward;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ExperienceReward;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BuffType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BuffType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BuffMagnitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BuffDuration;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static void NewProp_bIsCompleted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCompleted;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StrategicValue;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveCategory_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveCategory;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeAlive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONProceduralObjective>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveType = { "ObjectiveType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, ObjectiveType), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveType_MetaData), NewProp_ObjectiveType_MetaData) }; // 11509637
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_WorldPosition = { "WorldPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, WorldPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPosition_MetaData), NewProp_WorldPosition_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Environment_MetaData), NewProp_Environment_MetaData) }; // 2161956974
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_EnvironmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, EnvironmentType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentType_MetaData), NewProp_EnvironmentType_MetaData) }; // 2161956974
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ControllingTeam = { "ControllingTeam", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, ControllingTeam), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControllingTeam_MetaData), NewProp_ControllingTeam_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_AccumulatedValue = { "AccumulatedValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, AccumulatedValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccumulatedValue_MetaData), NewProp_AccumulatedValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_MaxValue = { "MaxValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, MaxValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxValue_MetaData), NewProp_MaxValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_LifeTime = { "LifeTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, LifeTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifeTime_MetaData), NewProp_LifeTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_RemainingLifeTime = { "RemainingLifeTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, RemainingLifeTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RemainingLifeTime_MetaData), NewProp_RemainingLifeTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_GoldReward = { "GoldReward", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, GoldReward), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GoldReward_MetaData), NewProp_GoldReward_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ExperienceReward = { "ExperienceReward", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, ExperienceReward), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExperienceReward_MetaData), NewProp_ExperienceReward_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffType = { "BuffType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, BuffType), Z_Construct_UEnum_AURACRON_EAURACRONBuffType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BuffType_MetaData), NewProp_BuffType_MetaData) }; // 362549284
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffMagnitude = { "BuffMagnitude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, BuffMagnitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BuffMagnitude_MetaData), NewProp_BuffMagnitude_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffDuration = { "BuffDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, BuffDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BuffDuration_MetaData), NewProp_BuffDuration_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAURACRONProceduralObjective*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONProceduralObjective), &Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsCompleted_SetBit(void* Obj)
{
	((FAURACRONProceduralObjective*)Obj)->bIsCompleted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsCompleted = { "bIsCompleted", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONProceduralObjective), &Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsCompleted_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCompleted_MetaData), NewProp_bIsCompleted_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, Priority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_StrategicValue = { "StrategicValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, StrategicValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrategicValue_MetaData), NewProp_StrategicValue_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveCategory_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveCategory = { "ObjectiveCategory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, ObjectiveCategory), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveCategory_MetaData), NewProp_ObjectiveCategory_MetaData) }; // 1919075103
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CurrentState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CurrentState = { "CurrentState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, CurrentState), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentState_MetaData), NewProp_CurrentState_MetaData) }; // 1497233863
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_TimeAlive = { "TimeAlive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, TimeAlive), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeAlive_MetaData), NewProp_TimeAlive_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_WorldPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Environment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_EnvironmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_EnvironmentType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ControllingTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_AccumulatedValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_MaxValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_LifeTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_RemainingLifeTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_GoldReward,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ExperienceReward,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffMagnitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_StrategicValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveCategory_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveCategory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CurrentState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CurrentState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_TimeAlive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONProceduralObjective",
	Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::PropPointers),
	sizeof(FAURACRONProceduralObjective),
	alignof(FAURACRONProceduralObjective),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONProceduralObjective()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONProceduralObjective ****************************************

// ********** Begin ScriptStruct FAURACRONObjectiveGenerationConfig ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig;
class UScriptStruct* FAURACRONObjectiveGenerationConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONObjectiveGenerationConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xa3o de gera\xc3\xa7\xc3\xa3o de objetivos procedurais\n * Par\xc3\xa2metros para o sistema de balanceamento din\xc3\xa2mico\n */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o de gera\xc3\xa7\xc3\xa3o de objetivos procedurais\nPar\xc3\xa2metros para o sistema de balanceamento din\xc3\xa2mico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinActiveObjectives_MetaData[] = {
		{ "Category", "Gera\xc3\xa7\xc3\xa3o" },
		{ "ClampMax", "10" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xadnimo de objetivos ativos simultaneamente */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xadnimo de objetivos ativos simultaneamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxActiveObjectives_MetaData[] = {
		{ "Category", "Gera\xc3\xa7\xc3\xa3o" },
		{ "ClampMax", "20" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xa1ximo de objetivos ativos simultaneamente */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de objetivos ativos simultaneamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinSpawnInterval_MetaData[] = {
		{ "Category", "Gera\xc3\xa7\xc3\xa3o" },
		{ "ClampMax", "300.0" },
		{ "ClampMin", "10.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo m\xc3\xadnimo entre spawns de objetivos (segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo m\xc3\xadnimo entre spawns de objetivos (segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSpawnInterval_MetaData[] = {
		{ "Category", "Gera\xc3\xa7\xc3\xa3o" },
		{ "ClampMax", "600.0" },
		{ "ClampMin", "30.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo m\xc3\xa1ximo entre spawns de objetivos (segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo m\xc3\xa1ximo entre spawns de objetivos (segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CatchupSpawnMultiplier_MetaData[] = {
		{ "Category", "Gera\xc3\xa7\xc3\xa3o" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de spawn quando uma equipe est\xc3\xa1 atr\xc3\xa1s */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de spawn quando uma equipe est\xc3\xa1 atr\xc3\xa1s" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CatchupGoldThreshold_MetaData[] = {
		{ "Category", "Gera\xc3\xa7\xc3\xa3o" },
		{ "ClampMax", "5000" },
		{ "ClampMin", "500" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Diferen\xc3\xa7""a de ouro necess\xc3\xa1ria para ativar catch-up */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Diferen\xc3\xa7""a de ouro necess\xc3\xa1ria para ativar catch-up" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CatchupKillThreshold_MetaData[] = {
		{ "Category", "Gera\xc3\xa7\xc3\xa3o" },
		{ "ClampMax", "15" },
		{ "ClampMin", "3" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Diferen\xc3\xa7""a de kills necess\xc3\xa1ria para ativar catch-up */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Diferen\xc3\xa7""a de kills necess\xc3\xa1ria para ativar catch-up" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinActiveObjectives;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxActiveObjectives;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinSpawnInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSpawnInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CatchupSpawnMultiplier;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CatchupGoldThreshold;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CatchupKillThreshold;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONObjectiveGenerationConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MinActiveObjectives = { "MinActiveObjectives", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, MinActiveObjectives), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinActiveObjectives_MetaData), NewProp_MinActiveObjectives_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MaxActiveObjectives = { "MaxActiveObjectives", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, MaxActiveObjectives), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxActiveObjectives_MetaData), NewProp_MaxActiveObjectives_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MinSpawnInterval = { "MinSpawnInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, MinSpawnInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinSpawnInterval_MetaData), NewProp_MinSpawnInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MaxSpawnInterval = { "MaxSpawnInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, MaxSpawnInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSpawnInterval_MetaData), NewProp_MaxSpawnInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_CatchupSpawnMultiplier = { "CatchupSpawnMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, CatchupSpawnMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CatchupSpawnMultiplier_MetaData), NewProp_CatchupSpawnMultiplier_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_CatchupGoldThreshold = { "CatchupGoldThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, CatchupGoldThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CatchupGoldThreshold_MetaData), NewProp_CatchupGoldThreshold_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_CatchupKillThreshold = { "CatchupKillThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, CatchupKillThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CatchupKillThreshold_MetaData), NewProp_CatchupKillThreshold_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MinActiveObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MaxActiveObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MinSpawnInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MaxSpawnInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_CatchupSpawnMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_CatchupGoldThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_CatchupKillThreshold,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONObjectiveGenerationConfig",
	Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::PropPointers),
	sizeof(FAURACRONObjectiveGenerationConfig),
	alignof(FAURACRONObjectiveGenerationConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONObjectiveGenerationConfig **********************************

// ********** Begin ScriptStruct FAURACRONBasicStreamingConfig *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig;
class UScriptStruct* FAURACRONBasicStreamingConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONBasicStreamingConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xa3o b\xc3\xa1sica de streaming para sistemas gerais\n * Para World Partition espec\xc3\xad""fico, use FAURACRONPCGWorldPartitionStreamingConfig\n */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o b\xc3\xa1sica de streaming para sistemas gerais\nPara World Partition espec\xc3\xad""fico, use FAURACRONPCGWorldPartitionStreamingConfig" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShapeScale_MetaData[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Forma de streaming - usando FVector para definir escala da caixa */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Forma de streaming - usando FVector para definir escala da caixa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingPriority_MetaData[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivationDistance_MetaData[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia de ativa\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia de ativa\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePerformanceBasedStreaming_MetaData[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve usar streaming baseado em performance */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve usar streaming baseado em performance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ShapeScale;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamingPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivationDistance;
	static void NewProp_bUsePerformanceBasedStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePerformanceBasedStreaming;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONBasicStreamingConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_ShapeScale = { "ShapeScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONBasicStreamingConfig, ShapeScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShapeScale_MetaData), NewProp_ShapeScale_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_StreamingPriority = { "StreamingPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONBasicStreamingConfig, StreamingPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingPriority_MetaData), NewProp_StreamingPriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_ActivationDistance = { "ActivationDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONBasicStreamingConfig, ActivationDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivationDistance_MetaData), NewProp_ActivationDistance_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_bUsePerformanceBasedStreaming_SetBit(void* Obj)
{
	((FAURACRONBasicStreamingConfig*)Obj)->bUsePerformanceBasedStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_bUsePerformanceBasedStreaming = { "bUsePerformanceBasedStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONBasicStreamingConfig), &Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_bUsePerformanceBasedStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePerformanceBasedStreaming_MetaData), NewProp_bUsePerformanceBasedStreaming_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_ShapeScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_StreamingPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_ActivationDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_bUsePerformanceBasedStreaming,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONBasicStreamingConfig",
	Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::PropPointers),
	sizeof(FAURACRONBasicStreamingConfig),
	alignof(FAURACRONBasicStreamingConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONBasicStreamingConfig ***************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONStructs_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPrismalFlowSegment::StaticStruct, Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewStructOps, TEXT("PrismalFlowSegment"), &Z_Registration_Info_UScriptStruct_FPrismalFlowSegment, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPrismalFlowSegment), 757446967U) },
		{ FAURACRONProceduralObjective::StaticStruct, Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewStructOps, TEXT("AURACRONProceduralObjective"), &Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONProceduralObjective), 3311681805U) },
		{ FAURACRONObjectiveGenerationConfig::StaticStruct, Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewStructOps, TEXT("AURACRONObjectiveGenerationConfig"), &Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONObjectiveGenerationConfig), 1527385679U) },
		{ FAURACRONBasicStreamingConfig::StaticStruct, Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewStructOps, TEXT("AURACRONBasicStreamingConfig"), &Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONBasicStreamingConfig), 3378310114U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONStructs_h__Script_AURACRON_2964591209(TEXT("/Script/AURACRON"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONStructs_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONStructs_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
