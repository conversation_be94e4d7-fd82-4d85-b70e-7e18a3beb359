/MANIFEST:NO
/NOLOGO
/DEBUG:FULL
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/FIXED:No
/NXCOMPAT
/STACK:12000000
/DELAY:UNLOAD
/DLL
/PDBALTPATH:%_PDB%
/d2:-ExtendedWarningInfo
/OPT:NOREF
/OPT:NOICF
/INCREMENTAL:NO
/ignore:4199
/ignore:4099
/ALTERNATENAME:__imp___std_init_once_begin_initialize=__imp_InitOnceBeginInitialize
/ALTERNATENAME:__imp___std_init_once_complete=__imp_InitOnceComplete
/DELAYLOAD:"d3d12.dll"
/DELAYLOAD:"DBGHELP.DLL"
/LIBPATH:"C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/lib/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/ucrt/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/um/x64"
/NODEFAULTLIB:"LIBCMT"
/NODEFAULTLIB:"LIBCPMT"
/NODEFAULTLIB:"LIBCMTD"
/NODEFAULTLIB:"LIBCPMTD"
/NODEFAULTLIB:"MSVCRTD"
/NODEFAULTLIB:"MSVCPRTD"
/NODEFAULTLIB:"LIBC"
/NODEFAULTLIB:"LIBCP"
/NODEFAULTLIB:"LIBCD"
/NODEFAULTLIB:"LIBCPD"
/FUNCTIONPADMIN:6
/NOIMPLIB
/NOEXP
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/IrisCore/IrisCore.natvis"
/NATVIS:"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/Niagara/Niagara.natvis"
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/RenderCore.natvis"
/NATVIS:"../Plugins/PCG/Intermediate/Build/Win64/x64/UnrealEditor/Development/PCG/PCG.natvis"
"C:/AURACRON/Intermediate/Build/Win64/x64/AURACRONEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/Module.AURACRON.1.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/Module.AURACRON.2.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/Module.AURACRON.3.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/Module.AURACRON.4.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/Module.AURACRON.5.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/Module.AURACRON.6.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/Module.AURACRON.7.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/Module.AURACRON.8.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/Module.AURACRON.9.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/Module.AURACRON.10.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/Module.AURACRON.11.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/Module.AURACRON.12.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRON.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/DamageZoneComponent.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/SigilDebugCommands.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/SigilFusionSystem.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/SigilNetworkConfig.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/SigilReplicationManager.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONMapMeasurements.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGArsenalIsland.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGChaosIsland.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGChaosIslandManager.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGChaosPortal.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGEnergyPulse.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGEnvironment.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGEnvironmentManager.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGIsland.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGJungleSystem.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGLaneSystem.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGMathLibrary.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGNexusIsland.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGObjectiveSystem.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGPerformanceManager.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGPhaseManager.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGPortal.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGPrismalFlow.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGPurgatoryAnchor.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGSanctuaryIsland.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGShadowNexuses.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGSpectralGuardian.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGSubsystem.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGTowersOfLamentation.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGTrail.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGUtility.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/AURACRONPCGWorldPartitionIntegration.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/SigilAbilities.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/SigilAbilityEffects.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/SigilAttributeSet.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/SigilGameplayEffects.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/SigilItem.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/SigilManagerComponent.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/SigilWidgets.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/SigilVFXManager.cpp.obj"
"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealEditor/Development/AURACRON/Default.rc2.res"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/NetCore/UnrealEditor-NetCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/NetCore/UnrealEditor-NetCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/TraceLog/UnrealEditor-TraceLog.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/IrisCore/UnrealEditor-IrisCore.lib"
"../Plugins/Runtime/GameplayAbilities/Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayAbilities/UnrealEditor-GameplayAbilities.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayTags/UnrealEditor-GameplayTags.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayTasks/UnrealEditor-GameplayTasks.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/UMG/UnrealEditor-UMG.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Slate/UnrealEditor-Slate.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SlateCore/UnrealEditor-SlateCore.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/Niagara/UnrealEditor-Niagara.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/NiagaraCore/UnrealEditor-NiagaraCore.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/NiagaraShader/UnrealEditor-NiagaraShader.lib"
"../Plugins/Runtime/ReplicationGraph/Intermediate/Build/Win64/x64/UnrealEditor/Development/ReplicationGraph/UnrealEditor-ReplicationGraph.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/DeveloperSettings/UnrealEditor-DeveloperSettings.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ToolMenus/UnrealEditor-ToolMenus.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ApplicationCore/UnrealEditor-ApplicationCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/UnrealEditor-RenderCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RHI/UnrealEditor-RHI.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Core/UnrealEditor-Core.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/CoreUObject/UnrealEditor-CoreUObject.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Engine/UnrealEditor-Engine.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/InputCore/UnrealEditor-InputCore.lib"
"../Plugins/EnhancedInput/Intermediate/Build/Win64/x64/UnrealEditor/Development/EnhancedInput/UnrealEditor-EnhancedInput.lib"
"../Plugins/PCG/Intermediate/Build/Win64/x64/UnrealEditor/Development/PCG/UnrealEditor-PCG.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/NetCommon/UnrealEditor-NetCommon.lib"
"delayimp.lib"
"wininet.lib"
"rpcrt4.lib"
"ws2_32.lib"
"dbghelp.lib"
"comctl32.lib"
"Winmm.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"netapi32.lib"
"iphlpapi.lib"
"setupapi.lib"
"synchronization.lib"
"dwmapi.lib"
"imm32.lib"
"uiautomationcore.lib"
"DXGI.lib"
/OUT:"C:/AURACRON/Binaries/Win64/UnrealEditor-AURACRON.dll"
/PDB:"C:/AURACRON/Binaries/Win64/UnrealEditor-AURACRON.pdb"
/ignore:4078