// AURACRONPCGLaneSystem.cpp
// Sistema de Lanes e Jungle para AURACRON - UE 5.6
// Implementação baseada em análise de LoL e Dota 2 usando APIs modernas do UE 5.6

#include "PCG/AURACRONPCGLaneSystem.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "PCG/AURACRONPCGJungleSystem.h"
#include "PCG/AURACRONPCGPerformanceManager.h"

// Includes modernos do UE 5.6 - usando estrutura que funciona no projeto
#include "PCG/AURACRONPCGTypes.h"

AAURACRONPCGLaneSystem::AAURACRONPCGLaneSystem()
    : bA<PERSON>Generate(true)
    , CurrentEnvironment(EAURACRONEnvironmentType::RadiantPlains)
    , CurrentMapPhase(EAURACRONMapPhase::Awakening)
    , CurrentTransitionDuration(30.0f)
    , TargetEnvironment(EAURACRONEnvironmentType::RadiantPlains)
{
    PrimaryActorTick.bCanEverTick = true;

    // Configurar replicação para multiplayer
    bReplicates = true;
    SetReplicateMovement(false);

    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));
}

void AAURACRONPCGLaneSystem::BeginPlay()
{
    Super::BeginPlay();
    
    // Gerar sistema apenas no servidor
    if (HasAuthority() && bAutoGenerate)
    {
        // Delay pequeno para garantir que outros sistemas estejam prontos
        FTimerHandle GenerationTimer;
        GetWorld()->GetTimerManager().SetTimer(GenerationTimer, this, 
            &AAURACRONPCGLaneSystem::GenerateLaneSystem, 1.0f, false);
    }
}

void AAURACRONPCGLaneSystem::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Atualizar efeitos baseados na fase do mapa
    if (HasAuthority())
    {
        // Verificar se a fase mudou
        // (Isso seria integrado com o sistema de fases do mapa)
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES PÚBLICAS
// ========================================

void AAURACRONPCGLaneSystem::GenerateLaneSystem()
{
    if (!HasAuthority())
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGLaneSystem: Gerando sistema completo de lanes para 3 ambientes (baseado em LoL)"));

    // Limpar elementos anteriores
    ClearAllGeneratedElements();

    // Inicializar informações das lanes para todos os ambientes
    InitializeLaneInfos();

    // Gerar lanes para todos os 3 ambientes
    for (int32 EnvIndex = 0; EnvIndex < 3; ++EnvIndex)
    {
        EAURACRONEnvironmentType Environment = static_cast<EAURACRONEnvironmentType>(EnvIndex);
        GenerateLanesForEnvironment(Environment);
    }

    // Gerar jungle camps
    GenerateJungleCamps();

    // Gerar estruturas defensivas para todos os ambientes
    GenerateDefensiveStructures();

    // Iniciar com Radiant Plains ativo
    TransitionToEnvironment(EAURACRONEnvironmentType::RadiantPlains, 0.0f);

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGLaneSystem: Sistema completo de lanes gerado para 3 ambientes"));
}

void AAURACRONPCGLaneSystem::GenerateLanesForEnvironment(EAURACRONEnvironmentType Environment)
{
    if (!HasAuthority())
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGLaneSystem: Gerando lanes para ambiente %d"), static_cast<int32>(Environment));

    // Gerar cada lane para o ambiente específico
    GenerateLane(EAURACRONLaneType::TopLane, Environment);
    GenerateLane(EAURACRONLaneType::MidLane, Environment);
    GenerateLane(EAURACRONLaneType::BotLane, Environment);
    GenerateLane(EAURACRONLaneType::PrismalFlow, Environment);

    // Aplicar características específicas do ambiente
    ApplyEnvironmentCharacteristics(Environment);
}

void AAURACRONPCGLaneSystem::TransitionToEnvironment(EAURACRONEnvironmentType NewEnvironment, float TransitionDuration)
{
    if (!HasAuthority())
    {
        return;
    }

    if (CurrentEnvironment == NewEnvironment)
    {
        return; // Já estamos no ambiente desejado
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGLaneSystem: Iniciando transição para ambiente %d (duração: %.1fs)"),
        static_cast<int32>(NewEnvironment), TransitionDuration);

    // Configurar transição
    TargetEnvironment = NewEnvironment;
    CurrentTransitionDuration = TransitionDuration;

    if (TransitionDuration > 0.0f)
    {
        // Transição gradual
        GetWorld()->GetTimerManager().SetTimer(
            EnvironmentTransitionTimer,
            this,
            &AAURACRONPCGLaneSystem::ExecuteEnvironmentTransition,
            0.1f, // Update a cada 100ms
            true
        );
    }
    else
    {
        // Transição instantânea
        CurrentEnvironment = NewEnvironment;
        CompleteEnvironmentTransition();
    }
}

void AAURACRONPCGLaneSystem::ExecuteEnvironmentTransition()
{
    // Implementar transição suave (fade in/out, movimento de elementos, etc.)
    // Por enquanto, fazer transição instantânea após o timer

    static float TransitionProgress = 0.0f;
    TransitionProgress += 0.1f;

    if (TransitionProgress >= CurrentTransitionDuration)
    {
        // Finalizar transição
        CurrentEnvironment = TargetEnvironment;
        CompleteEnvironmentTransition();

        GetWorld()->GetTimerManager().ClearTimer(EnvironmentTransitionTimer);
        TransitionProgress = 0.0f;
    }
}

void AAURACRONPCGLaneSystem::CompleteEnvironmentTransition()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGLaneSystem: Transição completa para ambiente %d"),
        static_cast<int32>(CurrentEnvironment));

    // Atualizar visibilidade dos elementos baseado no ambiente atual
    for (const auto& EnvPair : GeneratedComponentsByEnvironment)
    {
        EAURACRONEnvironmentType Environment = EnvPair.Key;
        const TArray<UActorComponent*>& Components = EnvPair.Value.Components;

        bool bShouldBeVisible = (Environment == CurrentEnvironment);

        for (UActorComponent* Component : Components)
        {
            if (UPrimitiveComponent* PrimComp = Cast<UPrimitiveComponent>(Component))
            {
                PrimComp->SetVisibility(bShouldBeVisible);
            }
        }
    }

    // Atualizar estruturas defensivas
    for (const auto& EnvPair : DefensiveStructuresByEnvironment)
    {
        EAURACRONEnvironmentType Environment = EnvPair.Key;
        const TArray<AActor*>& Structures = EnvPair.Value.Actors;

        bool bShouldBeVisible = (Environment == CurrentEnvironment);

        for (AActor* Structure : Structures)
        {
            if (IsValid(Structure))
            {
                Structure->SetActorHiddenInGame(!bShouldBeVisible);
            }
        }
    }
}

TArray<FVector> AAURACRONPCGLaneSystem::CalculateLanePointsForEnvironment(EAURACRONLaneType LaneType, EAURACRONEnvironmentType Environment)
{
    TArray<FVector> LanePoints;
    float EnvironmentHeight = GetEnvironmentHeight(Environment);

    switch (LaneType)
    {
    case EAURACRONLaneType::TopLane:
        {
            // Top Lane: diagonal superior esquerda para inferior direita (layout LoL)
            FVector StartPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
                FAURACRONMapDimensions::TOP_LANE_START_X,
                FAURACRONMapDimensions::TOP_LANE_START_Y,
                EnvironmentHeight
            );
            FVector EndPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
                FAURACRONMapDimensions::TOP_LANE_END_X,
                FAURACRONMapDimensions::TOP_LANE_END_Y,
                EnvironmentHeight
            );

            // Aplicar modificações específicas do ambiente
            if (Environment == EAURACRONEnvironmentType::ZephyrFirmament)
            {
                // Zephyr: adicionar plataformas flutuantes
                int32 NumPlatforms = 8;
                for (int32 i = 0; i <= NumPlatforms; ++i)
                {
                    float T = static_cast<float>(i) / NumPlatforms;
                    FVector BasePoint = FMath::Lerp(StartPoint, EndPoint, T);

                    // Adicionar variação de altura para plataformas
                    float PlatformVariation = FMath::Sin(T * PI * 3) * 300.0f; // ±3m
                    BasePoint.Z += PlatformVariation;

                    LanePoints.Add(BasePoint);
                }
            }
            else if (Environment == EAURACRONEnvironmentType::PurgatoryRealm)
            {
                // Purgatory: túneis com curvas
                int32 NumPoints = 30;
                for (int32 i = 0; i <= NumPoints; ++i)
                {
                    float T = static_cast<float>(i) / NumPoints;
                    FVector BasePoint = FMath::Lerp(StartPoint, EndPoint, T);

                    // Adicionar curvas espectrais
                    float CurveOffset = FMath::Sin(T * PI * 2) * 400.0f; // ±4m
                    FVector CurveDirection = FVector::CrossProduct(
                        (EndPoint - StartPoint).GetSafeNormal(),
                        FVector::UpVector
                    );
                    BasePoint += CurveDirection * CurveOffset;

                    LanePoints.Add(BasePoint);
                }
            }
            else
            {
                // Radiant Plains: lane reta tradicional
                int32 NumPoints = 25;
                for (int32 i = 0; i <= NumPoints; ++i)
                {
                    float T = static_cast<float>(i) / NumPoints;
                    FVector LanePoint = FMath::Lerp(StartPoint, EndPoint, T);
                    LanePoints.Add(LanePoint);
                }
            }
        }
        break;

    case EAURACRONLaneType::MidLane:
        {
            // Mid Lane: diagonal inferior esquerda para superior direita (centro)
            FVector StartPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
                FAURACRONMapDimensions::MID_LANE_START_X,
                FAURACRONMapDimensions::MID_LANE_START_Y,
                EnvironmentHeight
            );
            FVector EndPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
                FAURACRONMapDimensions::MID_LANE_END_X,
                FAURACRONMapDimensions::MID_LANE_END_Y,
                EnvironmentHeight
            );

            // Mid lane sempre tem mais pontos (mais importante)
            int32 NumPoints = (Environment == EAURACRONEnvironmentType::PurgatoryRealm) ? 40 : 30;

            for (int32 i = 0; i <= NumPoints; ++i)
            {
                float T = static_cast<float>(i) / NumPoints;
                FVector BasePoint = FMath::Lerp(StartPoint, EndPoint, T);

                // Aplicar modificações específicas do ambiente
                if (Environment == EAURACRONEnvironmentType::ZephyrFirmament)
                {
                    // Elevação suave no centro
                    float CenterBoost = (1.0f - FMath::Abs(T - 0.5f) * 2.0f) * 500.0f; // +5m no centro
                    BasePoint.Z += CenterBoost;
                }
                else if (Environment == EAURACRONEnvironmentType::PurgatoryRealm)
                {
                    // Depressão no centro (vale espectral)
                    float CenterDip = (1.0f - FMath::Abs(T - 0.5f) * 2.0f) * -300.0f; // -3m no centro
                    BasePoint.Z += CenterDip;
                }

                LanePoints.Add(BasePoint);
            }
        }
        break;

    case EAURACRONLaneType::BotLane:
        {
            // Bot Lane: espelho da Top Lane
            FVector StartPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
                FAURACRONMapDimensions::BOT_LANE_START_X,
                FAURACRONMapDimensions::BOT_LANE_START_Y,
                EnvironmentHeight
            );
            FVector EndPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
                FAURACRONMapDimensions::BOT_LANE_END_X,
                FAURACRONMapDimensions::BOT_LANE_END_Y,
                EnvironmentHeight
            );

            // Mesmo tratamento que Top Lane (simetria)
            int32 NumPoints = 25;
            for (int32 i = 0; i <= NumPoints; ++i)
            {
                float T = static_cast<float>(i) / NumPoints;
                FVector LanePoint = FMath::Lerp(StartPoint, EndPoint, T);
                LanePoints.Add(LanePoint);
            }
        }
        break;

    case EAURACRONLaneType::PrismalFlow:
        {
            // Prismal Flow: conecta objetivos principais (River equivalent)
            TArray<FVector> ObjectivePositions = UAURACRONMapMeasurements::GetStrategicObjectivePositions();

            if (ObjectivePositions.Num() >= 2)
            {
                FVector PrismalNexus = ObjectivePositions[0]; // Superior
                FVector RadiantAnchor = ObjectivePositions[1]; // Inferior

                // Ajustar altura para o ambiente
                PrismalNexus.Z = EnvironmentHeight;
                RadiantAnchor.Z = EnvironmentHeight;

                // Criar curva serpentina conectando os objetivos
                int32 NumPoints = 20;
                for (int32 i = 0; i <= NumPoints; ++i)
                {
                    float T = static_cast<float>(i) / NumPoints;
                    FVector BasePoint = FMath::Lerp(RadiantAnchor, PrismalNexus, T);

                    // Adicionar curvatura serpentina
                    float CurveOffset = FMath::Sin(T * PI * 2) * 1500.0f; // ±15m
                    FVector CurveDirection = FVector::CrossProduct(
                        (PrismalNexus - RadiantAnchor).GetSafeNormal(),
                        FVector::UpVector
                    );

                    FVector FlowPoint = BasePoint + CurveDirection * CurveOffset;

                    // Aplicar características do ambiente
                    if (Environment == EAURACRONEnvironmentType::ZephyrFirmament)
                    {
                        FlowPoint.Z += FMath::Sin(T * PI * 4) * 200.0f; // Ondulação aérea
                    }
                    else if (Environment == EAURACRONEnvironmentType::PurgatoryRealm)
                    {
                        FlowPoint.Z -= 100.0f; // Mais profundo
                    }

                    LanePoints.Add(FlowPoint);
                }
            }
        }
        break;
    }

    return LanePoints;
}

float AAURACRONPCGLaneSystem::GetEnvironmentHeight(EAURACRONEnvironmentType Environment) const
{
    switch (Environment)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        return FAURACRONMapDimensions::RADIANT_PLAINS_HEIGHT_CM;

    case EAURACRONEnvironmentType::ZephyrFirmament:
        return FAURACRONMapDimensions::ZEPHYR_FIRMAMENT_HEIGHT_CM;

    case EAURACRONEnvironmentType::PurgatoryRealm:
        return FAURACRONMapDimensions::PURGATORY_REALM_HEIGHT_CM;

    default:
        return 0.0f;
    }
}

void AAURACRONPCGLaneSystem::GenerateJungleCamps()
{
    if (!HasAuthority())
    {
        return;
    }
    
    JungleCamps.Empty();
    
    // Gerar camps baseados no layout do LoL
    // Jungle superior (lado azul)
    GenerateJungleCampsForRegion(
        FAURACRONMapDimensions::MAP_CENTER + FVector(-3000.0f, 3000.0f, 0.0f),
        2000.0f, 4, this->CurrentEnvironment
    );
    
    // Jungle inferior (lado vermelho)
    GenerateJungleCampsForRegion(
        FAURACRONMapDimensions::MAP_CENTER + FVector(3000.0f, -3000.0f, 0.0f),
        2000.0f, 4, this->CurrentEnvironment
    );
    
    // Jungle central (neutro)
    GenerateJungleCampsForRegion(
        FAURACRONMapDimensions::MAP_CENTER,
        1500.0f, 3, this->CurrentEnvironment
    );
    
    // Camps especiais (equivalente aos buffs do LoL)
    
    // Blue Buff equivalent (Radiant Energy)
    FAURACRONJungleCamp RadiantCamp;
    RadiantCamp.Position = FAURACRONMapDimensions::MAP_CENTER + FVector(-4000.0f, 2000.0f, 0.0f);
    RadiantCamp.Radius = 800.0f;
    RadiantCamp.MonsterType = TEXT("RadiantGuardian");
    RadiantCamp.RespawnTime = 300.0f; // 5 minutos
    RadiantCamp.bIsBuffCamp = true;
    RadiantCamp.DifficultyLevel = 3;
    JungleCamps.Add(RadiantCamp);
    
    // Red Buff equivalent (Chaos Energy)
    FAURACRONJungleCamp ChaosCamp;
    ChaosCamp.Position = FAURACRONMapDimensions::MAP_CENTER + FVector(4000.0f, -2000.0f, 0.0f);
    ChaosCamp.Radius = 800.0f;
    ChaosCamp.MonsterType = TEXT("ChaosGuardian");
    ChaosCamp.RespawnTime = 300.0f; // 5 minutos
    ChaosCamp.bIsBuffCamp = true;
    ChaosCamp.DifficultyLevel = 3;
    JungleCamps.Add(ChaosCamp);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGLaneSystem: Gerados %d camps da jungle"), JungleCamps.Num());
}

void AAURACRONPCGLaneSystem::GenerateDefensiveStructures()
{
    if (!HasAuthority())
    {
        return;
    }
    
    // Limpar estruturas anteriores
    FAURACRONActorArray& DefensiveStructures = DefensiveStructuresByEnvironment.FindOrAdd(this->CurrentEnvironment);
    for (AActor* Structure : DefensiveStructures.Actors)
    {
        if (IsValid(Structure))
        {
            Structure->Destroy();
        }
    }
    DefensiveStructures.Actors.Empty();
    
    // Gerar torres para cada lane
    for (const auto& LanePair : LaneInfos)
    {
        EAURACRONLaneType LaneType = LanePair.Key;
        const FAURACRONLaneInfo& LaneInfo = LanePair.Value;
        
        // Calcular posições das torres
        TArray<FVector> TowerPositions = CalculateTowerPositions(LaneType, this->CurrentEnvironment);
        
        for (int32 i = 0; i < TowerPositions.Num(); ++i)
        {
            EAURACRONDefensiveStructure StructureType;
            
            // Determinar tipo da torre baseado na posição
            if (i == 0)
            {
                StructureType = EAURACRONDefensiveStructure::OuterTower;
            }
            else if (i == 1)
            {
                StructureType = EAURACRONDefensiveStructure::InnerTower;
            }
            else
            {
                StructureType = EAURACRONDefensiveStructure::InhibitorTower;
            }
            
            AActor* Tower = GenerateDefensiveStructure(StructureType, TowerPositions[i], this->CurrentEnvironment);
            if (Tower)
            {
                DefensiveStructures.Actors.Add(Tower);
            }
        }
    }
    
    // Gerar bases (Nexus)
    TArray<FVector> BasePositions = UAURACRONMapMeasurements::GetBasePositions();
    for (const FVector& BasePos : BasePositions)
    {
        AActor* Nexus = GenerateDefensiveStructure(EAURACRONDefensiveStructure::Nexus, BasePos, this->CurrentEnvironment);
        if (Nexus)
        {
            DefensiveStructures.Actors.Add(Nexus);
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGLaneSystem: Geradas %d estruturas defensivas"), DefensiveStructures.Actors.Num());
}

FAURACRONLaneInfo AAURACRONPCGLaneSystem::GetLaneInfo(EAURACRONLaneType LaneType) const
{
    if (const FAURACRONLaneInfo* FoundInfo = LaneInfos.Find(LaneType))
    {
        return *FoundInfo;
    }
    
    return FAURACRONLaneInfo();
}

bool AAURACRONPCGLaneSystem::IsPositionInLane(const FVector& Position, EAURACRONLaneType LaneType, float Tolerance) const
{
    const FAURACRONLaneInfo* LaneInfo = LaneInfos.Find(LaneType);
    if (!LaneInfo)
    {
        return false;
    }

    // Obter pontos da lane para o ambiente atual
    const FAURACRONVectorArray* LanePointsArray = LaneInfo->LanePointsByEnvironment.Find(this->CurrentEnvironment);
    if (!LanePointsArray || LanePointsArray->Vectors.Num() < 2)
    {
        return false;
    }

    // Verificar distância mínima para qualquer ponto da lane
    float MinDistance = MAX_FLT;
    for (const FVector& LanePoint : LanePointsArray->Vectors)
    {
        float Distance = FVector::Dist2D(Position, LanePoint);
        MinDistance = FMath::Min(MinDistance, Distance);
    }
    
    return MinDistance <= Tolerance;
}

EAURACRONLaneType AAURACRONPCGLaneSystem::GetClosestLane(const FVector& Position) const
{
    float MinDistance = MAX_FLT;
    EAURACRONLaneType ClosestLane = EAURACRONLaneType::MidLane;
    
    // Verificar distância para cada lane
    for (const auto& LanePair : LaneInfos)
    {
        EAURACRONLaneType LaneType = LanePair.Key;
        const FAURACRONLaneInfo& LaneInfo = LanePair.Value;
        
        // Obter pontos da lane para o ambiente atual
        const FAURACRONVectorArray* LanePointsArray = LaneInfo.LanePointsByEnvironment.Find(this->CurrentEnvironment);
        if (LanePointsArray)
        {
            // Encontrar ponto mais próximo nesta lane
            for (const FVector& LanePoint : LanePointsArray->Vectors)
            {
                float Distance = FVector::Dist2D(Position, LanePoint);
                if (Distance < MinDistance)
                {
                    MinDistance = Distance;
                    ClosestLane = LaneType;
                }
            }
        }
    }
    
    return ClosestLane;
}

void AAURACRONPCGLaneSystem::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    if (CurrentMapPhase != MapPhase)
    {
        CurrentMapPhase = MapPhase;
        ApplyMapPhaseEffects();
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGLaneSystem: Atualizado para fase %d"), static_cast<int32>(MapPhase));
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES INTERNAS
// ========================================

void AAURACRONPCGLaneSystem::InitializeLaneInfos()
{
    LaneInfos.Empty();

    // Configurar Top Lane
    FAURACRONLaneInfo TopLaneInfo;
    TopLaneInfo.LaneType = EAURACRONLaneType::TopLane;

    // Configurar pontos para cada ambiente
    FAURACRONVectorArray TopLanePoints;
    TopLanePoints.Vectors = UAURACRONMapMeasurements::GetTopLanePoints();
    TopLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::RadiantPlains, TopLanePoints);
    TopLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::ZephyrFirmament, TopLanePoints);
    TopLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::PurgatoryRealm, TopLanePoints);

    TopLaneInfo.LaneWidth = FAURACRONMapDimensions::LANE_WIDTH_CM;
    TopLaneInfo.bIsActive = true;
    LaneInfos.Add(EAURACRONLaneType::TopLane, TopLaneInfo);

    // Configurar Mid Lane
    FAURACRONLaneInfo MidLaneInfo;
    MidLaneInfo.LaneType = EAURACRONLaneType::MidLane;

    // Configurar pontos para cada ambiente
    FAURACRONVectorArray MidLanePoints;
    MidLanePoints.Vectors = UAURACRONMapMeasurements::GetMidLanePoints();
    MidLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::RadiantPlains, MidLanePoints);
    MidLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::ZephyrFirmament, MidLanePoints);
    MidLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::PurgatoryRealm, MidLanePoints);

    MidLaneInfo.LaneWidth = FAURACRONMapDimensions::LANE_WIDTH_CM;
    MidLaneInfo.bIsActive = true;
    LaneInfos.Add(EAURACRONLaneType::MidLane, MidLaneInfo);

    // Configurar Bot Lane
    FAURACRONLaneInfo BotLaneInfo;
    BotLaneInfo.LaneType = EAURACRONLaneType::BotLane;

    // Configurar pontos para cada ambiente
    FAURACRONVectorArray BotLanePoints;
    BotLanePoints.Vectors = UAURACRONMapMeasurements::GetBotLanePoints();
    BotLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::RadiantPlains, BotLanePoints);
    BotLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::ZephyrFirmament, BotLanePoints);
    BotLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::PurgatoryRealm, BotLanePoints);

    BotLaneInfo.LaneWidth = FAURACRONMapDimensions::LANE_WIDTH_CM;
    BotLaneInfo.bIsActive = true;
    LaneInfos.Add(EAURACRONLaneType::BotLane, BotLaneInfo);

    // Configurar River (baseado no LoL)
    FAURACRONLaneInfo RiverInfo;
    RiverInfo.LaneType = EAURACRONLaneType::River;

    // River conecta os objetivos principais (Baron e Dragon equivalents)
    TArray<FVector> ObjectivePositions = UAURACRONMapMeasurements::GetStrategicObjectivePositions();
    if (ObjectivePositions.Num() >= 2)
    {
        // Criar curva do river conectando os objetivos
        FVector BaronPos = ObjectivePositions[0]; // Objetivo superior
        FVector DragonPos = ObjectivePositions[1]; // Objetivo inferior

        FAURACRONVectorArray RiverPoints;

        // Gerar pontos curvos do river
        int32 NumRiverPoints = 15;
        for (int32 i = 0; i <= NumRiverPoints; ++i)
        {
            float T = static_cast<float>(i) / NumRiverPoints;

            // Criar curva suave
            FVector BasePoint = FMath::Lerp(DragonPos, BaronPos, T);

            // Adicionar curvatura lateral
            float CurveOffset = FMath::Sin(T * PI) * 1500.0f; // 15m de curvatura máxima
            FVector CurveDirection = FVector::CrossProduct(
                (BaronPos - DragonPos).GetSafeNormal(),
                FVector::UpVector
            );

            FVector RiverPoint = BasePoint + CurveDirection * CurveOffset;
            RiverPoints.Vectors.Add(RiverPoint);
        }

        // Configurar pontos para cada ambiente
        RiverInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::RadiantPlains, RiverPoints);
        RiverInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::ZephyrFirmament, RiverPoints);
        RiverInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::PurgatoryRealm, RiverPoints);
    }

    RiverInfo.LaneWidth = 1200.0f; // River é mais largo que lanes normais
    RiverInfo.bIsActive = true;
    LaneInfos.Add(EAURACRONLaneType::River, RiverInfo);
}

void AAURACRONPCGLaneSystem::GenerateLane(EAURACRONLaneType LaneType, EAURACRONEnvironmentType Environment)
{
    const FAURACRONLaneInfo* LaneInfo = LaneInfos.Find(LaneType);
    if (!LaneInfo)
    {
        return;
    }

    // Obter pontos da lane para o ambiente especificado
    const FAURACRONVectorArray* LanePointsArray = LaneInfo->LanePointsByEnvironment.Find(Environment);
    if (!LanePointsArray || LanePointsArray->Vectors.Num() < 2)
    {
        return;
    }

    // Criar spline para a lane
    USplineComponent* LaneSpline = CreateLaneSpline(LaneType, Environment, LanePointsArray->Vectors);
    if (LaneSpline)
    {
        LaneSplines.Add(LaneType, LaneSpline);

        // Gerar mesh visual da lane
        GenerateLaneMesh(LaneType, Environment, LaneSpline);
    }
}

USplineComponent* AAURACRONPCGLaneSystem::CreateLaneSpline(EAURACRONLaneType LaneType, EAURACRONEnvironmentType Environment, const TArray<FVector>& Points)
{
    // Criar nome único para o componente
    FString SplineName = FString::Printf(TEXT("LaneSpline_%d"), static_cast<int32>(LaneType));

    // Criar componente spline dinamicamente (UE 5.6 API)
    USplineComponent* Spline = NewObject<USplineComponent>(this, USplineComponent::StaticClass(), *SplineName);
    if (!Spline)
    {
        return nullptr;
    }

    Spline->SetupAttachment(this->RootComponent);
    Spline->ClearSplinePoints();

    // Adicionar pontos à spline
    for (int32 i = 0; i < Points.Num(); ++i)
    {
        FVector LocalPoint = Points[i] - this->GetActorLocation();
        Spline->AddSplinePoint(LocalPoint, ESplineCoordinateSpace::Local);
        Spline->SetSplinePointType(i, ESplinePointType::CurveClamped);
    }

    // Atualizar tangentes para curvas suaves
    Spline->UpdateSpline();

    return Spline;
}

void AAURACRONPCGLaneSystem::GenerateLaneMesh(EAURACRONLaneType LaneType, EAURACRONEnvironmentType Environment, USplineComponent* Spline)
{
    if (!Spline)
    {
        return;
    }

    // Criar múltiplos segmentos de mesh ao longo da spline
    int32 NumSegments = FMath::Max(1, Spline->GetNumberOfSplinePoints() - 1);

    for (int32 i = 0; i < NumSegments; ++i)
    {
        // Criar componente de spline mesh
        FString MeshName = FString::Printf(TEXT("LaneMesh_%d_%d"), static_cast<int32>(LaneType), i);
        // Criar componente spline mesh dinamicamente (UE 5.6 API)
        USplineMeshComponent* SplineMesh = NewObject<USplineMeshComponent>(this, USplineMeshComponent::StaticClass(), *MeshName);

        if (SplineMesh)
        {
            SplineMesh->SetupAttachment(this->RootComponent);

            // Configurar segmento usando SetStartAndEnd (UE 5.6 API)
            SplineMesh->SetStartAndEnd(
                Spline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::Local),
                Spline->GetTangentAtSplinePoint(i, ESplineCoordinateSpace::Local),
                Spline->GetLocationAtSplinePoint(i + 1, ESplineCoordinateSpace::Local),
                Spline->GetTangentAtSplinePoint(i + 1, ESplineCoordinateSpace::Local)
            );

            // Configurar escala baseada na largura da lane
            const FAURACRONLaneInfo* LaneInfo = LaneInfos.Find(LaneType);
            if (LaneInfo)
            {
                float ScaleY = LaneInfo->LaneWidth / 100.0f; // Normalizar para escala
                SplineMesh->SetStartScale(FVector2D(1.0f, ScaleY));
                SplineMesh->SetEndScale(FVector2D(1.0f, ScaleY));
            }

            LaneMeshComponents.Add(SplineMesh);

            // Adicionar ao array de componentes gerados para o ambiente atual
            // Adicionar ao array de componentes gerados para o ambiente especificado
            FAURACRONComponentArray& GeneratedComponents = GeneratedComponentsByEnvironment.FindOrAdd(Environment);
            GeneratedComponents.Components.Add(SplineMesh);
        }
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

TArray<FVector> AAURACRONPCGLaneSystem::GetLanePointsForEnvironment(EAURACRONLaneType LaneType, EAURACRONEnvironmentType EnvironmentType) const
{
    // Obter pontos da lane para ambiente específico usando APIs modernas do UE 5.6
    TArray<FVector> LanePoints;

    // Usar sistema moderno de medições do mapa
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    float LaneLength = FAURACRONMapDimensions::LANE_LENGTH_CM;
    float LaneWidth = FAURACRONMapDimensions::LANE_WIDTH_CM;

    // Calcular pontos baseados no tipo de lane usando algoritmos modernos
    switch (LaneType)
    {
        case EAURACRONLaneType::TopLane:
        {
            FVector StartPoint = MapCenter + FVector(-LaneLength * 0.5f, LaneWidth * 0.5f, 0.0f);
            FVector EndPoint = MapCenter + FVector(LaneLength * 0.5f, LaneWidth * 0.5f, 0.0f);

            // Gerar pontos ao longo da lane usando curva serpentina moderna
            FAURACRONSplineCurve LaneCurve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
                StartPoint, EndPoint, 20, 200.0f, 1.0f
            );

            // Distribuir pontos ao longo da curva
            for (int32 i = 0; i <= 50; ++i)
            {
                float T = static_cast<float>(i) / 50.0f;
                FVector Point = UAURACRONPCGMathLibrary::EvaluateSplineCurve(LaneCurve, T);
                LanePoints.Add(Point);
            }
            break;
        }

        case EAURACRONLaneType::MidLane:
        {
            FVector StartPoint = MapCenter + FVector(-LaneLength * 0.5f, 0.0f, 0.0f);
            FVector EndPoint = MapCenter + FVector(LaneLength * 0.5f, 0.0f, 0.0f);

            // Lane do meio é mais reta
            FAURACRONSplineCurve LaneCurve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
                StartPoint, EndPoint, 15, 100.0f, 0.5f
            );

            for (int32 i = 0; i <= 50; ++i)
            {
                float T = static_cast<float>(i) / 50.0f;
                FVector Point = UAURACRONPCGMathLibrary::EvaluateSplineCurve(LaneCurve, T);
                LanePoints.Add(Point);
            }
            break;
        }

        case EAURACRONLaneType::BotLane:
        {
            FVector StartPoint = MapCenter + FVector(-LaneLength * 0.5f, -LaneWidth * 0.5f, 0.0f);
            FVector EndPoint = MapCenter + FVector(LaneLength * 0.5f, -LaneWidth * 0.5f, 0.0f);

            FAURACRONSplineCurve LaneCurve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
                StartPoint, EndPoint, 20, 200.0f, 1.0f
            );

            for (int32 i = 0; i <= 50; ++i)
            {
                float T = static_cast<float>(i) / 50.0f;
                FVector Point = UAURACRONPCGMathLibrary::EvaluateSplineCurve(LaneCurve, T);
                LanePoints.Add(Point);
            }
            break;
        }

        case EAURACRONLaneType::River:
        {
            // Rio atravessa diagonalmente o mapa
            FVector StartPoint = MapCenter + FVector(-LaneLength * 0.3f, -LaneWidth * 0.3f, -50.0f);
            FVector EndPoint = MapCenter + FVector(LaneLength * 0.3f, LaneWidth * 0.3f, -50.0f);

            FAURACRONSplineCurve RiverCurve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
                StartPoint, EndPoint, 25, 400.0f, 2.0f
            );

            for (int32 i = 0; i <= 60; ++i)
            {
                float T = static_cast<float>(i) / 60.0f;
                FVector Point = UAURACRONPCGMathLibrary::EvaluateSplineCurve(RiverCurve, T);
                LanePoints.Add(Point);
            }
            break;
        }
    }

    // Aplicar modificações baseadas no ambiente usando APIs modernas
    ApplyEnvironmentModificationsToPoints(LanePoints, EnvironmentType);

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGLaneSystem::GetLanePointsForEnvironment - Generated %d points for lane type %d in environment %d"),
           LanePoints.Num(), (int32)LaneType, (int32)EnvironmentType);

    return LanePoints;
}

void AAURACRONPCGLaneSystem::ApplyEnvironmentCharacteristics(EAURACRONEnvironmentType EnvironmentType)
{
    // Aplicar características do ambiente usando APIs modernas do UE 5.6
    if (!IsValid(this))
    {
        return;
    }

    // Obter configurações do ambiente usando sistema moderno
    FAURACRONEnvironmentConfig EnvironmentConfig = GetEnvironmentConfiguration(EnvironmentType);

    // Aplicar características aos componentes usando APIs modernas
    TArray<UActorComponent*> Components = GetComponents().Array();
    for (UActorComponent* Component : Components)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            // Aplicar material dinâmico baseado no ambiente
            if (UMaterialInterface* BaseMaterial = MeshComp->GetMaterial(0))
            {
                if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
                {
                    // Aplicar parâmetros baseados no ambiente
                    DynamicMaterial->SetVectorParameterValue(FName(TEXT("EnvironmentColor")), EnvironmentConfig.PrimaryColor);
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("EnvironmentIntensity")), EnvironmentConfig.LightIntensity);
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("EnvironmentRoughness")), EnvironmentConfig.MaterialRoughness);

                    // Aplicar efeitos específicos do ambiente
                    switch (EnvironmentType)
                    {
                        case EAURACRONEnvironmentType::RadiantPlains:
                            DynamicMaterial->SetScalarParameterValue(FName(TEXT("CrystalReflection")), 0.8f);
                            DynamicMaterial->SetVectorParameterValue(FName(TEXT("CrystalTint")), FLinearColor(0.7f, 0.9f, 1.0f));
                            break;

                        case EAURACRONEnvironmentType::ZephyrFirmament:
                            DynamicMaterial->SetScalarParameterValue(FName(TEXT("OrganicPulse")), 1.2f);
                            DynamicMaterial->SetVectorParameterValue(FName(TEXT("OrganicGrowth")), FLinearColor(0.2f, 0.8f, 0.3f));
                            break;

                        case EAURACRONEnvironmentType::PurgatoryRealm:
                        {
                            float BreathingCycle = FMath::Sin(GetWorld()->GetTimeSeconds() * 0.5f) * 0.5f + 0.5f;
                            DynamicMaterial->SetScalarParameterValue(FName(TEXT("ForestBreathing")), BreathingCycle);
                            DynamicMaterial->SetVectorParameterValue(FName(TEXT("ForestLife")), FLinearColor(0.1f, 0.9f, 0.2f));
                            break;
                        }

                        default:
                            // Aplicar características padrão
                            DynamicMaterial->SetScalarParameterValue(FName(TEXT("DefaultGlow")), 0.5f);
                            break;
                    }
                }
            }
        }
    }

    // Atualizar ambiente atual
    CurrentEnvironment = EnvironmentType;

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGLaneSystem::ApplyEnvironmentCharacteristics - Applied characteristics for environment %d using modern UE 5.6 APIs"),
           (int32)EnvironmentType);
}

void AAURACRONPCGLaneSystem::GenerateJungleCampsForRegion(const FVector& RegionCenter, float RegionRadius, int32 CampCount, EAURACRONEnvironmentType EnvironmentType)
{
    // Gerar camps da jungle para região específica usando APIs modernas do UE 5.6
    if (CampCount <= 0 || RegionRadius <= 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGLaneSystem::GenerateJungleCampsForRegion - Invalid parameters"));
        return;
    }

    // Usar algoritmo moderno de distribuição espacial
    TArray<FVector> CampPositions = UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(
        RegionCenter,
        RegionRadius,
        RegionRadius / FMath::Sqrt(static_cast<float>(CampCount)), // Distância mínima baseada na densidade desejada
        30, // Máximo de tentativas
        FMath::Rand() // Seed aleatório
    );

    // Limitar ao número desejado de camps
    if (CampPositions.Num() > CampCount)
    {
        CampPositions.SetNum(CampCount);
    }

    // Gerar camps usando APIs modernas
    for (int32 i = 0; i < CampPositions.Num(); ++i)
    {
        FVector CampPosition = CampPositions[i];

        // Ajustar altura baseada no terreno usando API moderna
        if (UWorld* World = GetWorld())
        {
            FHitResult HitResult;
            FVector TraceStart = CampPosition + FVector(0.0f, 0.0f, 1000.0f);
            FVector TraceEnd = CampPosition - FVector(0.0f, 0.0f, 1000.0f);

            if (World->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic))
            {
                CampPosition.Z = HitResult.Location.Z + 50.0f; // 0.5 metros acima do solo
            }
        }

        // Criar camp usando sistema moderno de spawn
        FActorSpawnParameters SpawnParams;
        SpawnParams.Name = FName(*FString::Printf(TEXT("JungleCamp_%d_%d"), (int32)EnvironmentType, i));
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

        // Determinar tipo de camp baseado na posição e ambiente
        EAURACRONJungleCampType CampType = DetermineCampType(CampPosition, EnvironmentType);

        // Spawnar camp usando API moderna
        if (AActor* CampActor = GetWorld()->SpawnActor<AActor>(SpawnParams))
        {
            // Configurar componentes do camp
            if (UStaticMeshComponent* CampMesh = CampActor->CreateDefaultSubobject<UStaticMeshComponent>(TEXT("CampMesh")))
            {
                CampActor->SetRootComponent(CampMesh);

                // Aplicar mesh baseado no tipo de camp e ambiente
                ApplyCampMeshAndMaterial(CampMesh, CampType, EnvironmentType);
            }

            // Adicionar ao registro de camps gerados
            FAURACRONComponentArray& GeneratedComponents = GeneratedComponentsByEnvironment.FindOrAdd(EnvironmentType);
            if (UStaticMeshComponent* CampMeshComp = CampActor->FindComponentByClass<UStaticMeshComponent>())
            {
                GeneratedComponents.Components.Add(CampMeshComp);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGLaneSystem::GenerateJungleCampsForRegion - Generated %d jungle camps for environment %d using modern UE 5.6 APIs"),
           CampPositions.Num(), (int32)EnvironmentType);
}

AActor* AAURACRONPCGLaneSystem::GenerateDefensiveStructure(EAURACRONDefensiveStructure StructureType, const FVector& Position, EAURACRONEnvironmentType EnvironmentType)
{
    // Gerar estrutura defensiva usando APIs modernas do UE 5.6
    if (!IsValid(GetWorld()))
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGLaneSystem::GenerateDefensiveStructure - Invalid world"));
        return nullptr;
    }

    // Configurar parâmetros de spawn usando sistema moderno
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*FString::Printf(TEXT("DefensiveStructure_%d_%d"), (int32)StructureType, (int32)EnvironmentType));
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    // Spawnar estrutura usando API moderna
    AActor* StructureActor = GetWorld()->SpawnActor<AActor>(SpawnParams);
    if (!StructureActor)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGLaneSystem::GenerateDefensiveStructure - Failed to spawn structure"));
        return nullptr;
    }

    // Posicionar estrutura
    StructureActor->SetActorLocation(Position);

    // Criar componente de mesh usando API moderna
    UStaticMeshComponent* StructureMesh = StructureActor->CreateDefaultSubobject<UStaticMeshComponent>(TEXT("StructureMesh"));
    if (StructureMesh)
    {
        StructureActor->SetRootComponent(StructureMesh);

        // Aplicar mesh e material baseado no tipo de estrutura e ambiente
        ApplyStructureMeshAndMaterial(StructureMesh, StructureType, EnvironmentType);

        // Configurar propriedades físicas usando APIs modernas
        StructureMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        StructureMesh->SetCollisionObjectType(ECC_WorldStatic);
        StructureMesh->SetCollisionResponseToAllChannels(ECR_Block);
    }

    // Aplicar efeitos específicos do ambiente
    ApplyEnvironmentEffectsToStructure(StructureActor, EnvironmentType);

    // Adicionar ao registro de estruturas geradas
    FAURACRONComponentArray& GeneratedComponents = GeneratedComponentsByEnvironment.FindOrAdd(EnvironmentType);
    if (UStaticMeshComponent* StructureMeshComp = StructureActor->FindComponentByClass<UStaticMeshComponent>())
    {
        GeneratedComponents.Components.Add(StructureMeshComp);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGLaneSystem::GenerateDefensiveStructure - Generated defensive structure type %d for environment %d using modern UE 5.6 APIs"),
           (int32)StructureType, (int32)EnvironmentType);

    return StructureActor;
}

TArray<FVector> AAURACRONPCGLaneSystem::CalculateTowerPositions(EAURACRONLaneType LaneType, EAURACRONEnvironmentType EnvironmentType)
{
    // Calcular posições de torres usando APIs modernas do UE 5.6
    TArray<FVector> TowerPositions;

    // Obter pontos da lane para o ambiente específico
    TArray<FVector> LanePoints = GetLanePointsForEnvironment(LaneType, EnvironmentType);

    if (LanePoints.Num() < 2)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGLaneSystem::CalculateTowerPositions - Insufficient lane points"));
        return TowerPositions;
    }

    // Usar algoritmo moderno para distribuir torres ao longo da lane
    float LaneLength = 0.0f;
    for (int32 i = 1; i < LanePoints.Num(); ++i)
    {
        LaneLength += FVector::Dist(LanePoints[i-1], LanePoints[i]);
    }

    // Calcular número de torres baseado no comprimento da lane
    int32 TowerCount = FMath::Max(3, FMath::FloorToInt(LaneLength / 1500.0f)); // Uma torre a cada 15 metros

    // Distribuir torres uniformemente ao longo da lane
    for (int32 i = 0; i < TowerCount; ++i)
    {
        float T = static_cast<float>(i) / static_cast<float>(TowerCount - 1);
        int32 PointIndex = FMath::FloorToInt(T * (LanePoints.Num() - 1));

        if (PointIndex < LanePoints.Num())
        {
            FVector TowerPosition = LanePoints[PointIndex];

            // Aplicar offset baseado no ambiente
            FVector EnvironmentOffset = GetEnvironmentTowerOffset(EnvironmentType);
            TowerPosition += EnvironmentOffset;

            // Ajustar altura para torres
            TowerPosition.Z += 100.0f; // 1 metro acima do solo

            TowerPositions.Add(TowerPosition);
        }
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGLaneSystem::CalculateTowerPositions - Calculated %d tower positions for lane type %d in environment %d"),
           TowerPositions.Num(), (int32)LaneType, (int32)EnvironmentType);

    return TowerPositions;
}

void AAURACRONPCGLaneSystem::ApplyMapPhaseEffects()
{
    // Aplicar efeitos da fase do mapa usando APIs modernas do UE 5.6
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Obter fase atual do mapa usando subsistema moderno
    if (UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>())
    {
        EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();

        // Aplicar efeitos baseados na fase
        float PhaseIntensity = 1.0f;
        FLinearColor PhaseColor = FLinearColor::White;

        switch (CurrentPhase)
        {
            case EAURACRONMapPhase::Awakening:
                PhaseIntensity = 0.7f;
                PhaseColor = FLinearColor(1.0f, 0.8f, 0.6f, 1.0f); // Dourado suave
                break;

            case EAURACRONMapPhase::Convergence:
                PhaseIntensity = 1.0f;
                PhaseColor = FLinearColor(1.0f, 1.0f, 1.0f, 1.0f); // Branco puro
                break;

            case EAURACRONMapPhase::Intensification:
                PhaseIntensity = 0.8f;
                PhaseColor = FLinearColor(1.0f, 0.6f, 0.4f, 1.0f); // Laranja avermelhado
                break;

            case EAURACRONMapPhase::Resolution:
                PhaseIntensity = 0.5f;
                PhaseColor = FLinearColor(0.4f, 0.6f, 1.0f, 1.0f); // Azul noturno
                break;

            default:
                PhaseIntensity = 0.8f;
                PhaseColor = FLinearColor(0.8f, 0.8f, 0.8f, 1.0f); // Cinza neutro
                break;
        }

        // Aplicar efeitos a todos os componentes gerados
        for (auto& EnvironmentPair : GeneratedComponentsByEnvironment)
        {
            FAURACRONComponentArray& ComponentArray = EnvironmentPair.Value;

            for (UObject* Component : ComponentArray.Components)
            {
                if (AActor* Actor = Cast<AActor>(Component))
                {
                    ApplyPhaseEffectsToActor(Actor, PhaseIntensity, PhaseColor);
                }
                else if (UActorComponent* ActorComponent = Cast<UActorComponent>(Component))
                {
                    ApplyPhaseEffectsToComponent(ActorComponent, PhaseIntensity, PhaseColor);
                }
            }
        }

        // Atualizar fase atual
        CurrentMapPhase = CurrentPhase;

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGLaneSystem::ApplyMapPhaseEffects - Applied phase %d effects with intensity %.2f using modern UE 5.6 APIs"),
               (int32)CurrentPhase, PhaseIntensity);
    }
}

void AAURACRONPCGLaneSystem::ClearAllGeneratedElements()
{
    // Limpar todos os elementos gerados usando APIs modernas do UE 5.6
    int32 ClearedCount = 0;

    // Usar algoritmo moderno para limpeza eficiente
    for (auto& EnvironmentPair : GeneratedComponentsByEnvironment)
    {
        FAURACRONComponentArray& ComponentArray = EnvironmentPair.Value;

        // Usar algoritmo moderno de limpeza com validação
        for (int32 i = ComponentArray.Components.Num() - 1; i >= 0; --i)
        {
            UObject* Component = ComponentArray.Components[i];

            if (AActor* Actor = Cast<AActor>(Component))
            {
                if (IsValid(Actor))
                {
                    Actor->Destroy();
                    ClearedCount++;
                }
            }
            else if (UActorComponent* ActorComponent = Cast<UActorComponent>(Component))
            {
                if (IsValid(ActorComponent))
                {
                    ActorComponent->DestroyComponent();
                    ClearedCount++;
                }
            }
        }

        // Limpar array usando API moderna
        ComponentArray.Components.Empty();
    }

    // Limpar mapa de componentes gerados
    GeneratedComponentsByEnvironment.Empty();

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGLaneSystem::ClearAllGeneratedElements - Cleared %d generated elements using modern UE 5.6 APIs"),
           ClearedCount);
}

// ========================================
// IMPLEMENTAÇÃO DE INTERSECÇÃO DE TRILHOS BASEADA NA CAPACIDADE DE RENDERIZAÇÃO - FASE 3
// ========================================

void AAURACRONPCGLaneSystem::GenerateLaneIntersectionsBasedOnRenderingCapacity()
{
    // Implementar intersecção de trilhos adaptada à capacidade de renderização conforme Fase 3
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Obter informações de performance do sistema
    AAURACRONPCGPerformanceManager* PerformanceManager = nullptr;
    if (UWorld* World = GetWorld())
    {
        PerformanceManager = Cast<AAURACRONPCGPerformanceManager>(
            UGameplayStatics::GetActorOfClass(World, AAURACRONPCGPerformanceManager::StaticClass())
        );
    }

    if (!PerformanceManager)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGLaneSystem::GenerateLaneIntersectionsBasedOnRenderingCapacity - Performance Manager not found"));
        return;
    }

    // Obter configurações baseadas no dispositivo
    float QualityMultiplier = 1.0f;
    int32 MaxElements = 150;
    float MaxDistance = 4000.0f;
    PerformanceManager->GetDeviceBasedQualitySettings(QualityMultiplier, MaxElements, MaxDistance);

    EAURACRONDeviceType DeviceType = PerformanceManager->DetectDeviceType();

    // Calcular pontos de intersecção baseados na capacidade
    TArray<FVector> IntersectionPoints = CalculateLaneIntersectionPoints(DeviceType, QualityMultiplier);

    // Gerar intersecções com base na capacidade de renderização
    GenerateIntersectionElements(IntersectionPoints, DeviceType, MaxElements);

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGLaneSystem::GenerateLaneIntersectionsBasedOnRenderingCapability - Generated %d intersections for device type %d"),
           IntersectionPoints.Num(), (int32)DeviceType);
}

TArray<FVector> AAURACRONPCGLaneSystem::CalculateLaneIntersectionPoints(EAURACRONDeviceType DeviceType, float QualityMultiplier)
{
    TArray<FVector> IntersectionPoints;

    // Obter pontos das lanes principais
    TArray<FVector> TopLanePoints = GetLanePointsForEnvironment(EAURACRONLaneType::TopLane, CurrentEnvironment);
    TArray<FVector> MidLanePoints = GetLanePointsForEnvironment(EAURACRONLaneType::MidLane, CurrentEnvironment);
    TArray<FVector> BotLanePoints = GetLanePointsForEnvironment(EAURACRONLaneType::BotLane, CurrentEnvironment);
    TArray<FVector> RiverPoints = GetLanePointsForEnvironment(EAURACRONLaneType::River, CurrentEnvironment);

    // Configurar densidade de intersecções baseada no dispositivo
    int32 IntersectionDensity = 1;
    float IntersectionTolerance = 200.0f;

    switch (DeviceType)
    {
        case EAURACRONDeviceType::Entry:
            IntersectionDensity = 1; // Mínimo de intersecções
            IntersectionTolerance = 300.0f;
            break;

        case EAURACRONDeviceType::Mid:
            IntersectionDensity = 2; // Densidade média
            IntersectionTolerance = 200.0f;
            break;

        case EAURACRONDeviceType::High:
            IntersectionDensity = 3; // Máxima densidade
            IntersectionTolerance = 150.0f;
            break;

        default:
            IntersectionDensity = 2;
            IntersectionTolerance = 200.0f;
            break;
    }

    // Calcular intersecções entre Top Lane e Mid Lane
    CalculateIntersectionsBetweenLanes(TopLanePoints, MidLanePoints, IntersectionPoints, IntersectionTolerance, IntersectionDensity);

    // Calcular intersecções entre Mid Lane e Bot Lane
    CalculateIntersectionsBetweenLanes(MidLanePoints, BotLanePoints, IntersectionPoints, IntersectionTolerance, IntersectionDensity);

    // Calcular intersecções entre River e todas as lanes
    CalculateIntersectionsBetweenLanes(RiverPoints, TopLanePoints, IntersectionPoints, IntersectionTolerance, IntersectionDensity);
    CalculateIntersectionsBetweenLanes(RiverPoints, MidLanePoints, IntersectionPoints, IntersectionTolerance, IntersectionDensity);
    CalculateIntersectionsBetweenLanes(RiverPoints, BotLanePoints, IntersectionPoints, IntersectionTolerance, IntersectionDensity);

    // Aplicar multiplicador de qualidade
    int32 TargetIntersections = FMath::RoundToInt(IntersectionPoints.Num() * QualityMultiplier);
    if (IntersectionPoints.Num() > TargetIntersections)
    {
        IntersectionPoints.SetNum(TargetIntersections);
    }

    return IntersectionPoints;
}

void AAURACRONPCGLaneSystem::CalculateIntersectionsBetweenLanes(const TArray<FVector>& Lane1Points, const TArray<FVector>& Lane2Points, TArray<FVector>& OutIntersections, float Tolerance, int32 Density)
{
    // Algoritmo otimizado para calcular intersecções entre duas lanes
    for (int32 i = 0; i < Lane1Points.Num() - 1; i += Density)
    {
        FVector Lane1Start = Lane1Points[i];
        FVector Lane1End = Lane1Points[FMath::Min(i + 1, Lane1Points.Num() - 1)];

        for (int32 j = 0; j < Lane2Points.Num() - 1; j += Density)
        {
            FVector Lane2Start = Lane2Points[j];
            FVector Lane2End = Lane2Points[FMath::Min(j + 1, Lane2Points.Num() - 1)];

            // Calcular intersecção entre dois segmentos de linha
            FVector IntersectionPoint;
            if (CalculateLineIntersection2D(Lane1Start, Lane1End, Lane2Start, Lane2End, IntersectionPoint, Tolerance))
            {
                // Verificar se a intersecção já existe (evitar duplicatas)
                bool bAlreadyExists = false;
                for (const FVector& ExistingPoint : OutIntersections)
                {
                    if (FVector::Dist(ExistingPoint, IntersectionPoint) < Tolerance)
                    {
                        bAlreadyExists = true;
                        break;
                    }
                }

                if (!bAlreadyExists)
                {
                    OutIntersections.Add(IntersectionPoint);
                }
            }
        }
    }
}

bool AAURACRONPCGLaneSystem::CalculateLineIntersection2D(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, FVector& OutIntersection, float Tolerance)
{
    // Calcular intersecção entre duas linhas em 2D (ignorando Z)
    float X1 = Line1Start.X, Y1 = Line1Start.Y;
    float X2 = Line1End.X, Y2 = Line1End.Y;
    float X3 = Line2Start.X, Y3 = Line2Start.Y;
    float X4 = Line2End.X, Y4 = Line2End.Y;

    float Denominator = (X1 - X2) * (Y3 - Y4) - (Y1 - Y2) * (X3 - X4);

    // Linhas paralelas
    if (FMath::Abs(Denominator) < KINDA_SMALL_NUMBER)
    {
        return false;
    }

    float T = ((X1 - X3) * (Y3 - Y4) - (Y1 - Y3) * (X3 - X4)) / Denominator;
    float U = -((X1 - X2) * (Y1 - Y3) - (Y1 - Y2) * (X1 - X3)) / Denominator;

    // Verificar se a intersecção está dentro dos segmentos
    if (T >= 0.0f && T <= 1.0f && U >= 0.0f && U <= 1.0f)
    {
        OutIntersection.X = X1 + T * (X2 - X1);
        OutIntersection.Y = Y1 + T * (Y2 - Y1);
        OutIntersection.Z = FMath::Lerp(Line1Start.Z, Line1End.Z, T); // Interpolar altura

        return true;
    }

    return false;
}

void AAURACRONPCGLaneSystem::GenerateIntersectionElements(const TArray<FVector>& IntersectionPoints, EAURACRONDeviceType DeviceType, int32 MaxElements)
{
    // Gerar elementos visuais nas intersecções baseado na capacidade do dispositivo
    int32 ElementsToGenerate = FMath::Min(IntersectionPoints.Num(), MaxElements);

    for (int32 i = 0; i < ElementsToGenerate; ++i)
    {
        FVector IntersectionPoint = IntersectionPoints[i];

        // Criar elemento de intersecção
        FActorSpawnParameters SpawnParams;
        SpawnParams.Name = FName(*FString::Printf(TEXT("LaneIntersection_%d_%d"), (int32)CurrentEnvironment, i));
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

        if (AActor* IntersectionActor = GetWorld()->SpawnActor<AActor>(SpawnParams))
        {
            // Configurar componente visual baseado no tipo de dispositivo
            if (UStaticMeshComponent* IntersectionMesh = IntersectionActor->CreateDefaultSubobject<UStaticMeshComponent>(TEXT("IntersectionMesh")))
            {
                IntersectionActor->SetRootComponent(IntersectionMesh);
                IntersectionActor->SetActorLocation(IntersectionPoint);

                // Aplicar configurações baseadas no dispositivo
                ApplyIntersectionVisualsForDevice(IntersectionMesh, DeviceType);

                // Adicionar ao array de atores de interseção gerados
                FAURACRONActorArray& GeneratedActors = GeneratedIntersectionActorsByEnvironment.FindOrAdd(CurrentEnvironment);
                GeneratedActors.Actors.Add(IntersectionActor);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGLaneSystem::GenerateIntersectionElements - Generated %d intersection elements for device type %d"),
           ElementsToGenerate, (int32)DeviceType);
}

void AAURACRONPCGLaneSystem::ApplyIntersectionVisualsForDevice(UStaticMeshComponent* MeshComponent, EAURACRONDeviceType DeviceType)
{
    if (!MeshComponent)
    {
        return;
    }

    // Configurar visuais baseados no tipo de dispositivo
    switch (DeviceType)
    {
        case EAURACRONDeviceType::Entry:
        {
            // Visuais simples para Entry devices
            MeshComponent->SetRelativeScale3D(FVector(0.5f, 0.5f, 0.1f));
            
            if (UMaterialInstanceDynamic* DynamicMaterial = MeshComponent->CreateAndSetMaterialInstanceDynamic(0))
            {
                DynamicMaterial->SetVectorParameterValue(FName(TEXT("BaseColor")), FLinearColor(0.8f, 0.8f, 0.2f, 0.7f));
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("Metallic")), 0.1f);
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("Roughness")), 0.8f);
            }
            break;
        }

        case EAURACRONDeviceType::Mid:
        {
            // Visuais balanceados para Mid devices
            MeshComponent->SetRelativeScale3D(FVector(0.8f, 0.8f, 0.15f));
            
            if (UMaterialInstanceDynamic* DynamicMaterial = MeshComponent->CreateAndSetMaterialInstanceDynamic(0))
            {
                DynamicMaterial->SetVectorParameterValue(FName(TEXT("BaseColor")), FLinearColor(0.2f, 0.8f, 0.8f, 0.8f));
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("Metallic")), 0.3f);
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("Roughness")), 0.5f);
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("EmissiveIntensity")), 0.5f);
            }
            break;
        }

        case EAURACRONDeviceType::High:
        {
            // Visuais complexos para High devices
            MeshComponent->SetRelativeScale3D(FVector(1.2f, 1.2f, 0.2f));
            
            if (UMaterialInstanceDynamic* DynamicMaterial = MeshComponent->CreateAndSetMaterialInstanceDynamic(0))
            {
                DynamicMaterial->SetVectorParameterValue(FName(TEXT("BaseColor")), FLinearColor(0.8f, 0.2f, 0.8f, 0.9f));
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("Metallic")), 0.7f);
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("Roughness")), 0.2f);
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("EmissiveIntensity")), 1.2f);
                
                // Adicionar efeitos de pulsação para High devices
                float PulseValue = FMath::Sin(GetWorld()->GetTimeSeconds() * 2.0f) * 0.5f + 0.5f;
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("PulseIntensity")), PulseValue);
            }
            break;
        }

        default:
            // Configuração padrão
            MeshComponent->SetRelativeScale3D(FVector(0.8f, 0.8f, 0.15f));
            break;
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES AUXILIARES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGLaneSystem::ApplyEnvironmentModificationsToPoints(TArray<FVector>& Points, EAURACRONEnvironmentType EnvironmentType) const
{
    // Aplicar modificações ambientais aos pontos usando APIs modernas do UE 5.6
    if (Points.Num() == 0)
    {
        return;
    }

    // Aplicar modificações baseadas no tipo de ambiente
    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
        {
            // Planícies radiantes: suavizar pontos e adicionar brilho
            for (FVector& Point : Points)
            {
                Point.Z += FMath::Sin(Point.X * 0.01f) * 50.0f; // Ondulação suave
                Point.Z += FMath::Cos(Point.Y * 0.01f) * 30.0f; // Variação adicional
            }
            break;
        }

        case EAURACRONEnvironmentType::ZephyrFirmament:
        {
            // Firmamento Zephyr: elevar pontos e adicionar movimento aéreo
            for (FVector& Point : Points)
            {
                Point.Z += 200.0f; // Elevar 2 metros
                Point.Z += FMath::Sin(Point.X * 0.005f + Point.Y * 0.005f) * 100.0f; // Movimento flutuante
            }
            break;
        }

        case EAURACRONEnvironmentType::PurgatoryRealm:
        {
            // Reino do Purgatório: adicionar distorção e irregularidade
            for (FVector& Point : Points)
            {
                Point.X += FMath::RandRange(-100.0f, 100.0f); // Distorção aleatória
                Point.Y += FMath::RandRange(-100.0f, 100.0f);
                Point.Z += FMath::Sin(Point.X * 0.02f) * FMath::Cos(Point.Y * 0.02f) * 150.0f; // Distorção complexa
            }
            break;
        }

        default:
        {
            // Ambiente padrão: aplicar modificações mínimas
            for (FVector& Point : Points)
            {
                Point.Z += FMath::RandRange(-25.0f, 25.0f); // Variação pequena
            }
            break;
        }
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGLaneSystem::ApplyEnvironmentModificationsToPoints - Applied modifications to %d points for environment %d"),
           Points.Num(), (int32)EnvironmentType);
}

EAURACRONJungleCampType AAURACRONPCGLaneSystem::DetermineCampType(const FVector& Position, EAURACRONEnvironmentType EnvironmentType)
{
    // Determinar tipo de camp baseado na posição e ambiente usando algoritmos modernos
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    float DistanceFromCenter = FVector::Dist2D(Position, MapCenter);

    // Usar algoritmo baseado em distância e ambiente
    if (DistanceFromCenter < 1000.0f) // Dentro de 10 metros do centro
    {
        return EAURACRONJungleCampType::RadiantEssence; // Essência radiante no centro
    }
    else if (DistanceFromCenter < 2000.0f) // Entre 10 e 20 metros
    {
        // Camps de buff baseados no ambiente
        switch (EnvironmentType)
        {
            case EAURACRONEnvironmentType::RadiantPlains:
                return EAURACRONJungleCampType::RadiantEssence;
            case EAURACRONEnvironmentType::ZephyrFirmament:
                return EAURACRONJungleCampType::ChaosEssence;
            case EAURACRONEnvironmentType::PurgatoryRealm:
                return EAURACRONJungleCampType::SpectralPack;
            default:
                return EAURACRONJungleCampType::RadiantEssence;
        }
    }
    else if (DistanceFromCenter < 3000.0f) // Entre 20 e 30 metros
    {
        return EAURACRONJungleCampType::StoneGuardians;
    }
    else if (DistanceFromCenter < 4000.0f) // Entre 30 e 40 metros
    {
        return EAURACRONJungleCampType::PrismalToad;
    }
    else
    {
        return EAURACRONJungleCampType::WindSpirits;
    }
}

void AAURACRONPCGLaneSystem::ApplyCampMeshAndMaterial(UStaticMeshComponent* MeshComponent, EAURACRONJungleCampType CampType, EAURACRONEnvironmentType EnvironmentType)
{
    // Aplicar mesh e material ao camp usando APIs modernas do UE 5.6
    if (!MeshComponent || !IsValid(MeshComponent))
    {
        return;
    }

    // Configurar propriedades básicas usando APIs modernas
    MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    MeshComponent->SetCollisionObjectType(ECC_WorldStatic);
    MeshComponent->SetCollisionResponseToAllChannels(ECR_Block);

    // Aplicar escala baseada no tipo de camp
    FVector CampScale = FVector(1.0f);
    switch (CampType)
    {
        case EAURACRONJungleCampType::WindSpirits:
            CampScale = FVector(0.8f);
            break;
        case EAURACRONJungleCampType::PrismalToad:
            CampScale = FVector(1.0f);
            break;
        case EAURACRONJungleCampType::StoneGuardians:
            CampScale = FVector(1.3f);
            break;
        case EAURACRONJungleCampType::SpectralPack:
            CampScale = FVector(1.5f);
            break;
        case EAURACRONJungleCampType::RadiantEssence:
        case EAURACRONJungleCampType::ChaosEssence:
        case EAURACRONJungleCampType::FluxCrawler:
            CampScale = FVector(2.0f);
            break;
    }

    MeshComponent->SetWorldScale3D(CampScale);

    // Criar material dinâmico baseado no ambiente
    if (UMaterialInterface* BaseMaterial = MeshComponent->GetMaterial(0))
    {
        if (UMaterialInstanceDynamic* DynamicMaterial = MeshComponent->CreateAndSetMaterialInstanceDynamic(0))
        {
            // Aplicar cores baseadas no ambiente
            FLinearColor EnvironmentColor;
            switch (EnvironmentType)
            {
                case EAURACRONEnvironmentType::RadiantPlains:
                    EnvironmentColor = FLinearColor(1.0f, 0.9f, 0.7f, 1.0f); // Dourado radiante
                    break;
                case EAURACRONEnvironmentType::ZephyrFirmament:
                    EnvironmentColor = FLinearColor(0.7f, 0.9f, 1.0f, 1.0f); // Azul aéreo
                    break;
                case EAURACRONEnvironmentType::PurgatoryRealm:
                    EnvironmentColor = FLinearColor(0.9f, 0.3f, 0.3f, 1.0f); // Vermelho sombrio
                    break;
                default:
                    EnvironmentColor = FLinearColor::White;
                    break;
            }

            DynamicMaterial->SetVectorParameterValue(FName(TEXT("BaseColor")), EnvironmentColor);
            DynamicMaterial->SetScalarParameterValue(FName(TEXT("CampIntensity")), static_cast<float>(CampType) * 0.2f + 0.5f);
        }
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGLaneSystem::ApplyCampMeshAndMaterial - Applied mesh and material for camp type %d in environment %d"),
           (int32)CampType, (int32)EnvironmentType);
}

void AAURACRONPCGLaneSystem::ApplyStructureMeshAndMaterial(UStaticMeshComponent* MeshComponent, EAURACRONDefensiveStructure StructureType, EAURACRONEnvironmentType EnvironmentType)
{
    // Aplicar mesh e material à estrutura usando APIs modernas do UE 5.6
    if (!MeshComponent || !IsValid(MeshComponent))
    {
        return;
    }

    // Configurar propriedades físicas usando APIs modernas
    MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    MeshComponent->SetCollisionObjectType(ECC_WorldStatic);
    MeshComponent->SetCollisionResponseToAllChannels(ECR_Block);
    MeshComponent->SetCanEverAffectNavigation(true);

    // Aplicar escala baseada no tipo de estrutura
    FVector StructureScale = FVector(1.0f);
    switch (StructureType)
    {
        case EAURACRONDefensiveStructure::OuterTower:
            StructureScale = FVector(1.0f, 1.0f, 1.5f); // Torres externas mais altas
            break;
        case EAURACRONDefensiveStructure::InnerTower:
            StructureScale = FVector(1.2f, 1.2f, 2.0f); // Torres internas maiores
            break;
        case EAURACRONDefensiveStructure::InhibitorTower:
            StructureScale = FVector(1.5f, 1.5f, 2.5f); // Torres inibidoras muito grandes
            break;
        case EAURACRONDefensiveStructure::Inhibitor:
            StructureScale = FVector(2.0f, 2.0f, 1.0f); // Inibidores largos e baixos
            break;
        case EAURACRONDefensiveStructure::Nexus:
            StructureScale = FVector(3.0f, 3.0f, 3.0f); // Nexus massivo
            break;
    }

    MeshComponent->SetWorldScale3D(StructureScale);

    // Criar material dinâmico baseado no ambiente e estrutura
    if (UMaterialInterface* BaseMaterial = MeshComponent->GetMaterial(0))
    {
        if (UMaterialInstanceDynamic* DynamicMaterial = MeshComponent->CreateAndSetMaterialInstanceDynamic(0))
        {
            // Aplicar cores baseadas no ambiente
            FLinearColor StructureColor;
            float StructureIntensity = 1.0f;

            switch (EnvironmentType)
            {
                case EAURACRONEnvironmentType::RadiantPlains:
                    StructureColor = FLinearColor(1.0f, 0.8f, 0.4f, 1.0f); // Dourado brilhante
                    StructureIntensity = 1.2f;
                    break;
                case EAURACRONEnvironmentType::ZephyrFirmament:
                    StructureColor = FLinearColor(0.6f, 0.8f, 1.0f, 1.0f); // Azul cristalino
                    StructureIntensity = 1.0f;
                    break;
                case EAURACRONEnvironmentType::PurgatoryRealm:
                    StructureColor = FLinearColor(0.8f, 0.2f, 0.2f, 1.0f); // Vermelho sombrio
                    StructureIntensity = 0.8f;
                    break;
                default:
                    StructureColor = FLinearColor(0.7f, 0.7f, 0.7f, 1.0f); // Cinza neutro
                    StructureIntensity = 0.9f;
                    break;
            }

            // Aplicar intensidade baseada no tipo de estrutura
            float TypeMultiplier = 1.0f;
            switch (StructureType)
            {
                case EAURACRONDefensiveStructure::OuterTower:
                    TypeMultiplier = 0.8f;
                    break;
                case EAURACRONDefensiveStructure::InnerTower:
                    TypeMultiplier = 1.0f;
                    break;
                case EAURACRONDefensiveStructure::InhibitorTower:
                    TypeMultiplier = 1.3f;
                    break;
                case EAURACRONDefensiveStructure::Inhibitor:
                    TypeMultiplier = 1.5f;
                    break;
                case EAURACRONDefensiveStructure::Nexus:
                    TypeMultiplier = 2.0f;
                    break;
            }

            DynamicMaterial->SetVectorParameterValue(FName(TEXT("BaseColor")), StructureColor);
            DynamicMaterial->SetScalarParameterValue(FName(TEXT("StructureIntensity")), StructureIntensity * TypeMultiplier);
            DynamicMaterial->SetScalarParameterValue(FName(TEXT("DefensivePower")), static_cast<float>(StructureType) * 0.25f + 0.5f);
        }
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGLaneSystem::ApplyStructureMeshAndMaterial - Applied mesh and material for structure type %d in environment %d"),
           (int32)StructureType, (int32)EnvironmentType);
}

void AAURACRONPCGLaneSystem::ApplyEnvironmentEffectsToStructure(AActor* StructureActor, EAURACRONEnvironmentType EnvironmentType)
{
    // Aplicar efeitos ambientais à estrutura usando APIs modernas do UE 5.6
    if (!StructureActor || !IsValid(StructureActor))
    {
        return;
    }

    // Aplicar efeitos baseados no ambiente
    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
        {
            // Efeito de brilho radiante
            if (UStaticMeshComponent* MeshComp = StructureActor->FindComponentByClass<UStaticMeshComponent>())
            {
                if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
                {
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("RadiantGlow")), 1.5f);
                    DynamicMaterial->SetVectorParameterValue(FName(TEXT("RadiantColor")), FLinearColor(1.0f, 0.9f, 0.7f));
                }
            }
            break;
        }

        case EAURACRONEnvironmentType::ZephyrFirmament:
        {
            // Efeito de flutuação aérea
            if (UStaticMeshComponent* MeshComp = StructureActor->FindComponentByClass<UStaticMeshComponent>())
            {
                if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
                {
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("AerialFloat")), 1.2f);
                    DynamicMaterial->SetVectorParameterValue(FName(TEXT("ZephyrWind")), FLinearColor(0.7f, 0.9f, 1.0f));
                }
            }
            break;
        }

        case EAURACRONEnvironmentType::PurgatoryRealm:
        {
            // Efeito de distorção sombria
            if (UStaticMeshComponent* MeshComp = StructureActor->FindComponentByClass<UStaticMeshComponent>())
            {
                if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
                {
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("ShadowDistortion")), 1.8f);
                    DynamicMaterial->SetVectorParameterValue(FName(TEXT("PurgatoryAura")), FLinearColor(0.9f, 0.3f, 0.3f));
                }
            }
            break;
        }

        default:
            // Efeitos padrão mínimos
            if (UStaticMeshComponent* MeshComp = StructureActor->FindComponentByClass<UStaticMeshComponent>())
            {
                if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
                {
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("DefaultEffect")), 1.0f);
                }
            }
            break;
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGLaneSystem::ApplyEnvironmentEffectsToStructure - Applied environment effects for environment %d to structure %s"),
           (int32)EnvironmentType, *StructureActor->GetName());
}

FVector AAURACRONPCGLaneSystem::GetEnvironmentTowerOffset(EAURACRONEnvironmentType EnvironmentType)
{
    // Obter offset de torre baseado no ambiente usando algoritmos modernos
    FVector Offset = FVector::ZeroVector;

    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            // Torres nas planícies radiantes ficam ligeiramente elevadas
            Offset = FVector(0.0f, 0.0f, 50.0f);
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            // Torres no firmamento ficam flutuando mais alto
            Offset = FVector(0.0f, 0.0f, 150.0f);
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            // Torres no purgatório ficam parcialmente enterradas
            Offset = FVector(0.0f, 0.0f, -25.0f);
            break;

        default:
            // Offset padrão
            Offset = FVector(0.0f, 0.0f, 25.0f);
            break;
    }

    return Offset;
}

void AAURACRONPCGLaneSystem::ApplyPhaseEffectsToActor(AActor* Actor, float PhaseIntensity, const FLinearColor& PhaseColor)
{
    // Aplicar efeitos de fase ao ator usando APIs modernas do UE 5.6
    if (!Actor || !IsValid(Actor))
    {
        return;
    }

    // Aplicar efeitos a todos os componentes de mesh do ator
    TArray<UStaticMeshComponent*> MeshComponents;
    Actor->GetComponents<UStaticMeshComponent>(MeshComponents);

    for (UStaticMeshComponent* MeshComp : MeshComponents)
    {
        if (MeshComp && IsValid(MeshComp))
        {
            ApplyPhaseEffectsToComponent(MeshComp, PhaseIntensity, PhaseColor);
        }
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGLaneSystem::ApplyPhaseEffectsToActor - Applied phase effects to actor %s with intensity %.2f"),
           *Actor->GetName(), PhaseIntensity);
}

void AAURACRONPCGLaneSystem::ApplyPhaseEffectsToComponent(UActorComponent* Component, float PhaseIntensity, const FLinearColor& PhaseColor)
{
    // Aplicar efeitos de fase ao componente usando APIs modernas do UE 5.6
    if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
    {
        if (MeshComp && IsValid(MeshComp))
        {
            // Criar material dinâmico se necessário
            if (UMaterialInterface* BaseMaterial = MeshComp->GetMaterial(0))
            {
                if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
                {
                    // Aplicar parâmetros de fase
                    DynamicMaterial->SetVectorParameterValue(FName(TEXT("PhaseColor")), PhaseColor);
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("PhaseIntensity")), PhaseIntensity);
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("PhaseBlend")), PhaseIntensity * 0.8f);
                }
            }

            // Aplicar visibilidade baseada na intensidade da fase
            bool bShouldBeVisible = PhaseIntensity > 0.1f;
            MeshComp->SetVisibility(bShouldBeVisible);
        }
    }
}

FAURACRONEnvironmentConfig AAURACRONPCGLaneSystem::GetEnvironmentConfiguration(EAURACRONEnvironmentType EnvironmentType)
{
    // Obter configuração do ambiente usando dados modernos do UE 5.6
    FAURACRONEnvironmentConfig Config;

    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            Config.PrimaryColor = FLinearColor(1.0f, 0.9f, 0.7f, 1.0f); // Dourado radiante
            Config.LightIntensity = 1.3f;
            Config.MaterialRoughness = 0.3f;
            Config.ActivityScale = 1.2f;
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            Config.PrimaryColor = FLinearColor(0.7f, 0.9f, 1.0f, 1.0f); // Azul aéreo
            Config.LightIntensity = 1.1f;
            Config.MaterialRoughness = 0.2f;
            Config.ActivityScale = 1.0f;
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            Config.PrimaryColor = FLinearColor(0.9f, 0.3f, 0.3f, 1.0f); // Vermelho sombrio
            Config.LightIntensity = 0.8f;
            Config.MaterialRoughness = 0.7f;
            Config.ActivityScale = 1.5f;
            break;

        default:
            Config.PrimaryColor = FLinearColor::White;
            Config.LightIntensity = 1.0f;
            Config.MaterialRoughness = 0.5f;
            Config.ActivityScale = 1.0f;
            break;
    }

    return Config;
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGLaneSystem::ClearGeneratedElementsForEnvironment(EAURACRONEnvironmentType Environment)
{
    // Limpar elementos gerados para ambiente específico usando APIs modernas do UE 5.6
    int32 ClearedCount = 0;

    // Limpar componentes gerados para o ambiente
    if (FAURACRONComponentArray* ComponentArray = GeneratedComponentsByEnvironment.Find(Environment))
    {
        for (UObject* Component : ComponentArray->Components)
        {
            if (AActor* Actor = Cast<AActor>(Component))
            {
                if (IsValid(Actor))
                {
                    Actor->Destroy();
                    ClearedCount++;
                }
            }
            else if (UActorComponent* ActorComponent = Cast<UActorComponent>(Component))
            {
                if (IsValid(ActorComponent))
                {
                    ActorComponent->DestroyComponent();
                    ClearedCount++;
                }
            }
        }
        ComponentArray->Components.Empty();
    }

    // Limpar estruturas defensivas para o ambiente
    if (FAURACRONActorArray* ActorArray = DefensiveStructuresByEnvironment.Find(Environment))
    {
        for (AActor* Actor : ActorArray->Actors)
        {
            if (IsValid(Actor))
            {
                Actor->Destroy();
                ClearedCount++;
            }
        }
        ActorArray->Actors.Empty();
    }

    UE_LOG(LogAURACRONPCGLaneSystem, Log, TEXT("ClearGeneratedElementsForEnvironment - Cleared %d elements for environment %d using modern UE 5.6 APIs"),
           ClearedCount, (int32)Environment);
}

bool AAURACRONPCGLaneSystem::IsValidJungleCampPosition(const FVector& Position, float MinDistanceFromLanes)
{
    // Validar posição para camp da jungle usando algoritmos modernos do UE 5.6
    if (!IsValid(GetWorld()))
    {
        return false;
    }

    // Verificar distância mínima de todas as lanes no ambiente atual
    for (const auto& LanePair : LaneInfos)
    {
        EAURACRONLaneType LaneType = LanePair.Key;
        const FAURACRONLaneInfo& LaneInfo = LanePair.Value;

        // Obter pontos da lane para o ambiente atual
        const FAURACRONVectorArray* LanePointsArray = LaneInfo.LanePointsByEnvironment.Find(CurrentEnvironment);
        if (LanePointsArray)
        {
            // Verificar distância mínima para todos os pontos da lane
            for (const FVector& LanePoint : LanePointsArray->Vectors)
            {
                float Distance = FVector::Dist2D(Position, LanePoint);
                if (Distance < MinDistanceFromLanes)
                {
                    UE_LOG(LogAURACRONPCGLaneSystem, VeryVerbose, TEXT("IsValidJungleCampPosition - Position too close to lane %d (distance: %.1f, min: %.1f)"),
                           (int32)LaneType, Distance, MinDistanceFromLanes);
                    return false;
                }
            }
        }
    }

    // Verificar colisão com terreno usando trace moderno do UE 5.6
    FHitResult HitResult;
    FVector TraceStart = Position + FVector(0.0f, 0.0f, 1000.0f);
    FVector TraceEnd = Position - FVector(0.0f, 0.0f, 1000.0f);

    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.bReturnPhysicalMaterial = false;

    bool bHit = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        TraceStart,
        TraceEnd,
        ECC_WorldStatic,
        QueryParams
    );

    if (!bHit)
    {
        UE_LOG(LogAURACRONPCGLaneSystem, VeryVerbose, TEXT("IsValidJungleCampPosition - No ground found at position"));
        return false;
    }

    // Verificar se a inclinação do terreno é aceitável
    float SlopeAngle = FMath::RadiansToDegrees(FMath::Acos(FVector::DotProduct(HitResult.Normal, FVector::UpVector)));
    if (SlopeAngle > 30.0f) // Máximo 30 graus de inclinação
    {
        UE_LOG(LogAURACRONPCGLaneSystem, VeryVerbose, TEXT("IsValidJungleCampPosition - Terrain too steep (angle: %.1f degrees)"), SlopeAngle);
        return false;
    }

    UE_LOG(LogAURACRONPCGLaneSystem, VeryVerbose, TEXT("IsValidJungleCampPosition - Position is valid for jungle camp"));
    return true;
}

FAURACRONJungleCamp AAURACRONPCGLaneSystem::GetJungleCampConfig(const FString& CampType, EAURACRONEnvironmentType Environment)
{
    // Obter configuração de camp baseada no tipo e ambiente usando dados modernos do UE 5.6
    FAURACRONJungleCamp CampConfig;

    // Configurar propriedades básicas
    CampConfig.MonsterType = CampType;
    CampConfig.bIsBuffCamp = false;
    CampConfig.DifficultyLevel = 1;
    CampConfig.RespawnTime = 120.0f; // 2 minutos padrão

    // Configurar baseado no tipo de camp
    if (CampType.Contains(TEXT("Guardian")))
    {
        CampConfig.bIsBuffCamp = true;
        CampConfig.DifficultyLevel = 3;
        CampConfig.RespawnTime = 300.0f; // 5 minutos para buffs
        CampConfig.Radius = 800.0f;
    }
    else if (CampType.Contains(TEXT("Golem")) || CampType.Contains(TEXT("Elemental")))
    {
        CampConfig.DifficultyLevel = 2;
        CampConfig.RespawnTime = 180.0f; // 3 minutos
        CampConfig.Radius = 600.0f;
    }
    else if (CampType.Contains(TEXT("Spirit")) || CampType.Contains(TEXT("Wisp")))
    {
        CampConfig.DifficultyLevel = 1;
        CampConfig.RespawnTime = 90.0f; // 1.5 minutos
        CampConfig.Radius = 400.0f;
    }
    else
    {
        // Configuração padrão
        CampConfig.DifficultyLevel = 1;
        CampConfig.RespawnTime = 120.0f;
        CampConfig.Radius = 500.0f;
    }

    // Aplicar modificações baseadas no ambiente
    switch (Environment)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            CampConfig.RespawnTime *= 0.9f; // 10% mais rápido
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            CampConfig.RespawnTime *= 1.0f; // Tempo normal
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            CampConfig.RespawnTime *= 1.2f; // 20% mais lento
            CampConfig.DifficultyLevel = FMath::Min(5, CampConfig.DifficultyLevel + 1); // Mais difícil
            break;

        default:
            break;
    }

    UE_LOG(LogAURACRONPCGLaneSystem, VeryVerbose, TEXT("GetJungleCampConfig - Generated config for camp type %s in environment %d"),
           *CampType, (int32)Environment);

    return CampConfig;
}

void AAURACRONPCGLaneSystem::UpdateVisibilityForPhase(EAURACRONMapPhase Phase)
{
    // Atualizar visibilidade baseada na fase usando APIs modernas do UE 5.6
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Obter configurações de visibilidade baseadas na fase
    float VisibilityMultiplier = 1.0f;
    float FogDensity = 0.0f;
    FLinearColor PhaseColor = FLinearColor::White;

    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            VisibilityMultiplier = 1.2f; // Maior visibilidade no início
            FogDensity = 0.1f;
            PhaseColor = FLinearColor(1.0f, 0.9f, 0.7f, 1.0f); // Dourado suave
            break;

        case EAURACRONMapPhase::Convergence:
            VisibilityMultiplier = 1.0f; // Visibilidade normal
            FogDensity = 0.2f;
            PhaseColor = FLinearColor(1.0f, 1.0f, 1.0f, 1.0f); // Branco puro
            break;

        case EAURACRONMapPhase::Intensification:
            VisibilityMultiplier = 0.8f; // Visibilidade reduzida
            FogDensity = 0.4f;
            PhaseColor = FLinearColor(1.0f, 0.6f, 0.4f, 1.0f); // Laranja intenso
            break;

        case EAURACRONMapPhase::Resolution:
            VisibilityMultiplier = 0.6f; // Visibilidade muito reduzida
            FogDensity = 0.6f;
            PhaseColor = FLinearColor(0.4f, 0.6f, 1.0f, 1.0f); // Azul noturno
            break;

        default:
            VisibilityMultiplier = 1.0f;
            FogDensity = 0.2f;
            PhaseColor = FLinearColor::White;
            break;
    }

    // Aplicar efeitos de visibilidade a todos os componentes gerados
    for (auto& EnvironmentPair : GeneratedComponentsByEnvironment)
    {
        EAURACRONEnvironmentType Environment = EnvironmentPair.Key;
        FAURACRONComponentArray& ComponentArray = EnvironmentPair.Value;

        // Só aplicar ao ambiente atual
        bool bIsCurrentEnvironment = (Environment == CurrentEnvironment);
        float EnvironmentVisibility = bIsCurrentEnvironment ? VisibilityMultiplier : 0.0f;

        for (UObject* Component : ComponentArray.Components)
        {
            if (UPrimitiveComponent* PrimComp = Cast<UPrimitiveComponent>(Component))
            {
                if (IsValid(PrimComp))
                {
                    // Aplicar visibilidade baseada na fase
                    PrimComp->SetVisibility(EnvironmentVisibility > 0.1f);

                    // Aplicar efeitos de material se for um componente de mesh
                    if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(PrimComp))
                    {
                        ApplyPhaseEffectsToComponent(MeshComp, EnvironmentVisibility, PhaseColor);
                    }
                }
            }
        }
    }

    // Aplicar efeitos às estruturas defensivas
    for (auto& EnvironmentPair : DefensiveStructuresByEnvironment)
    {
        EAURACRONEnvironmentType Environment = EnvironmentPair.Key;
        FAURACRONActorArray& ActorArray = EnvironmentPair.Value;

        bool bIsCurrentEnvironment = (Environment == CurrentEnvironment);
        float EnvironmentVisibility = bIsCurrentEnvironment ? VisibilityMultiplier : 0.0f;

        for (AActor* Actor : ActorArray.Actors)
        {
            if (IsValid(Actor))
            {
                // Aplicar visibilidade ao ator
                Actor->SetActorHiddenInGame(EnvironmentVisibility <= 0.1f);

                // Aplicar efeitos de fase ao ator
                ApplyPhaseEffectsToActor(Actor, EnvironmentVisibility, PhaseColor);
            }
        }
    }

    // Atualizar fase atual
    CurrentMapPhase = Phase;

    UE_LOG(LogAURACRONPCGLaneSystem, Log, TEXT("UpdateVisibilityForPhase - Updated visibility for phase %d with multiplier %.2f and fog density %.2f"),
           (int32)Phase, VisibilityMultiplier, FogDensity);
}

void AAURACRONPCGLaneSystem::ApplyPhaseEffectsToComponent(UStaticMeshComponent* MeshComponent, float VisibilityMultiplier, const FLinearColor& PhaseColor)
{
    // Aplicar efeitos de fase ao componente usando APIs modernas do UE 5.6
    if (!IsValid(MeshComponent))
    {
        return;
    }

    // Criar material dinâmico se necessário
    UMaterialInstanceDynamic* DynamicMaterial = MeshComponent->CreateAndSetMaterialInstanceDynamic(0);
    if (IsValid(DynamicMaterial))
    {
        // Aplicar cor da fase
        DynamicMaterial->SetVectorParameterValue(TEXT("PhaseColor"), PhaseColor);

        // Aplicar multiplicador de visibilidade como opacidade
        float Opacity = FMath::Clamp(VisibilityMultiplier, 0.0f, 1.0f);
        DynamicMaterial->SetScalarParameterValue(TEXT("Opacity"), Opacity);

        // Aplicar efeito de brilho baseado na fase
        float EmissiveStrength = FMath::Lerp(0.0f, 2.0f, 1.0f - VisibilityMultiplier);
        DynamicMaterial->SetScalarParameterValue(TEXT("EmissiveStrength"), EmissiveStrength);

        UE_LOG(LogAURACRONPCGLaneSystem, VeryVerbose, TEXT("ApplyPhaseEffectsToComponent - Applied phase effects with opacity %.2f and emissive %.2f"),
               Opacity, EmissiveStrength);
    }
}



void AAURACRONPCGLaneSystem::ApplyDefensiveStructurePhaseEffects(AActor* StructureActor, float VisibilityMultiplier, const FLinearColor& PhaseColor)
{
    // Aplicar efeitos especiais de fase para estruturas defensivas usando APIs modernas do UE 5.6
    if (!IsValid(StructureActor))
    {
        return;
    }

    // Obter componentes de partículas se existirem
    TArray<UParticleSystemComponent*> ParticleComponents;
    StructureActor->GetComponents<UParticleSystemComponent>(ParticleComponents);

    for (UParticleSystemComponent* ParticleComp : ParticleComponents)
    {
        if (IsValid(ParticleComp))
        {
            // Ajustar intensidade das partículas baseada na visibilidade
            float ParticleIntensity = FMath::Lerp(0.2f, 1.0f, VisibilityMultiplier);
            ParticleComp->SetFloatParameter(TEXT("Intensity"), ParticleIntensity);

            // Aplicar cor da fase às partículas
            ParticleComp->SetColorParameter(TEXT("Color"), PhaseColor);
        }
    }

    // Obter componentes de luz se existirem
    TArray<ULightComponent*> LightComponents;
    StructureActor->GetComponents<ULightComponent>(LightComponents);

    for (ULightComponent* LightComp : LightComponents)
    {
        if (IsValid(LightComp))
        {
            // Ajustar intensidade da luz baseada na visibilidade
            float LightIntensity = FMath::Lerp(0.1f, 1.0f, VisibilityMultiplier);
            LightComp->SetIntensity(LightIntensity * 1000.0f); // Multiplicar por 1000 para unidades do UE

            // Aplicar cor da fase à luz
            LightComp->SetLightColor(PhaseColor);
        }
    }

    UE_LOG(LogAURACRONPCGLaneSystem, VeryVerbose, TEXT("ApplyDefensiveStructurePhaseEffects - Applied special effects to structure %s"),
           *StructureActor->GetName());
}
