// AURACRONPCGWorldPartitionIntegration.cpp
// Implementação da Integração com World Partition para AURACRON - UE 5.6
// Sistema de streaming e gerenciamento de elementos PCG usando APIs modernas
// Suporte para otimizações de hardware e streaming adaptativo

#include "PCG/AURACRONPCGWorldPartitionIntegration.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGEnvironment.h"
#include "PCG/AURACRONPCGIsland.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetSystemLibrary.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/WorldPartitionStreamingPolicy.h"
#include "Engine/Engine.h"
#include "Engine/GameViewportClient.h"
#include "Engine/GameInstance.h"
#include "HAL/PlatformMemory.h"
#include "HAL/PlatformApplicationMisc.h"
#include "GenericPlatform/GenericPlatformMemory.h"
#include "Curves/CurveFloat.h"
#include "TimerManager.h"

AAURACRONPCGWorldPartitionIntegration::AAURACRONPCGWorldPartitionIntegration()
    : StreamingRadius(5000.0f)
    , UpdateInterval(1.0f)
    , bAutoUpdateStreaming(true)
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.TickInterval = 0.5f;
    
    // Configurar replicação para multiplayer
    bReplicates = true;
    SetReplicateMovement(false);
    
    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));
    
    // Configurações de otimização de hardware
    HardwareConfig.QualityProfile = EAURACRONStreamingQualityProfile::Medium;
    HardwareConfig.DistanceMultiplier = 1.0f;
    HardwareConfig.bIsLowEndHardware = false;
    HardwareConfig.MemoryBudgetMB = 1024;
    HardwareConfig.MaxStreamingElements = 100;
    HardwareConfig.LODDistanceFactors = {1.0f, 0.75f, 0.5f, 0.25f};
    
    // Estatísticas de desempenho
    PerformanceStats.AverageFrameTime = 0.0f;
    PerformanceStats.ElementsCurrentlyStreamed = 0;
    PerformanceStats.PeakMemoryUsageMB = 0;
    PerformanceStats.LastUpdateTime = 0.0f;
    
    // Configurações de otimização dinâmica
    StatsUpdateInterval = 5.0f;
    bEnableDynamicOptimizations = true;
    LowFPSThreshold = 30.0f;
    MinTimeBetweenOptimizationAdjustments = 10.0f;
    bUsePlatformSpecificOptimizations = true;
    
    // Inicialização de variáveis de rastreamento
    TimeSinceLastOptimizationAdjustment = 0.0f;
    StatsUpdateAccumulator = 0.0f;
    TotalElementsLoaded = 0;
    TotalElementsUnloaded = 0;
}

void AAURACRONPCGWorldPartitionIntegration::BeginPlay()
{
    Super::BeginPlay();
    
    // Inicializar integração apenas no servidor
    if (HasAuthority())
    {
        InitializeWorldPartitionIntegration();
        
        // Detectar capacidades de hardware e otimizar streaming
        DetectHardwareCapabilities();
        OptimizeStreamingForCurrentPlatform();
        
        // Configurar timer de atualização se auto-update estiver ativo
        if (bAutoUpdateStreaming && UpdateInterval > 0.0f)
        {
            FTimerHandle UpdateTimer;
            GetWorld()->GetTimerManager().SetTimer(UpdateTimer, this, 
                &AAURACRONPCGWorldPartitionIntegration::UpdateStreamingForAllPlayers, 
                UpdateInterval, true);
        }
        
        // Configurar timer para atualização de estatísticas de desempenho
        FTimerHandle StatsTimer;
        GetWorld()->GetTimerManager().SetTimer(StatsTimer, FTimerDelegate::CreateUObject(this, 
            &AAURACRONPCGWorldPartitionIntegration::UpdatePerformanceStats, StatsUpdateInterval),
            StatsUpdateInterval, true);
            
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::BeginPlay - Sistema de streaming PCG inicializado com otimizações de hardware"));
    }
}

void AAURACRONPCGWorldPartitionIntegration::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Atualizar streaming baseado na localização dos jogadores
    if (HasAuthority() && bAutoUpdateStreaming)
    {
        UpdateStreamingForAllPlayers();
    }
    
    // Atualizar estatísticas de desempenho
    StatsUpdateAccumulator += DeltaTime;
    if (StatsUpdateAccumulator >= StatsUpdateInterval)
    {
        UpdatePerformanceStats(DeltaTime);
        StatsUpdateAccumulator = 0.0f;
    }
    
    // Gerenciar otimizações dinâmicas baseadas em desempenho
    if (bEnableDynamicOptimizations)
    {
        TimeSinceLastOptimizationAdjustment += DeltaTime;
        
        // Verificar se é hora de ajustar as otimizações
        if (TimeSinceLastOptimizationAdjustment >= MinTimeBetweenOptimizationAdjustments)
        {
            // Ajustar otimizações se o FPS estiver abaixo do limite
            if (PerformanceStats.AverageFrameTime > 0.0f && (1.0f / PerformanceStats.AverageFrameTime) < LowFPSThreshold)
            {
                AdjustOptimizationsForPerformance();
                TimeSinceLastOptimizationAdjustment = 0.0f;
            }
        }
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration()
{
    // Inicializar integração com World Partition usando APIs modernas do UE 5.6
    if (!HasAuthority())
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration - Only server can initialize"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration - Initializing World Partition integration"));

    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration - World is null"));
        return;
    }

    // Obter subsistema de World Partition
    if (UWorldPartitionSubsystem* WorldPartitionSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>())
    {
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration - World Partition subsystem found"));
        
        // Configurar streaming baseado nas dimensões do mapa AURACRON
        FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
        float MapRadius = FAURACRONMapDimensions::MAP_RADIUS_CM;
        
        // Registrar regiões de streaming para diferentes áreas do mapa
        RegisterStreamingRegion(TEXT("CenterRegion"), MapCenter, MapRadius * 0.3f);
        RegisterStreamingRegion(TEXT("InnerRegion"), MapCenter, MapRadius * 0.6f);
        RegisterStreamingRegion(TEXT("OuterRegion"), MapCenter, MapRadius);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration - World Partition subsystem not found"));
    }

    // Obter subsistema de Data Layer
    if (UDataLayerSubsystem* DataLayerSubsystem = World->GetSubsystem<UDataLayerSubsystem>())
    {
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration - Data Layer subsystem found"));
        
        // Configurar data layers para diferentes tipos de conteúdo PCG
        ConfigureDataLayers(DataLayerSubsystem);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration - Integration initialized successfully"));
}

void AAURACRONPCGWorldPartitionIntegration::RegisterPCGElementForStreaming(AActor* PCGElement, const FAURACRONPCGStreamingConfig& StreamingConfig)
{
    // Registrar elemento PCG para streaming usando APIs modernas do UE 5.6
    if (!PCGElement || !IsValid(PCGElement))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGWorldPartitionIntegration::RegisterPCGElementForStreaming - Invalid PCG element"));
        return;
    }

    if (!HasAuthority())
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGWorldPartitionIntegration::RegisterPCGElementForStreaming - Only server can register elements"));
        return;
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGWorldPartitionIntegration::RegisterPCGElementForStreaming - Registering element %s"), *PCGElement->GetName());

    // Criar entrada de streaming para o elemento
    FAURACRONPCGStreamingEntry StreamingEntry;
    StreamingEntry.PCGElement = PCGElement;
    StreamingEntry.StreamingConfig = StreamingConfig;
    StreamingEntry.bIsCurrentlyStreamed = false;
    StreamingEntry.LastUpdateTime = GetWorld()->GetTimeSeconds();

    // Adicionar ao mapa de elementos registrados
    FGuid ElementId = FGuid::NewGuid();
    RegisteredElements.Add(ElementId, StreamingEntry);

    // Configurar elemento baseado no tipo
    if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(PCGElement))
    {
        // Ambientes têm configuração especial de streaming
        Environment->SetActivityScale(StreamingConfig.bStartActive ? 1.0f : 0.0f);
    }
    else if (AAURACRONPCGIsland* Island = Cast<AAURACRONPCGIsland>(PCGElement))
    {
        // Ilhas são sempre importantes para streaming
        Island->SetActorTickEnabled(StreamingConfig.bStartActive);
    }
    else if (AAURACRONPCGPrismalFlow* PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(PCGElement))
    {
        // Prismal Flow tem streaming dinâmico
        PrismalFlow->SetActorHiddenInGame(!StreamingConfig.bStartActive);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::RegisterPCGElementForStreaming - Registered element %s with streaming distance %.1f"), 
           *PCGElement->GetName(), StreamingConfig.StreamingDistance);
}

void AAURACRONPCGWorldPartitionIntegration::UpdateStreamingForPlayerLocation(const FVector& PlayerLocation)
{
    // Atualizar streaming para localização do jogador usando APIs modernas do UE 5.6
    if (!HasAuthority())
    {
        return;
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGWorldPartitionIntegration::UpdateStreamingForPlayerLocation - Updating streaming for location %s"), *PlayerLocation.ToString());

    int32 StreamedInCount = 0;
    int32 StreamedOutCount = 0;

    // Atualizar streaming para todos os elementos registrados
    for (auto& ElementPair : RegisteredElements)
    {
        FAURACRONPCGStreamingEntry& StreamingEntry = ElementPair.Value;
        
        if (!StreamingEntry.PCGElement || !IsValid(StreamingEntry.PCGElement))
        {
            continue;
        }

        // Calcular distância do jogador ao elemento
        float Distance = FVector::Dist(PlayerLocation, StreamingEntry.PCGElement->GetActorLocation());
        // Verificar se o elemento deve ser carregado usando a função ShouldLoadElement
        bool bShouldBeStreamed = ShouldLoadElement(StreamingEntry, Distance);

        // Verificar se estado de streaming mudou
        if (bShouldBeStreamed && !StreamingEntry.bIsCurrentlyStreamed)
        {
            // Stream in
            StreamInElement(StreamingEntry);
            StreamedInCount++;
        }
        else if (!bShouldBeStreamed && StreamingEntry.bIsCurrentlyStreamed)
        {
            // Stream out
            StreamOutElement(StreamingEntry);
            StreamedOutCount++;
        }
    }

    if (StreamedInCount > 0 || StreamedOutCount > 0)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGWorldPartitionIntegration::UpdateStreamingForPlayerLocation - Streamed in: %d, Streamed out: %d"),
               StreamedInCount, StreamedOutCount);
    }
}

// ========================================
// FUNÇÕES INTERNAS - BÁSICAS
// ========================================

bool AAURACRONPCGWorldPartitionIntegration::ShouldLoadElement(const FAURACRONPCGStreamingEntry& StreamingEntry, float Distance)
{
    // Verificar se o elemento deve ser carregado baseado na distância e configurações de hardware
    if (!IsValid(StreamingEntry.PCGElement))
    {
        return false;
    }
    
    // Aplicar multiplicador de distância baseado nas configurações de hardware
    float DistanceMultiplier = GetHardwareStreamingDistanceMultiplier();
    float AdjustedLoadingDistance = StreamingEntry.StreamingConfig.LoadingDistance * DistanceMultiplier;
    float AdjustedUnloadingDistance = StreamingEntry.StreamingConfig.UnloadingDistance * DistanceMultiplier;
    
    // Verificar se já está em streaming e usar histerese para evitar oscilações
    if (StreamingEntry.bIsCurrentlyStreamed)
    {
        // Se já está em streaming, usar distância de descarregamento (maior)
        return Distance <= AdjustedUnloadingDistance;
    }
    else
    {
        // Se não está em streaming, usar distância de carregamento (menor)
        // Verificar também se não excedemos o número máximo de elementos em streaming
        if (PerformanceStats.ElementsCurrentlyStreamed >= HardwareConfig.MaxConcurrentStreamingElements)
        {
            // Se excedemos o limite, só carregar elementos muito próximos ou de alta prioridade
            return (Distance <= AdjustedLoadingDistance * 0.5f) || 
                   (StreamingEntry.StreamingConfig.StreamingPriority > 75);
        }
        
        return Distance <= AdjustedLoadingDistance;
    }
}

void AAURACRONPCGWorldPartitionIntegration::RegisterStreamingRegion(const FString& RegionName, const FVector& Center, float Radius)
{
    // Registrar região de streaming usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::RegisterStreamingRegion - Registering region %s at %s with radius %.1f"),
           *RegionName, *Center.ToString(), Radius);

    // Criar configuração de região
    FAURACRONPCGStreamingRegion Region;
    Region.RegionName = RegionName;
    Region.Center = Center;
    Region.Radius = Radius;
    Region.bIsActive = true;

    // Adicionar à lista de regiões
    StreamingRegions.Add(Region);
}

void AAURACRONPCGWorldPartitionIntegration::ConfigureDataLayers(UDataLayerSubsystem* DataLayerSubsystem)
{
    // Configurar data layers para diferentes tipos de conteúdo PCG
    if (!DataLayerSubsystem)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::ConfigureDataLayers - Configuring data layers for PCG content"));

    // Configurar data layers baseados nos tipos de ambiente AURACRON
    TArray<FString> DataLayerNames = {
        TEXT("AURACRON_RadiantPlains"),
        TEXT("AURACRON_ZephyrFirmament"),
        TEXT("AURACRON_PurgatoryRealm"),
        TEXT("AURACRON_Islands"),
        TEXT("AURACRON_PrismalFlow"),
        TEXT("AURACRON_Objectives")
    };

    for (const FString& LayerName : DataLayerNames)
    {
        // Tentar ativar data layer se existir
        // Nota: Em UE 5.6, a API pode variar dependendo da versão específica
        UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGWorldPartitionIntegration::ConfigureDataLayers - Configured data layer %s"), *LayerName);
    }
}

void AAURACRONPCGWorldPartitionIntegration::StreamInElement(FAURACRONPCGStreamingEntry& StreamingEntry)
{
    // Stream in elemento usando APIs modernas do UE 5.6
    if (!StreamingEntry.PCGElement || !IsValid(StreamingEntry.PCGElement))
    {
        return;
    }

    StreamingEntry.bIsCurrentlyStreamed = true;
    StreamingEntry.LastUpdateTime = GetWorld()->GetTimeSeconds();

    // Ativar elemento baseado no tipo
    if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(StreamingEntry.PCGElement))
    {
        Environment->SetActivityScale(1.0f);
        Environment->SetActorHiddenInGame(false);
        
        // Aplicar configurações de LOD baseadas no perfil de qualidade
        ApplyLODSettingsToElement(StreamingEntry);
    }
    else if (AAURACRONPCGIsland* Island = Cast<AAURACRONPCGIsland>(StreamingEntry.PCGElement))
    {
        Island->SetActorTickEnabled(true);
        Island->SetActorHiddenInGame(false);
        
        // Aplicar configurações de LOD baseadas no perfil de qualidade
        ApplyLODSettingsToElement(StreamingEntry);
    }
    else if (AAURACRONPCGPrismalFlow* PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(StreamingEntry.PCGElement))
    {
        PrismalFlow->SetActorHiddenInGame(false);
        PrismalFlow->SetActorTickEnabled(true);
        
        // Aplicar configurações de LOD baseadas no perfil de qualidade
        ApplyLODSettingsToElement(StreamingEntry);
    }

    // Atualizar estatísticas
    PerformanceStats.ElementsCurrentlyStreamed++;
    TotalElementsLoaded++;

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGWorldPartitionIntegration::StreamInElement - Streamed in element %s"),
           *StreamingEntry.PCGElement->GetName());
}

void AAURACRONPCGWorldPartitionIntegration::StreamOutElement(FAURACRONPCGStreamingEntry& StreamingEntry)
{
    // Stream out elemento usando APIs modernas do UE 5.6
    if (!StreamingEntry.PCGElement || !IsValid(StreamingEntry.PCGElement))
    {
        return;
    }

    StreamingEntry.bIsCurrentlyStreamed = false;
    StreamingEntry.LastUpdateTime = GetWorld()->GetTimeSeconds();

    // Atualizar estatísticas
    PerformanceStats.ElementsCurrentlyStreamed--;
    TotalElementsUnloaded++;

    // Desativar elemento baseado no tipo
    if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(StreamingEntry.PCGElement))
    {
        Environment->SetActivityScale(0.0f);
        Environment->SetActorHiddenInGame(true);
    }
    else if (AAURACRONPCGIsland* Island = Cast<AAURACRONPCGIsland>(StreamingEntry.PCGElement))
    {
        Island->SetActorTickEnabled(false);
        Island->SetActorHiddenInGame(true);
    }
    else if (AAURACRONPCGPrismalFlow* PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(StreamingEntry.PCGElement))
    {
        PrismalFlow->SetActorHiddenInGame(true);
        PrismalFlow->SetActorTickEnabled(false);
    }

    // Atualizar estatísticas
    PerformanceStats.ElementsCurrentlyStreamed--;
    TotalElementsUnloaded++;

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGWorldPartitionIntegration::StreamOutElement - Streamed out element %s"),
           *StreamingEntry.PCGElement->GetName());
}

// ========================================
// FUNÇÕES INTERNAS - OTIMIZAÇÃO DE HARDWARE
// ========================================

float AAURACRONPCGWorldPartitionIntegration::GetHardwareStreamingDistanceMultiplier() const
{
    // Retornar o multiplicador de distância baseado no perfil de qualidade atual
    switch (CurrentQualityProfile)
    {
        case EAURACRONStreamingQualityProfile::Ultra:
            return 1.2f;
        case EAURACRONStreamingQualityProfile::High:
            return 1.0f;
        case EAURACRONStreamingQualityProfile::Medium:
            return 0.8f;
        case EAURACRONStreamingQualityProfile::Low:
            return 0.6f;
        case EAURACRONStreamingQualityProfile::Mobile:
            return 0.4f;
        default:
            return 1.0f;
    }
}

void AAURACRONPCGWorldPartitionIntegration::DetectHardwareCapabilitiesInternal()
{
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::DetectHardwareCapabilitiesInternal - Detectando capacidades de hardware"));
    
    // Obter informações de memória do sistema
    FPlatformMemoryStats MemoryStats = FPlatformMemory::GetStats();
    uint64 TotalPhysicalGB = MemoryStats.TotalPhysical / (1024 * 1024 * 1024);
    
    // Detectar GPU e CPU
    FString CPUBrand = FPlatformMisc::GetCPUBrand();
    FString GPUBrand = FPlatformMisc::GetPrimaryGPUBrand();
    
    UE_LOG(LogTemp, Log, TEXT("Hardware detectado - CPU: %s, GPU: %s, RAM: %llu GB"), 
           *CPUBrand, *GPUBrand, TotalPhysicalGB);
    
    // Configurar hardware de baixo desempenho baseado na memória disponível
    HardwareConfig.bIsLowEndHardware = (TotalPhysicalGB < 8);
    
    // Configurar orçamento de memória baseado na memória total
    if (TotalPhysicalGB >= 16)
    {
        HardwareConfig.MemoryBudgetMB = 2048;
        HardwareConfig.QualityProfile = EAURACRONStreamingQualityProfile::High;
    }
    else if (TotalPhysicalGB >= 8)
    {
        HardwareConfig.MemoryBudgetMB = 1024;
        HardwareConfig.QualityProfile = EAURACRONStreamingQualityProfile::Medium;
    }
    else
    {
        HardwareConfig.MemoryBudgetMB = 512;
        HardwareConfig.QualityProfile = EAURACRONStreamingQualityProfile::Low;
    }
    
    // Configurar número máximo de elementos baseado no orçamento de memória
    HardwareConfig.MaxStreamingElements = FMath::Clamp(HardwareConfig.MemoryBudgetMB / 10, 20, 500);
    
    // Configurar multiplicador de distância baseado no perfil de qualidade
    SetStreamingQualityProfile(HardwareConfig.QualityProfile);
    
    return true;
}

void AAURACRONPCGWorldPartitionIntegration::ApplyHardwareSpecificOptimizations()
{
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::ApplyHardwareSpecificOptimizations - Aplicando otimizações de hardware"));
    
    // Aplicar otimizações baseadas no perfil de qualidade
    switch (HardwareConfig.QualityProfile)
    {
        case EAURACRONStreamingQualityProfile::Low:
            // Reduzir distância de streaming e qualidade visual
            StreamingRadius = 3000.0f * HardwareConfig.DistanceMultiplier;
            break;
            
        case EAURACRONStreamingQualityProfile::Medium:
            // Configurações padrão
            StreamingRadius = 5000.0f * HardwareConfig.DistanceMultiplier;
            break;
            
        case EAURACRONStreamingQualityProfile::High:
            // Aumentar distância de streaming e qualidade visual
            StreamingRadius = 7000.0f * HardwareConfig.DistanceMultiplier;
            break;
            
        case EAURACRONStreamingQualityProfile::Ultra:
            // Máxima distância de streaming e qualidade visual
            StreamingRadius = 10000.0f * HardwareConfig.DistanceMultiplier;
            break;
            
        default:
            StreamingRadius = 5000.0f * HardwareConfig.DistanceMultiplier;
            break;
    }
    
    // Aplicar otimizações a todos os elementos
    ApplyOptimizationToAllElements();
}

void AAURACRONPCGWorldPartitionIntegration::ApplyPlatformSpecificOptimizations()
{
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::ApplyPlatformSpecificOptimizations - Aplicando otimizações específicas da plataforma"));
    
    // Detectar plataforma atual
    bool bIsConsole = FPlatformProperties::IsConsole();
    bool bIsMobile = FPlatformProperties::IsMobile();
    
    if (bIsMobile)
    {
        // Otimizações para dispositivos móveis
        HardwareConfig.QualityProfile = EAURACRONStreamingQualityProfile::Low;
        HardwareConfig.DistanceMultiplier = 0.5f;
        HardwareConfig.MaxStreamingElements = FMath::Min(HardwareConfig.MaxStreamingElements, 50);
    }
    else if (bIsConsole)
    {
        // Otimizações para consoles
        HardwareConfig.QualityProfile = EAURACRONStreamingQualityProfile::Medium;
        HardwareConfig.DistanceMultiplier = 0.75f;
    }
    else
    {
        // Otimizações para PC baseadas na detecção de hardware
        // Já configuradas em DetectHardwareCapabilitiesInternal
    }
}

void AAURACRONPCGWorldPartitionIntegration::RegisterElementLoadTime(const FAURACRONPCGStreamingEntry& Element, float LoadTimeMS)
{
    // Registrar o tempo de carregamento de um elemento para análise de desempenho
    if (!Element.PCGElement || !IsValid(Element.PCGElement))
    {
        return;
    }
    
    // Converter de milissegundos para segundos para consistência interna
    float LoadTimeSeconds = LoadTimeMS / 1000.0f;
    
    // Atualizar estatísticas de carregamento
    PerformanceStats.TotalElementLoadTime += LoadTimeSeconds;
    PerformanceStats.ElementLoadCount++;
    
    // Calcular média de tempo de carregamento
    if (PerformanceStats.ElementLoadCount > 0)
    {
        PerformanceStats.AverageElementLoadTime = PerformanceStats.TotalElementLoadTime / PerformanceStats.ElementLoadCount;
    }
    
    // Registrar tempo de carregamento no elemento se possível
    if (RegisteredElements.Contains(Element.ElementId))
    {
        RegisteredElements[Element.ElementId].LastLoadTime = LoadTimeSeconds;
    }
    
    // Detectar carregamentos lentos
    if (LoadTimeSeconds > 0.5f) // Mais de 500ms é considerado lento
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGWorldPartitionIntegration::RegisterElementLoadTime - Carregamento lento detectado: %f segundos para o elemento %s"), 
               LoadTimeSeconds, *Element.PCGElement->GetName());
        
        // Incrementar contador de carregamentos lentos
        PerformanceStats.SlowLoadCount++;
    }
}

void AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches(float DeltaTime)
{
    // Analisar padrões de streaming para detectar hitches e problemas de desempenho
    
    // Acumular tempo para análise periódica
    static float TimeSinceLastHitchAnalysis = 0.0f;
    TimeSinceLastHitchAnalysis += DeltaTime;
    
    // Realizar análise a cada 5 segundos para evitar sobrecarga
    const float HitchAnalysisInterval = 5.0f;
    if (TimeSinceLastHitchAnalysis < HitchAnalysisInterval)
    {
        return;
    }
    
    TimeSinceLastHitchAnalysis = 0.0f;
    
    // Verificar se temos dados suficientes para análise
    if (PerformanceStats.ElementLoadCount < 10)
    {
        return;
    }
    
    // Verificar se a média de tempo de carregamento está acima do limite aceitável
    const float MaxAcceptableLoadTime = 0.25f; // 250ms
    if (PerformanceStats.AverageElementLoadTime > MaxAcceptableLoadTime)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches - Tempo médio de carregamento alto: %f segundos"), 
               PerformanceStats.AverageElementLoadTime);
        
        // Sugerir ajustes de otimização
        if (HardwareConfig.QualityProfile > EAURACRONStreamingQualityProfile::Low)
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches - Considerando reduzir perfil de qualidade"));
            
            // Reduzir automaticamente o perfil de qualidade se configurado para ajuste automático
            if (bAutoAdjustQualityProfile)
            {
                EAURACRONStreamingQualityProfile NewProfile = static_cast<EAURACRONStreamingQualityProfile>(static_cast<int32>(HardwareConfig.QualityProfile) - 1);
                if (NewProfile >= EAURACRONStreamingQualityProfile::Low)
                {
                    SetStreamingQualityProfile(NewProfile);
                    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches - Perfil de qualidade reduzido automaticamente para %d"), 
                           static_cast<int32>(NewProfile));
                }
            }
        }
    }
    
    // Verificar se temos muitos carregamentos lentos
    const float SlowLoadThreshold = 0.3f; // 30% dos carregamentos são lentos
    float SlowLoadPercentage = PerformanceStats.ElementLoadCount > 0 ? 
        static_cast<float>(PerformanceStats.SlowLoadCount) / static_cast<float>(PerformanceStats.ElementLoadCount) : 0.0f;
    
    if (SlowLoadPercentage > SlowLoadThreshold)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches - Alta porcentagem de carregamentos lentos: %.1f%%"), 
               SlowLoadPercentage * 100.0f);
        
        // Reduzir distância de streaming se muitos carregamentos são lentos
        if (HardwareConfig.StreamingDistanceMultiplier > 0.5f)
        {
            HardwareConfig.StreamingDistanceMultiplier = FMath::Max(0.5f, HardwareConfig.StreamingDistanceMultiplier - 0.1f);
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches - Multiplicador de distância reduzido para %.2f"), 
                   HardwareConfig.StreamingDistanceMultiplier);
        }
    }
    
    // Verificar se houve hitches durante o streaming
    if (PerformanceStats.HitchesDetected > 0)
    {
        // Calcular FPS médio
        float CurrentFPS = PerformanceStats.AverageFrameTime > 0.0f ? 1.0f / PerformanceStats.AverageFrameTime : 60.0f;
        
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches - Detectados %d hitches. FPS atual: %.1f"), 
               PerformanceStats.HitchesDetected, CurrentFPS);
        
        // Se FPS estiver abaixo do aceitável e tivermos hitches, reduzir qualidade
        if (CurrentFPS < HardwareConfig.MinAcceptableFPS && HardwareConfig.QualityProfile > EAURACRONStreamingQualityProfile::Low)
        {
            // Reduzir qualidade mais agressivamente
            EAURACRONStreamingQualityProfile NewProfile = static_cast<EAURACRONStreamingQualityProfile>(static_cast<int32>(HardwareConfig.QualityProfile) - 1);
            if (NewProfile >= EAURACRONStreamingQualityProfile::Low)
            {
                SetStreamingQualityProfile(NewProfile);
                UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches - Reduzindo qualidade devido a hitches. Novo perfil: %d"), 
                       static_cast<int32>(NewProfile));
            }
        }
    }
    
    // Resetar contadores após análise periódica
    if (PerformanceStats.ElementLoadCount > 100)
    {
        PerformanceStats.TotalElementLoadTime = PerformanceStats.AverageElementLoadTime * 10; // Manter média com peso menor
        PerformanceStats.ElementLoadCount = 10;
        PerformanceStats.SlowLoadCount = FMath::RoundToInt(SlowLoadPercentage * 10); // Manter proporção
    }
}

void AAURACRONPCGWorldPartitionIntegration::UpdatePerformanceStats(float DeltaTime)
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGWorldPartitionIntegration::UpdatePerformanceStats - Atualizando estatísticas de desempenho"));
    
    // Atualizar tempo desde a última atualização
    float CurrentTime = GetWorld()->GetTimeSeconds();
    PerformanceStats.LastUpdateTime = CurrentTime;
    
    // Atualizar contagem de elementos em streaming
    int32 PreviousElementCount = PerformanceStats.ElementsCurrentlyStreamed;
    PerformanceStats.ElementsCurrentlyStreamed = 0;
    for (const auto& ElementPair : RegisteredElements)
    {
        if (ElementPair.Value.bIsCurrentlyStreamed)
        {
            PerformanceStats.ElementsCurrentlyStreamed++;
        }
    }
    
    // Calcular taxa de operações de streaming por segundo
    if (DeltaTime > 0.0f)
    {
        PerformanceStats.StreamingOperationsPerSecond = (PerformanceStats.ElementsCurrentlyStreamed - PreviousElementCount) / DeltaTime;
    }
    
    // Atualizar tempo médio de frame usando UWorld para maior precisão
    UWorld* World = GetWorld();
    if (World)
    {
        float CurrentFrameTime = World->GetDeltaSeconds();
        
        // Atualizar média de tempo de frame usando média móvel
        const float FrameTimeAlpha = 0.1f; // Peso para nova amostra
        PerformanceStats.AverageFrameTime = (PerformanceStats.AverageFrameTime * (1.0f - FrameTimeAlpha)) + (CurrentFrameTime * FrameTimeAlpha);
        
        // Calcular FPS médio
        PerformanceStats.AverageFPS = PerformanceStats.AverageFrameTime > 0.0f ? 1.0f / PerformanceStats.AverageFrameTime : 60.0f;
        
        // Detectar hitches (picos de tempo de frame)
        if (CurrentFrameTime > PerformanceStats.AverageFrameTime * 2.0f)
        {
            PerformanceStats.HitchesDetected++;
            
            // Analisar se o hitch pode estar relacionado ao streaming
            if (PerformanceStats.StreamingOperationsPerSecond > 5.0f)
            {
                UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGWorldPartitionIntegration::UpdatePerformanceStats - Hitch possivelmente relacionado ao streaming (%.1f ops/s)"), 
                       PerformanceStats.StreamingOperationsPerSecond);
                
                // Executar detecção de hitches de streaming para possíveis ajustes
                DetectStreamingHitches(DeltaTime);
            }
        }
    }
    
    // Atualizar uso de memória
    FPlatformMemoryStats MemoryStats = FPlatformMemory::GetStats();
    uint64 CurrentMemoryUsageMB = MemoryStats.UsedPhysical / (1024 * 1024);
    PerformanceStats.CurrentMemoryUsageMB = static_cast<float>(CurrentMemoryUsageMB);
    PerformanceStats.PeakMemoryUsageMB = FMath::Max(PerformanceStats.PeakMemoryUsageMB, static_cast<float>(CurrentMemoryUsageMB));
    
    // Verificar se é necessário otimizar o uso de memória
    if (PerformanceStats.CurrentMemoryUsageMB > HardwareConfig.MemoryBudgetMB * 0.9f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGWorldPartitionIntegration::UpdatePerformanceStats - Uso de memória alto: %.1f MB / %.1f MB"), 
               PerformanceStats.CurrentMemoryUsageMB, HardwareConfig.MemoryBudgetMB);
        
        // Otimizar uso de memória se estiver acima do orçamento
        OptimizeMemoryUsage();
    }
}

float AAURACRONPCGWorldPartitionIntegration::CalculateFPSBasedDistanceMultiplier() const
{
    // Se o FPS estiver abaixo do limite, reduzir o multiplicador de distância
    // para diminuir a carga de streaming e melhorar o desempenho
    
    const float TargetFPS = HardwareConfig.TargetFPS;
    const float MinFPS = HardwareConfig.MinAcceptableFPS;
    
    // Se não temos estatísticas de FPS válidas, retornar valor neutro
    if (PerformanceStats.AverageFPS <= 0.0f)
    {
        return 1.0f;
    }
    
    // Se o FPS está acima do alvo, podemos aumentar ligeiramente o multiplicador
    if (PerformanceStats.AverageFPS >= TargetFPS)
    {
        // Aumentar até 20% se o FPS estiver bem acima do alvo
        return FMath::Min(1.2f, 1.0f + ((PerformanceStats.AverageFPS - TargetFPS) / TargetFPS) * 0.2f);
    }
    
    // Se o FPS está abaixo do mínimo aceitável, reduzir drasticamente
    if (PerformanceStats.AverageFPS <= MinFPS)
    {
        // Reduzir até 50% em casos extremos
        return 0.5f;
    }
    
    // Caso contrário, interpolar linearmente entre 0.5 e 1.0 com base na relação entre FPS atual e alvo
    float FPSRatio = (PerformanceStats.AverageFPS - MinFPS) / (TargetFPS - MinFPS);
    return FMath::Lerp(0.5f, 1.0f, FPSRatio);
}

void AAURACRONPCGWorldPartitionIntegration::AdjustStreamingBasedOnPerformance()
{
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::AdjustStreamingBasedOnPerformance - Ajustando streaming baseado no desempenho atual"));
    
    // Verificar se é necessário ajustar otimizações
    if (!ShouldAdjustOptimizations())
    {
        return;
    }
    
    // Calcular FPS atual
    float CurrentFPS = PerformanceStats.AverageFrameTime > 0.0f ? 1.0f / PerformanceStats.AverageFrameTime : 60.0f;
    
    // Verificar se o FPS está abaixo do limite
    if (CurrentFPS < HardwareConfig.MinAcceptableFPS)
    {
        UE_LOG(LogTemp, Warning, TEXT("Baixo FPS detectado (%.1f). Reduzindo qualidade de streaming."), CurrentFPS);
        
        // Reduzir qualidade de streaming
        switch (HardwareConfig.QualityProfile)
        {
            case EAURACRONStreamingQualityProfile::Ultra:
                SetStreamingQualityProfile(EAURACRONStreamingQualityProfile::High);
                break;
                
            case EAURACRONStreamingQualityProfile::High:
                SetStreamingQualityProfile(EAURACRONStreamingQualityProfile::Medium);
                break;
                
            case EAURACRONStreamingQualityProfile::Medium:
                SetStreamingQualityProfile(EAURACRONStreamingQualityProfile::Low);
                break;
                
            case EAURACRONStreamingQualityProfile::Low:
                // Já está no nível mais baixo, reduzir ainda mais o multiplicador de distância
                HardwareConfig.DistanceMultiplier = FMath::Max(0.25f, HardwareConfig.DistanceMultiplier - 0.1f);
                break;
        }
        
        // Aplicar otimizações
        ApplyHardwareSpecificOptimizations();
        
        // Otimizar uso de memória em situações críticas
        OptimizeMemoryUsage();
    }
    else if (CurrentFPS > HardwareConfig.TargetFPS && HardwareConfig.QualityProfile != EAURACRONStreamingQualityProfile::Ultra)
    {
        // FPS está bom, podemos aumentar a qualidade se não estiver no máximo
        UE_LOG(LogTemp, Log, TEXT("Bom FPS detectado (%.1f). Aumentando qualidade de streaming."), CurrentFPS);
        
        // Aumentar qualidade de streaming gradualmente
        switch (HardwareConfig.QualityProfile)
        {
            case EAURACRONStreamingQualityProfile::Low:
                SetStreamingQualityProfile(EAURACRONStreamingQualityProfile::Medium);
                break;
                
            case EAURACRONStreamingQualityProfile::Medium:
                SetStreamingQualityProfile(EAURACRONStreamingQualityProfile::High);
                break;
                
            case EAURACRONStreamingQualityProfile::High:
                SetStreamingQualityProfile(EAURACRONStreamingQualityProfile::Ultra);
                break;
        }
        
        // Aplicar otimizações
        ApplyHardwareSpecificOptimizations();
    }
}

void AAURACRONPCGWorldPartitionIntegration::AdjustOptimizationsForPerformance()
{
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::AdjustOptimizationsForPerformance - Ajustando otimizações para melhorar desempenho"));
    
    // Ajustar streaming baseado no desempenho atual
    AdjustStreamingBasedOnPerformance();
    
    // Calcular nível de LOD ótimo baseado no hardware
    int32 OptimalLOD = CalculateOptimalLODLevel();
    
    // Aplicar configurações de LOD a todos os elementos
    for (auto& ElementPair : RegisteredElements)
    {
        FAURACRONPCGStreamingEntry& StreamingEntry = ElementPair.Value;
        if (StreamingEntry.bIsCurrentlyStreamed)
        {
            ApplyStreamingConfigToElement(StreamingEntry);
        }
    }
}

void AAURACRONPCGWorldPartitionIntegration::ApplyLODSettingsToElement(FAURACRONPCGStreamingEntry& StreamingEntry)
{
    if (!StreamingEntry.PCGElement || !IsValid(StreamingEntry.PCGElement))
    {
        return;
    }
    
    // Configurar LOD baseado no perfil de qualidade
    int32 TargetLOD = 0;
    switch (HardwareConfig.QualityProfile)
    {
        case EAURACRONStreamingQualityProfile::Low:
            TargetLOD = 3; // LOD mais baixo
            break;
            
        case EAURACRONStreamingQualityProfile::Medium:
            TargetLOD = 2;
            break;
            
        case EAURACRONStreamingQualityProfile::High:
            TargetLOD = 1;
            break;
            
        case EAURACRONStreamingQualityProfile::Ultra:
            TargetLOD = 0; // LOD mais alto
            break;
    }
    
    // Aplicar LOD aos componentes do ator
    TArray<UStaticMeshComponent*> MeshComponents;
    StreamingEntry.PCGElement->GetComponents<UStaticMeshComponent>(MeshComponents);
    
    for (UStaticMeshComponent* MeshComponent : MeshComponents)
    {
        if (MeshComponent)
        {
            // Forçar LOD específico se disponível
            if (MeshComponent->GetStaticMesh() && MeshComponent->GetStaticMesh()->GetNumLODs() > TargetLOD)
            {
                MeshComponent->ForcedLodModel = TargetLOD + 1; // ForcedLodModel é 1-based
            }
            
            // Ajustar distâncias de LOD baseado no perfil de qualidade
            if (HardwareConfig.LODDistanceFactors.Num() > 0)
            {
                float LODDistanceFactor = HardwareConfig.LODDistanceFactors[FMath::Min(TargetLOD, HardwareConfig.LODDistanceFactors.Num() - 1)];
                MeshComponent->SetLODDistanceScale(LODDistanceFactor);
            }
        }
    }
}

void AAURACRONPCGWorldPartitionIntegration::UpdateStreamingForAllPlayers()
{
    // Atualizar streaming para todos os jogadores
    if (UWorld* World = GetWorld())
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            if (APlayerController* PC = Iterator->Get())
            {
                if (APawn* Pawn = PC->GetPawn())
                {
                    UpdateStreamingForPlayerLocation(Pawn->GetActorLocation());
                }
            }
        }
    }
}

// ========================================
// FUNÇÕES PÚBLICAS - OTIMIZAÇÃO DE HARDWARE
// ========================================

bool AAURACRONPCGWorldPartitionIntegration::DetectHardwareCapabilities()
{
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::DetectHardwareCapabilities - Detectando capacidades de hardware"));
    
    return DetectHardwareCapabilitiesInternal();
}

void AAURACRONPCGWorldPartitionIntegration::ConfigureHardwareOptimizations(const FAURACRONHardwareStreamingConfig& NewConfig)
{
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::ConfigureHardwareOptimizations - Configurando otimizações de hardware"));
    
    // Atualizar configuração de hardware
    HardwareConfig = NewConfig;
    
    // Aplicar otimizações
    ApplyHardwareSpecificOptimizations();
}

float AAURACRONPCGWorldPartitionIntegration::GetHardwareStreamingDistanceMultiplier() const
{
    // Retornar multiplicador de distância com base no perfil de qualidade atual
    float BaseMultiplier = HardwareConfig.DistanceMultiplier;
    
    // Aplicar ajuste dinâmico baseado no FPS se habilitado
    if (HardwareConfig.bEnableDynamicFPSAdjustment)
    {
        float FPSMultiplier = CalculateFPSBasedDistanceMultiplier();
        return BaseMultiplier * FPSMultiplier;
    }
    
    return BaseMultiplier;
}

bool AAURACRONPCGWorldPartitionIntegration::SetStreamingQualityProfile(EAURACRONStreamingQualityProfile NewProfile)
{
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::SetStreamingQualityProfile - Alterando perfil para %d"), 
           static_cast<int32>(NewProfile));
    
    // Atualizar perfil de qualidade
    HardwareConfig.QualityProfile = NewProfile;
    
    // Configurar multiplicador de distância baseado no perfil
    switch (NewProfile)
    {
        case EAURACRONStreamingQualityProfile::Low:
            HardwareConfig.DistanceMultiplier = 0.5f;
            break;
        case EAURACRONStreamingQualityProfile::Medium:
            HardwareConfig.DistanceMultiplier = 1.0f;
            break;
        case EAURACRONStreamingQualityProfile::High:
            HardwareConfig.DistanceMultiplier = 1.5f;
            break;
        case EAURACRONStreamingQualityProfile::Ultra:
            HardwareConfig.DistanceMultiplier = 2.0f;
            break;
        default:
            HardwareConfig.DistanceMultiplier = 1.0f;
            break;
    }
    
    // Aplicar otimizações
    ApplyHardwareSpecificOptimizations();
    
    return true;
}

bool AAURACRONPCGWorldPartitionIntegration::OptimizeStreamingForCurrentPlatform()
{
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::OptimizeStreamingForCurrentPlatform - Otimizando para plataforma atual"));
    
    // Detectar capacidades de hardware
    if (!DetectHardwareCapabilitiesInternal())
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGWorldPartitionIntegration::OptimizeStreamingForCurrentPlatform - Falha ao detectar capacidades de hardware"));
        return false;
    }
    
    // Aplicar otimizações específicas da plataforma
    if (bUsePlatformSpecificOptimizations)
    {
        ApplyPlatformSpecificOptimizations();
    }
    
    // Aplicar otimizações gerais
    ApplyHardwareSpecificOptimizations();
    
    return true;
}

bool AAURACRONPCGWorldPartitionIntegration::ShouldAdjustOptimizations() const
{
    // Verificar se é necessário ajustar otimizações baseado em vários fatores
    
    // Verificar se o tempo desde o último ajuste é suficiente
    if (TimeSinceLastOptimizationAdjustment < MinTimeBetweenOptimizationAdjustments)
    {
        return false;
    }
    
    // Verificar se o FPS está fora dos limites aceitáveis
    float CurrentFPS = PerformanceStats.AverageFrameTime > 0.0f ? 1.0f / PerformanceStats.AverageFrameTime : 60.0f;
    if (CurrentFPS < HardwareConfig.MinAcceptableFPS || CurrentFPS > HardwareConfig.TargetFPS * 1.5f)
    {
        return true;
    }
    
    // Verificar se houve hitches recentes
    if (PerformanceStats.HitchesDetected > 0)
    {
        return true;
    }
    
    // Verificar uso de memória
    if (PerformanceStats.CurrentMemoryUsageMB > HardwareConfig.MemoryBudgetMB * 0.9f)
    {
        return true;
    }
    
    // Por padrão, não ajustar se tudo estiver dentro dos limites aceitáveis
    return false;
}

void AAURACRONPCGWorldPartitionIntegration::OptimizeMemoryUsage()
{
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::OptimizeMemoryUsage - Otimizando uso de memória"));
    
    // Verificar se o uso de memória está acima do orçamento
    if (PerformanceStats.CurrentMemoryUsageMB <= HardwareConfig.MemoryBudgetMB)
    {
        return;
    }
    
    // Calcular quantos elementos precisamos descarregar para ficar dentro do orçamento
    float ExcessMemoryMB = PerformanceStats.CurrentMemoryUsageMB - (HardwareConfig.MemoryBudgetMB * 0.8f); // Alvo: 80% do orçamento
    int32 ElementsToUnload = FMath::CeilToInt(ExcessMemoryMB / 10.0f); // Estimativa: 10MB por elemento
    
    UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGWorldPartitionIntegration::OptimizeMemoryUsage - Uso de memória excedido. Descarregando %d elementos"), ElementsToUnload);
    
    // Ordenar elementos por prioridade e distância
    TArray<TPair<FGuid, FAURACRONPCGStreamingEntry>> ElementsArray;
    for (const auto& ElementPair : RegisteredElements)
    {
        if (ElementPair.Value.bIsCurrentlyStreamed)
        {
            ElementsArray.Add(ElementPair);
        }
    }
    
    // Ordenar por prioridade (menor primeiro) e depois por última atualização (mais antiga primeiro)
    ElementsArray.Sort([](const TPair<FGuid, FAURACRONPCGStreamingEntry>& A, const TPair<FGuid, FAURACRONPCGStreamingEntry>& B) {
        // Primeiro por prioridade (menor primeiro)
        if (A.Value.StreamingConfig.StreamingPriority != B.Value.StreamingConfig.StreamingPriority)
        {
            return A.Value.StreamingConfig.StreamingPriority < B.Value.StreamingConfig.StreamingPriority;
        }
        
        // Depois por tempo desde a última atualização (mais antigo primeiro)
        return A.Value.LastUpdateTime < B.Value.LastUpdateTime;
    });
    
    // Descarregar elementos de baixa prioridade
    int32 ElementsUnloaded = 0;
    for (int32 i = 0; i < ElementsArray.Num() && ElementsUnloaded < ElementsToUnload; ++i)
    {
        auto& ElementPair = ElementsArray[i];
        if (ElementPair.Value.bIsCurrentlyStreamed)
        {
            // Não descarregar elementos de alta prioridade
            if (ElementPair.Value.StreamingConfig.StreamingPriority > 75)
            {
                continue;
            }
            
            // Descarregar elemento
            StreamOutElement(RegisteredElements[ElementPair.Key]);
            ElementsUnloaded++;
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::OptimizeMemoryUsage - Descarregados %d elementos para otimizar memória"), ElementsUnloaded);
}

int32 AAURACRONPCGWorldPartitionIntegration::CalculateOptimalLODLevel() const
{
    // Calcular nível de LOD ótimo baseado no hardware e desempenho atual
    
    // Base: perfil de qualidade
    int32 BaseLOD = 0;
    switch (HardwareConfig.QualityProfile)
    {
        case EAURACRONStreamingQualityProfile::Ultra:
            BaseLOD = 0;
            break;
        case EAURACRONStreamingQualityProfile::High:
            BaseLOD = 1;
            break;
        case EAURACRONStreamingQualityProfile::Medium:
            BaseLOD = 2;
            break;
        case EAURACRONStreamingQualityProfile::Low:
            BaseLOD = 3;
            break;
        default:
            BaseLOD = 2;
            break;
    }
    
    // Ajustar baseado no FPS atual
    float CurrentFPS = PerformanceStats.AverageFrameTime > 0.0f ? 1.0f / PerformanceStats.AverageFrameTime : 60.0f;
    if (CurrentFPS < HardwareConfig.MinAcceptableFPS)
    {
        // FPS muito baixo, aumentar LOD (reduzir qualidade)
        BaseLOD = FMath::Min(BaseLOD + 2, 3);
    }
    else if (CurrentFPS < HardwareConfig.TargetFPS)
    {
        // FPS abaixo do alvo, aumentar LOD levemente
        BaseLOD = FMath::Min(BaseLOD + 1, 3);
    }
    
    // Ajustar baseado no uso de memória
    if (PerformanceStats.CurrentMemoryUsageMB > HardwareConfig.MemoryBudgetMB * 0.9f)
    {
        // Uso de memória alto, aumentar LOD
        BaseLOD = FMath::Min(BaseLOD + 1, 3);
    }
    
    return BaseLOD;
}

void AAURACRONPCGWorldPartitionIntegration::ApplyStreamingConfigToElement(FAURACRONPCGStreamingEntry& Element)
{
    if (!Element.PCGElement || !IsValid(Element.PCGElement))
    {
        return;
    }
    
    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGWorldPartitionIntegration::ApplyStreamingConfigToElement - Aplicando configurações a %s"), 
           *Element.PCGElement->GetName());
    
    // Aplicar configurações de LOD baseadas no perfil de qualidade atual
    int32 TargetLOD = CalculateOptimalLODLevel();
    
    // Aplicar LOD aos componentes do ator
    TArray<UStaticMeshComponent*> MeshComponents;
    Element.PCGElement->GetComponents<UStaticMeshComponent>(MeshComponents);
    
    for (UStaticMeshComponent* MeshComponent : MeshComponents)
    {
        if (MeshComponent)
        {
            // Forçar LOD específico se disponível
            if (MeshComponent->GetStaticMesh() && MeshComponent->GetStaticMesh()->GetNumLODs() > TargetLOD)
            {
                MeshComponent->ForcedLodModel = TargetLOD + 1; // ForcedLodModel é 1-based
            }
            
            // Ajustar distâncias de LOD baseado no perfil de qualidade
            if (HardwareConfig.LODDistanceFactors.Num() > 0)
            {
                float LODDistanceFactor = HardwareConfig.LODDistanceFactors[FMath::Min(TargetLOD, HardwareConfig.LODDistanceFactors.Num() - 1)];
                MeshComponent->SetLODDistanceScale(LODDistanceFactor);
            }
        }
    }
    
    // Aplicar configurações específicas baseadas no tipo de elemento
    if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(Element.PCGElement))
    {
        // Configurar densidade de elementos baseado no perfil de qualidade
        float DensityScale = 1.0f;
        switch (HardwareConfig.QualityProfile)
        {
            case EAURACRONStreamingQualityProfile::Ultra:
                DensityScale = 1.0f;
                break;
            case EAURACRONStreamingQualityProfile::High:
                DensityScale = 0.8f;
                break;
            case EAURACRONStreamingQualityProfile::Medium:
                DensityScale = 0.6f;
                break;
            case EAURACRONStreamingQualityProfile::Low:
                DensityScale = 0.4f;
                break;
        }
        
        Environment->SetDensityScale(DensityScale);
    }
    else if (AAURACRONPCGPrismalFlow* PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(Element.PCGElement))
    {
        // Configurar qualidade de efeitos baseado no perfil de qualidade
        float EffectQuality = 1.0f;
        switch (HardwareConfig.QualityProfile)
        {
            case EAURACRONStreamingQualityProfile::Ultra:
                EffectQuality = 1.0f;
                break;
            case EAURACRONStreamingQualityProfile::High:
                EffectQuality = 0.75f;
                break;
            case EAURACRONStreamingQualityProfile::Medium:
                EffectQuality = 0.5f;
                break;
            case EAURACRONStreamingQualityProfile::Low:
                EffectQuality = 0.25f;
                break;
        }
        
        PrismalFlow->SetEffectQuality(EffectQuality);
    }
}

bool AAURACRONPCGWorldPartitionIntegration::ApplyOptimizationToAllElements()
{
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::ApplyOptimizationToAllElements - Aplicando otimizações a todos os elementos"));
    
    int32 ElementCount = 0;
    
    // Aplicar otimizações a todos os elementos registrados
    for (auto& ElementPair : RegisteredElements)
    {
        if (ElementPair.Value.PCGElement && IsValid(ElementPair.Value.PCGElement))
        {
            // Aplicar configurações de streaming e LOD baseadas no perfil de qualidade
            ApplyStreamingConfigToElement(ElementPair.Value);
            ElementCount++;
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGWorldPartitionIntegration::ApplyOptimizationToAllElements - Otimizações aplicadas a %d elementos"), ElementCount);
    
    return ElementCount > 0;
}

FAURACRONStreamingPerformanceStats AAURACRONPCGWorldPartitionIntegration::GetStreamingPerformanceStats() const
{
    return PerformanceStats;
}
