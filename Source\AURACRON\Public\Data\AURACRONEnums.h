// AURACRONEnums.h
// Sistema de Sígilos AURACRON - Enumerações Centrais UE 5.6
// Definições robustas e completas para todos os enums do sistema

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "AURACRONEnums.generated.h"

// Forward declarations para evitar dependências circulares
enum class EAURACRONIslandType : uint8;

/**
 * Fases do mapa durante a partida - Sistema de Alternância de Mapas
 * Baseado na documentação: FASE 1: DESPERTAR (0-15 min), FASE 2: CONVERGÊNCIA (15-25 min), etc.
 */
UENUM(BlueprintType)
enum class EAURACRONMapPhase : uint8
{
    Awakening       UMETA(DisplayName = "Despertar"),           // 0-15 minutos
    Expansion       UMETA(DisplayName = "Expansão"),            // 15-20 minutos (fase intermediária)
    Convergence     UMETA(DisplayName = "Convergência"),        // 15-25 minutos
    Intensification UMETA(DisplayName = "Intensificação"),      // 25-35 minutos
    Resolution      UMETA(DisplayName = "Resolução")            // 35+ minutos
};

/**
 * Tipos de ambiente no sistema de alternância de mapas
 * Baseado na documentação: Planície Radiante, Firmamento Zephyr, Reino Purgatório
 */
UENUM(BlueprintType)
enum class EAURACRONEnvironmentType : uint8
{
    RadiantPlains   UMETA(DisplayName = "Planície Radiante"),   // Mapa base terrestre
    ZephyrFirmament UMETA(DisplayName = "Firmamento Zephyr"),   // Plataformas celestiais
    PurgatoryRealm  UMETA(DisplayName = "Reino Purgatório")     // Dimensão espelhada
};

/**
 * Tipos de objetivos procedurais
 * Sistema de geração dinâmica baseado no estado da partida
 */
UENUM(BlueprintType)
enum class EAURACRONObjectiveType : uint8
{
    None                UMETA(DisplayName = "Nenhum"),                   // Valor padrão/inválido
    FragmentAuracron    UMETA(DisplayName = "Fragmento Auracron"),      // Mini-objetivos que constroem buff maior
    NexusFragment       UMETA(DisplayName = "Fragmento de Nexus"),      // Fragmentos de nexus para construir buffs
    TemporalRift        UMETA(DisplayName = "Fenda Temporal"),          // Permite rewind de 10 segundos
    EnvironmentAnchor   UMETA(DisplayName = "Âncora de Ambiente"),      // Controla ambiente ativo
    MapAnchor           UMETA(DisplayName = "Âncora de Mapa"),          // Controla qual mapa está ativo
    FusionCatalyst      UMETA(DisplayName = "Catalisador de Fusão"),    // Reduz cooldown de Sígilos
    TransitionPortal    UMETA(DisplayName = "Portal de Transição"),     // Ativa transições entre ambientes
    PrismalGuardian     UMETA(DisplayName = "Guardião Prismal"),        // Objetivo principal terrestre
    PrismalNexus        UMETA(DisplayName = "Nexus Prismal"),           // Nexus de energia prismática
    StormCore           UMETA(DisplayName = "Núcleo de Tempestade"),    // Objetivo principal celestial
    SpectralGuardian    UMETA(DisplayName = "Guardião Espectral"),      // Objetivo principal espectral
    UmbraticLeviathan   UMETA(DisplayName = "Leviatã Umbrático"),       // Objetivo especial do Reino Purgatório
    RadiantShrine       UMETA(DisplayName = "Santuário Radiante"),      // Santuário de energia radiante
    WindSanctuary       UMETA(DisplayName = "Santuário do Vento"),      // Santuário de energia eólica
    PurgatoryShrine     UMETA(DisplayName = "Santuário do Purgatório"), // Santuário de energia sombria
    ChaosRift           UMETA(DisplayName = "Fenda do Caos"),           // Fenda de energia caótica
    RadiantAnchor       UMETA(DisplayName = "Âncora Radiante"),         // Âncora de energia radiante
    ZephyrAnchor        UMETA(DisplayName = "Âncora Zéfiro"),           // Âncora de energia eólica
    PurgatoryAnchor     UMETA(DisplayName = "Âncora do Purgatório"),    // Âncora de energia sombria
    NexusIsland         UMETA(DisplayName = "Ilha Nexus"),              // Ilha com nexus de energia
    SanctuaryIsland     UMETA(DisplayName = "Ilha Santuário"),          // Ilha com santuário
    ArsenalIsland       UMETA(DisplayName = "Ilha Arsenal"),            // Ilha com arsenal
    ChaosIsland         UMETA(DisplayName = "Ilha do Caos")             // Ilha com energia caótica
};

/**
 * Tipos de buff aplicados por objetivos
 * Sistema robusto de buffs para equipes e áreas
 */
UENUM(BlueprintType)
enum class EAURACRONBuffType : uint8
{
    MovementSpeed       UMETA(DisplayName = "Velocidade de Movimento"),
    DamageBoost         UMETA(DisplayName = "Aumento de Dano"),
    DefenseBoost        UMETA(DisplayName = "Aumento de Defesa"),
    CooldownReduction   UMETA(DisplayName = "Redução de Recarga"),
    HealthRegeneration  UMETA(DisplayName = "Regeneração de Vida"),
    ManaRegeneration    UMETA(DisplayName = "Regeneração de Mana"),
    CriticalChance      UMETA(DisplayName = "Chance Crítica"),
    AttackSpeed         UMETA(DisplayName = "Velocidade de Ataque"),
    SpellPower          UMETA(DisplayName = "Poder Mágico"),
    Armor               UMETA(DisplayName = "Armadura"),
    MagicResistance     UMETA(DisplayName = "Resistência Mágica"),
    Lifesteal           UMETA(DisplayName = "Roubo de Vida"),
    Tenacity            UMETA(DisplayName = "Tenacidade")
};

/**
 * Tipos de efeitos temporais
 * Sistema de manipulação temporal para Fendas Temporais
 */
UENUM(BlueprintType)
enum class EAURACRONTemporalEffectType : uint8
{
    None                UMETA(DisplayName = "Nenhum"),                  // Sem efeito temporal
    Rewind              UMETA(DisplayName = "Retrocesso"),               // Volta posição/vida 10 segundos
    Slow                UMETA(DisplayName = "Lentidão"),                // Reduz velocidade temporal
    Accelerate          UMETA(DisplayName = "Aceleração"),              // Aumenta velocidade temporal
    Freeze              UMETA(DisplayName = "Congelamento"),            // Congela no tempo
    Loop                UMETA(DisplayName = "Loop"),                    // Cria loop temporal
    TimeAcceleration    UMETA(DisplayName = "Aceleração Temporal"),     // Acelera cooldowns
    TimeDeceleration    UMETA(DisplayName = "Desaceleração Temporal"),  // Desacelera inimigos
    ChronoShield        UMETA(DisplayName = "Escudo Temporal"),         // Imunidade temporal breve
    TemporalEcho        UMETA(DisplayName = "Eco Temporal")             // Duplica próxima habilidade
};

/**
 * Tipos de trilhos dinâmicos
 * Sistema de trilhos que mudam baseado em condições
 */
UENUM(BlueprintType)
enum class EAURACRONTrailType : uint8
{
    None                UMETA(DisplayName = "Nenhum"),                  // Valor padrão/inválido
    Solar               UMETA(DisplayName = "Trilho Solar"),            // Energia dourada, boost velocidade
    Axis                UMETA(DisplayName = "Trilho Axis"),             // Canais neutros, transição instantânea
    Lunar               UMETA(DisplayName = "Trilho Lunar"),            // Caminhos etéreos, furtividade noturna
    PrismalFlow         UMETA(DisplayName = "Fluxo Prismal"),           // Trilho do fluxo prismal
    EtherealPath        UMETA(DisplayName = "Caminho Etéreo"),          // Trilho etéreo
    NexusConnection     UMETA(DisplayName = "Conexão Nexus")            // Trilho de conexão com nexus
};

/**
 * Estados de qualidade de hardware para otimização adaptativa
 * Sistema de detecção automática de performance
 */
UENUM(BlueprintType)
enum class EAURACRONHardwareQuality : uint8
{
    Entry               UMETA(DisplayName = "Entry Level"),             // 2-3GB RAM, GPU básica
    MidRange            UMETA(DisplayName = "Mid Range"),               // 3-4GB RAM, GPU intermediária  
    HighEnd             UMETA(DisplayName = "High End")                 // 4GB+ RAM, GPU avançada
};

/**
 * Tipos de sígilos do sistema de fusão
 * Sistema de Sígilos Auracron (Fusion 2.0)
 */
UENUM(BlueprintType)
enum class EAURACRONSigilType : uint8
{
    Aegis               UMETA(DisplayName = "Aegis"),                   // Tanque - +15% HP, Armadura adaptativa
    Ruin                UMETA(DisplayName = "Ruin"),                    // Dano - +12% ATK/AP adaptativo
    Vesper              UMETA(DisplayName = "Vesper")                   // Utilidade - +10% Vel. Move + 8% Recarga
};

/**
 * Estados de replicação de rede
 * Sistema robusto para multiplayer
 */
UENUM(BlueprintType)
enum class EAURACRONNetworkState : uint8
{
    Disconnected        UMETA(DisplayName = "Desconectado"),
    Connecting          UMETA(DisplayName = "Conectando"),
    Connected           UMETA(DisplayName = "Conectado"),
    Synchronizing       UMETA(DisplayName = "Sincronizando"),
    Ready               UMETA(DisplayName = "Pronto"),
    InGame              UMETA(DisplayName = "Em Jogo"),
    Reconnecting        UMETA(DisplayName = "Reconectando"),
    Error               UMETA(DisplayName = "Erro")
};

/**
 * Categoria de objetivos procedurais
 * Sistema de classificação para balanceamento
 */
UENUM(BlueprintType)
enum class EAURACRONObjectiveCategory : uint8
{
    Core                UMETA(DisplayName = "Principal"),               // Objetivos principais
    CatchUp             UMETA(DisplayName = "Recuperação"),             // Objetivos de catch-up
    Bonus               UMETA(DisplayName = "Bônus"),                   // Objetivos extras
    Event               UMETA(DisplayName = "Evento"),                  // Objetivos de eventos especiais
    Environment         UMETA(DisplayName = "Ambiente")                // Objetivos ambientais
};

/**
 * Estados de objetivos procedurais
 * Sistema de controle de estado dos objetivos
 */
UENUM(BlueprintType)
enum class EAURACRONObjectiveState : uint8
{
    Inactive            UMETA(DisplayName = "Inativo"),                 // Objetivo não ativo
    Active              UMETA(DisplayName = "Ativo"),                   // Objetivo ativo e disponível
    Available           UMETA(DisplayName = "Disponível"),              // Objetivo disponível para captura
    InProgress          UMETA(DisplayName = "Em Progresso"),            // Objetivo sendo capturado
    InCombat            UMETA(DisplayName = "Em Combate"),              // Objetivo em combate
    Captured            UMETA(DisplayName = "Capturado"),               // Objetivo capturado por uma equipe
    Completed           UMETA(DisplayName = "Completado"),              // Objetivo completado
    Respawning          UMETA(DisplayName = "Renascendo"),              // Objetivo renascendo
    Expired             UMETA(DisplayName = "Expirado")                 // Objetivo expirou
};

/**
 * Tipos de energia para efeitos especiais
 * Sistema de classificação de energia
 */
UENUM(BlueprintType)
enum class EAURACRONEnergyType : uint8
{
    Golden              UMETA(DisplayName = "Energia Dourada"),         // Energia dos Portais Radiantes
    Silver              UMETA(DisplayName = "Energia Prateada"),        // Energia dos Portais Zephyr
    Violet              UMETA(DisplayName = "Energia Violeta"),         // Energia dos Portais Umbrais
    Solar               UMETA(DisplayName = "Solar"),                   // Energia solar dourada
    Lunar               UMETA(DisplayName = "Lunar"),                   // Energia lunar prateada
    Prismal             UMETA(DisplayName = "Prismal"),                 // Energia prismática multicolor
    Chaos               UMETA(DisplayName = "Caos"),                    // Energia caótica vermelha
    Void                UMETA(DisplayName = "Vazio")                    // Energia do vazio roxa
};

// ========================================
// DEFINIÇÕES DE RANGE PARA ENUMS (UE 5.6)
// ========================================

// Definir range para EAURACRONEnvironmentType
ENUM_RANGE_BY_FIRST_AND_LAST(EAURACRONEnvironmentType, EAURACRONEnvironmentType::RadiantPlains, EAURACRONEnvironmentType::PurgatoryRealm)

// Definir range para EAURACRONMapPhase
ENUM_RANGE_BY_FIRST_AND_LAST(EAURACRONMapPhase, EAURACRONMapPhase::Awakening, EAURACRONMapPhase::Resolution)

// Definir range para EAURACRONObjectiveType
ENUM_RANGE_BY_FIRST_AND_LAST(EAURACRONObjectiveType, EAURACRONObjectiveType::PrismalNexus, EAURACRONObjectiveType::TemporalRift)




