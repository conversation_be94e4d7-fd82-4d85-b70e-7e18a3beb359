// AURACRONStructs.h
// Sistema de Sígilos AURACRON - Estruturas de Dados UE 5.6
// Definições robustas e completas para todas as structs do sistema

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "GameFramework/Actor.h"
#include "Data/AURACRONEnums.h"
#include "AURACRONStructs.generated.h"

/**
 * Segmento do Fluxo Prismal - O núcleo serpentino do mapa
 * Representa uma seção do rio de energia que serpenteia pelos ambientes
 */
USTRUCT(BlueprintType)
struct AURACRON_API FPrismalFlowSegment
{
    GENERATED_BODY()

    /** Posição mundial do segmento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fluxo Prismal")
    FVector WorldPosition;

    /** Direção do fluxo de energia */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fluxo Prismal")
    FVector FlowDirection;

    /** Largura do segmento (20-50 unidades) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fluxo Prismal", meta = (ClampMin = "20.0", ClampMax = "50.0"))
    float Width;

    /** Velocidade do fluxo - afeta movimento e habilidades */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fluxo Prismal")
    float FlowSpeed;

    /** Intensidade da energia prismática */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fluxo Prismal")
    float EnergyIntensity;

    /** Cor atual baseada na equipe controladora */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fluxo Prismal")
    FLinearColor CurrentColor;

    /** Equipe que controla este segmento (-1 = neutro, 0 = equipe A, 1 = equipe B) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fluxo Prismal")
    int32 ControllingTeam;

    /** Se este segmento está em estado calmo (para estratégias) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fluxo Prismal")
    bool bIsCalmFlow;

    /** Tempo restante de controle de equipe */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fluxo Prismal")
    float ControlTimeRemaining;

    /** Construtor padrão */
    FPrismalFlowSegment()
    {
        WorldPosition = FVector::ZeroVector;
        FlowDirection = FVector::ForwardVector;
        Width = 35.0f;
        FlowSpeed = 1.0f;
        EnergyIntensity = 1.0f;
        CurrentColor = FLinearColor::White;
        ControllingTeam = -1;
        bIsCalmFlow = false;
        ControlTimeRemaining = 0.0f;
    }
};

/**
 * Objetivo Procedural - Sistema de geração dinâmica de objetivos
 * Baseado no estado da partida para balanceamento automático
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONProceduralObjective
{
    GENERATED_BODY()

    /** Tipo do objetivo procedural */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    EAURACRONObjectiveType ObjectiveType;

    /** Posição mundial do objetivo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    FVector WorldPosition;

    /** Ambiente onde o objetivo está ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    EAURACRONEnvironmentType Environment;

    /** Tipo de ambiente (alias para compatibilidade) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    EAURACRONEnvironmentType EnvironmentType;

    /** Equipe que controla o objetivo (-1 = neutro) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    int32 ControllingTeam;

    /** Valor acumulado para objetivos que requerem progresso */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    float AccumulatedValue;

    /** Valor máximo necessário para completar o objetivo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    float MaxValue;

    /** Tempo de vida do objetivo (0 = permanente) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    float LifeTime;

    /** Tempo restante de vida */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    float RemainingLifeTime;

    /** Recompensa em ouro para a equipe */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    int32 GoldReward;

    /** Recompensa em experiência para a equipe */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    int32 ExperienceReward;

    /** Tipo de buff concedido ao completar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    EAURACRONBuffType BuffType;

    /** Magnitude do buff */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    float BuffMagnitude;

    /** Duração do buff em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    float BuffDuration;

    /** Se o objetivo está ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    bool bIsActive;

    /** Se o objetivo foi completado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    bool bIsCompleted;

    /** Prioridade do objetivo (maior = mais importante) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    int32 Priority;

    /** Valor estratégico do objetivo (0.0 - 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    float StrategicValue;

    /** Categoria do objetivo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    EAURACRONObjectiveCategory ObjectiveCategory;

    /** Estado atual do objetivo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    EAURACRONObjectiveState CurrentState;

    /** Tempo de respawn em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    float RespawnTime;

    /** Tempo que o objetivo está vivo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objetivo Procedural")
    float TimeAlive;

    /** Construtor padrão */
    FAURACRONProceduralObjective()
    {
        ObjectiveType = EAURACRONObjectiveType::FragmentAuracron;
        WorldPosition = FVector::ZeroVector;
        Environment = EAURACRONEnvironmentType::RadiantPlains;
        EnvironmentType = EAURACRONEnvironmentType::RadiantPlains;
        ControllingTeam = -1;
        AccumulatedValue = 0.0f;
        MaxValue = 100.0f;
        LifeTime = 0.0f;
        RemainingLifeTime = 0.0f;
        GoldReward = 100;
        ExperienceReward = 50;
        BuffType = EAURACRONBuffType::MovementSpeed;
        BuffMagnitude = 0.1f;
        BuffDuration = 30.0f;
        bIsActive = true;
        bIsCompleted = false;
        Priority = 1;
        StrategicValue = 0.5f;
        ObjectiveCategory = EAURACRONObjectiveCategory::Core;
        CurrentState = EAURACRONObjectiveState::Active;
        TimeAlive = 0.0f;
    }
};

/**
 * Configuração de geração de objetivos procedurais
 * Parâmetros para o sistema de balanceamento dinâmico
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONObjectiveGenerationConfig
{
    GENERATED_BODY()

    /** Número mínimo de objetivos ativos simultaneamente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geração", meta = (ClampMin = "1", ClampMax = "10"))
    int32 MinActiveObjectives;

    /** Número máximo de objetivos ativos simultaneamente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geração", meta = (ClampMin = "1", ClampMax = "20"))
    int32 MaxActiveObjectives;

    /** Intervalo mínimo entre spawns de objetivos (segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geração", meta = (ClampMin = "10.0", ClampMax = "300.0"))
    float MinSpawnInterval;

    /** Intervalo máximo entre spawns de objetivos (segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geração", meta = (ClampMin = "30.0", ClampMax = "600.0"))
    float MaxSpawnInterval;

    /** Multiplicador de spawn quando uma equipe está atrás */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geração", meta = (ClampMin = "1.0", ClampMax = "5.0"))
    float CatchupSpawnMultiplier;

    /** Diferença de ouro necessária para ativar catch-up */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geração", meta = (ClampMin = "500", ClampMax = "5000"))
    int32 CatchupGoldThreshold;

    /** Diferença de kills necessária para ativar catch-up */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geração", meta = (ClampMin = "3", ClampMax = "15"))
    int32 CatchupKillThreshold;

    /** Construtor padrão */
    FAURACRONObjectiveGenerationConfig()
    {
        MinActiveObjectives = 2;
        MaxActiveObjectives = 6;
        MinSpawnInterval = 30.0f;
        MaxSpawnInterval = 120.0f;
        CatchupSpawnMultiplier = 1.5f;
        CatchupGoldThreshold = 1500;
        CatchupKillThreshold = 5;
    }
};

/**
 * Configuração básica de streaming para sistemas gerais
 * Para World Partition específico, use FAURACRONPCGWorldPartitionStreamingConfig
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONBasicStreamingConfig
{
    GENERATED_BODY()

    /** Forma de streaming - usando FVector para definir escala da caixa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    FVector ShapeScale;

    /** Prioridade de streaming */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    int32 StreamingPriority;

    /** Distância de ativação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float ActivationDistance;

    /** Se deve usar streaming baseado em performance */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    bool bUsePerformanceBasedStreaming;

    /** Construtor padrão - configuração robusta para UE 5.6 */
    FAURACRONBasicStreamingConfig()
        : ShapeScale(FVector(1000.0f))
        , StreamingPriority(1)
        , ActivationDistance(2000.0f)
        , bUsePerformanceBasedStreaming(true)
    {
    }
};
