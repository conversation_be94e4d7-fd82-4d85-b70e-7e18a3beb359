// AURACRONPCGLaneSystem.h
// Sistema de Lanes e Jungle para AURACRON - UE 5.6
// Baseado em análise detalhada de League of Legends e Dota 2

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGEnvironment.h"
#include "PCG/AURACRONPCGPerformanceManager.h"
#include "Components/SplineComponent.h"
#include "Components/StaticMeshComponent.h"
#include "AURACRONPCGLaneSystem.generated.h"

// Forward declarations para evitar dependência circular
enum class EAURACRONJungleCampType : uint8;

/**
 * Tipos de lanes baseados exatamente no layout do Summoner's Rift (LoL)
 */
UENUM(BlueprintType)
enum class EAURACRONLaneType : uint8
{
    TopLane         UMETA(DisplayName = "Top Lane"),        // Diagonal superior esquerda para inferior direita
    MidLane         UMETA(DisplayName = "Mid Lane"),        // Diagonal inferior esquerda para superior direita (centro)
    BotLane         UMETA(DisplayName = "Bot Lane"),        // Diagonal inferior direita para superior esquerda
    PrismalFlow     UMETA(DisplayName = "Prismal Flow"),    // River equivalent - conecta objetivos principais
    River           UMETA(DisplayName = "River"),           // Alias para PrismalFlow (compatibilidade)
    Jungle          UMETA(DisplayName = "Jungle"),          // Áreas entre lanes
};



/**
 * Tipos de estruturas defensivas
 */
UENUM(BlueprintType)
enum class EAURACRONDefensiveStructure : uint8
{
    OuterTower      UMETA(DisplayName = "Outer Tower"),
    InnerTower      UMETA(DisplayName = "Inner Tower"),
    InhibitorTower  UMETA(DisplayName = "Inhibitor Tower"),
    Inhibitor       UMETA(DisplayName = "Inhibitor"),
    Nexus           UMETA(DisplayName = "Nexus"),
};

/**
 * Estrutura wrapper para array de vetores (resolve problema UHT com TMap<Enum, TArray<Type>>)
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONVectorArray
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FVector> Vectors;

    FAURACRONVectorArray()
    {
    }
};

/**
 * Estrutura wrapper para array de atores (resolve problema UHT com TMap<Enum, TArray<Type>>)
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONActorArray
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<AActor*> Actors;

    FAURACRONActorArray()
    {
    }
};

/**
 * Estrutura wrapper para array de componentes (resolve problema UHT com TMap<Enum, TArray<Type>>)
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONComponentArray
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<UActorComponent*> Components;

    FAURACRONComponentArray()
    {
    }
};



/**
 * Informações de uma lane adaptada para os 3 ambientes dinâmicos
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONLaneInfo
{
    GENERATED_BODY()

    /** Tipo da lane */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONLaneType LaneType;

    /** Pontos que definem o caminho da lane para cada ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TMap<EAURACRONEnvironmentType, FAURACRONVectorArray> LanePointsByEnvironment;

    /** Largura da lane em centímetros (baseada no LoL) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float LaneWidth;

    /** Posições das torres nesta lane para cada ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TMap<EAURACRONEnvironmentType, FAURACRONVectorArray> TowerPositionsByEnvironment;

    /** Posições dos minions spawn para cada ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TMap<EAURACRONEnvironmentType, FAURACRONVectorArray> MinionSpawnPointsByEnvironment;

    /** Se a lane está ativa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsActive;

    /** Ambiente atualmente ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONEnvironmentType CurrentEnvironment;

    /** Características específicas por ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TMap<EAURACRONEnvironmentType, FString> EnvironmentCharacteristics;

    FAURACRONLaneInfo()
        : LaneType(EAURACRONLaneType::MidLane)
        , LaneWidth(FAURACRONMapDimensions::LANE_WIDTH_CM)
        , bIsActive(true)
        , CurrentEnvironment(EAURACRONEnvironmentType::RadiantPlains)
    {
    }
};

/**
 * Informações de um camp da jungle
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONJungleCamp
{
    GENERATED_BODY()

    /** Posição do camp */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FVector Position;
    
    /** Raio do camp */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Radius;
    
    /** Tipo de monstros no camp */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString MonsterType;
    
    /** Tempo de respawn em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float RespawnTime;
    
    /** Se é um camp de buff */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsBuffCamp;
    
    /** Nível de dificuldade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 DifficultyLevel;
    
    FAURACRONJungleCamp()
        : Position(FVector::ZeroVector)
        , Radius(600.0f)
        , MonsterType(TEXT("Neutral"))
        , RespawnTime(60.0f)
        , bIsBuffCamp(false)
        , DifficultyLevel(1)
    {
    }
};

/**
 * Sistema de lanes e jungle para AURACRON
 * Implementa layout baseado em LoL/Dota 2 com adaptações para 3 ambientes
 */
UCLASS()
class AURACRON_API AAURACRONPCGLaneSystem : public AActor
{
    GENERATED_BODY()

public:
    AAURACRONPCGLaneSystem();

    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // ========================================
    // FUNÇÕES PÚBLICAS DE CONFIGURAÇÃO
    // ========================================
    
    /** Gerar sistema completo de lanes para todos os 3 ambientes */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|LaneSystem")
    void GenerateLaneSystem();

    /** Gerar lanes específicas para um ambiente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|LaneSystem")
    void GenerateLanesForEnvironment(EAURACRONEnvironmentType Environment);

    /** Transicionar para novo ambiente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|LaneSystem")
    void TransitionToEnvironment(EAURACRONEnvironmentType NewEnvironment, float TransitionDuration = 30.0f);

    /** Gerar jungle camps para ambiente específico */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|LaneSystem")
    void GenerateJungleCamps();

    /** Gerar estruturas defensivas para todos os ambientes */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|LaneSystem")
    void GenerateDefensiveStructures();

    /** Obter informações de uma lane específica */
    UFUNCTION(BlueprintPure, Category = "AURACRON|LaneSystem")
    FAURACRONLaneInfo GetLaneInfo(EAURACRONLaneType LaneType) const;

    /** Obter pontos da lane para ambiente específico */
    UFUNCTION(BlueprintPure, Category = "AURACRON|LaneSystem")
    TArray<FVector> GetLanePointsForEnvironment(EAURACRONLaneType LaneType, EAURACRONEnvironmentType Environment) const;

    /** Obter todos os camps da jungle */
    UFUNCTION(BlueprintPure, Category = "AURACRON|LaneSystem")
    TArray<FAURACRONJungleCamp> GetJungleCamps() const { return JungleCamps; }

    /** Verificar se posição está em uma lane (considerando ambiente atual) */
    UFUNCTION(BlueprintPure, Category = "AURACRON|LaneSystem")
    bool IsPositionInLane(const FVector& Position, EAURACRONLaneType LaneType, float Tolerance = 400.0f) const;

    /** Obter lane mais próxima de uma posição */
    UFUNCTION(BlueprintPure, Category = "AURACRON|LaneSystem")
    EAURACRONLaneType GetClosestLane(const FVector& Position) const;

    /** Atualizar para fase do mapa */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|LaneSystem")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);

    /** Obter ambiente atualmente ativo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|LaneSystem")
    EAURACRONEnvironmentType GetCurrentEnvironment() const { return CurrentEnvironment; }

protected:
    // ========================================
    // COMPONENTES
    // ========================================
    
    /** Splines das lanes */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|LaneSystem")
    TMap<EAURACRONLaneType, USplineComponent*> LaneSplines;
    
    /** Componentes de mesh para visualização */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|LaneSystem")
    TArray<UStaticMeshComponent*> LaneMeshComponents;

    // ========================================
    // CONFIGURAÇÕES
    // ========================================
    
    /** Informações das lanes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|LaneSystem")
    TMap<EAURACRONLaneType, FAURACRONLaneInfo> LaneInfos;
    
    /** Camps da jungle */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|LaneSystem")
    TArray<FAURACRONJungleCamp> JungleCamps;
    
    /** Se deve gerar automaticamente no BeginPlay */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|LaneSystem")
    bool bAutoGenerate;

private:
    // ========================================
    // ESTADO INTERNO
    // ========================================

    /** Ambiente atualmente ativo */
    UPROPERTY()
    EAURACRONEnvironmentType CurrentEnvironment;

    /** Fase atual do mapa */
    UPROPERTY()
    EAURACRONMapPhase CurrentMapPhase;

    /** Estruturas defensivas geradas por ambiente */
    UPROPERTY()
    TMap<EAURACRONEnvironmentType, FAURACRONActorArray> DefensiveStructuresByEnvironment;

    /** Componentes gerados dinamicamente por ambiente */
    UPROPERTY()
    TMap<EAURACRONEnvironmentType, FAURACRONComponentArray> GeneratedComponentsByEnvironment;

    /** Atores de interseção gerados dinamicamente por ambiente */
    UPROPERTY()
    TMap<EAURACRONEnvironmentType, FAURACRONActorArray> GeneratedIntersectionActorsByEnvironment;

    /** Timer para transição entre ambientes */
    UPROPERTY()
    FTimerHandle EnvironmentTransitionTimer;

    /** Duração da transição atual */
    UPROPERTY()
    float CurrentTransitionDuration;

    /** Ambiente de destino durante transição */
    UPROPERTY()
    EAURACRONEnvironmentType TargetEnvironment;

    // ========================================
    // FUNÇÕES INTERNAS
    // ========================================
    
    /** Inicializar informações das lanes para todos os ambientes */
    void InitializeLaneInfos();

    /** Gerar lane específica para ambiente específico */
    void GenerateLane(EAURACRONLaneType LaneType, EAURACRONEnvironmentType Environment);

    /** Gerar spline para lane em ambiente específico */
    USplineComponent* CreateLaneSpline(EAURACRONLaneType LaneType, EAURACRONEnvironmentType Environment, const TArray<FVector>& Points);

    /** Gerar mesh da lane para ambiente específico */
    void GenerateLaneMesh(EAURACRONLaneType LaneType, EAURACRONEnvironmentType Environment, USplineComponent* Spline);

    /** Calcular pontos da lane baseados no ambiente */
    TArray<FVector> CalculateLanePointsForEnvironment(EAURACRONLaneType LaneType, EAURACRONEnvironmentType Environment);

    /** Aplicar características específicas do ambiente */
    void ApplyEnvironmentCharacteristics(EAURACRONEnvironmentType Environment);

    /** Gerar camps específicos por região e ambiente */
    void GenerateJungleCampsForRegion(const FVector& RegionCenter, float RegionRadius, int32 NumCamps, EAURACRONEnvironmentType Environment);

    /** Gerar estrutura defensiva específica para ambiente */
    AActor* GenerateDefensiveStructure(EAURACRONDefensiveStructure StructureType, const FVector& Position, EAURACRONEnvironmentType Environment);

    /** Calcular posições das torres para uma lane em ambiente específico */
    TArray<FVector> CalculateTowerPositions(EAURACRONLaneType LaneType, EAURACRONEnvironmentType Environment);

    /** Aplicar efeitos da fase do mapa */
    void ApplyMapPhaseEffects();

    /** Limpar elementos gerados de um ambiente */
    void ClearGeneratedElementsForEnvironment(EAURACRONEnvironmentType Environment);

    /** Limpar todos os elementos gerados */
    void ClearAllGeneratedElements();

    /** Validar posição para camp da jungle */
    bool IsValidJungleCampPosition(const FVector& Position, float MinDistanceFromLanes = 1000.0f);

    /** Obter configurações específicas por tipo de camp e ambiente */
    FAURACRONJungleCamp GetJungleCampConfig(const FString& CampType, EAURACRONEnvironmentType Environment);

    /** Atualizar visibilidade baseada na fase */
    void UpdateVisibilityForPhase(EAURACRONMapPhase Phase);

    /** Executar transição suave entre ambientes */
    void ExecuteEnvironmentTransition();

    /** Finalizar transição de ambiente */
    void CompleteEnvironmentTransition();

    /** Obter altura baseada no ambiente */
    float GetEnvironmentHeight(EAURACRONEnvironmentType Environment) const;

    // ========================================
    // FUNÇÕES AUXILIARES PARA UE 5.6 - APIS MODERNAS
    // ========================================

    /** Aplicar modificações ambientais aos pontos */
    void ApplyEnvironmentModificationsToPoints(TArray<FVector>& Points, EAURACRONEnvironmentType EnvironmentType) const;

    /** Determinar tipo de camp baseado na posição */
    EAURACRONJungleCampType DetermineCampType(const FVector& Position, EAURACRONEnvironmentType EnvironmentType);

    /** Aplicar mesh e material ao camp */
    void ApplyCampMeshAndMaterial(UStaticMeshComponent* MeshComponent, EAURACRONJungleCampType CampType, EAURACRONEnvironmentType EnvironmentType);

    /** Aplicar mesh e material à estrutura */
    void ApplyStructureMeshAndMaterial(UStaticMeshComponent* MeshComponent, EAURACRONDefensiveStructure StructureType, EAURACRONEnvironmentType EnvironmentType);

    /** Aplicar efeitos ambientais à estrutura */
    void ApplyEnvironmentEffectsToStructure(AActor* StructureActor, EAURACRONEnvironmentType EnvironmentType);

    /** Obter offset de torre baseado no ambiente */
    FVector GetEnvironmentTowerOffset(EAURACRONEnvironmentType EnvironmentType);

    /** Aplicar efeitos de fase ao ator */
    void ApplyPhaseEffectsToActor(AActor* Actor, float PhaseIntensity, const FLinearColor& PhaseColor);

    /** Aplicar efeitos de fase ao componente */
    void ApplyPhaseEffectsToComponent(UActorComponent* Component, float PhaseIntensity, const FLinearColor& PhaseColor);

    /** Aplicar efeitos de fase ao componente de mesh (sobrecarga moderna UE 5.6) */
    void ApplyPhaseEffectsToComponent(UStaticMeshComponent* MeshComponent, float VisibilityMultiplier, const FLinearColor& PhaseColor);

    /** Aplicar efeitos especiais de fase para estruturas defensivas */
    void ApplyDefensiveStructurePhaseEffects(AActor* StructureActor, float VisibilityMultiplier, const FLinearColor& PhaseColor);

    /** Obter configuração do ambiente */
    FAURACRONEnvironmentConfig GetEnvironmentConfiguration(EAURACRONEnvironmentType EnvironmentType);

    // ========================================
    // FUNÇÕES DE INTERSECÇÃO DE TRILHOS BASEADA NA CAPACIDADE DE RENDERIZAÇÃO - FASE 3
    // ========================================

    /** Gerar intersecções de trilhos baseadas na capacidade de renderização do dispositivo */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|LaneSystem|Performance")
    void GenerateLaneIntersectionsBasedOnRenderingCapacity();

    /** Calcular pontos de intersecção baseados no tipo de dispositivo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|LaneSystem|Performance")
    TArray<FVector> CalculateLaneIntersectionPoints(EAURACRONDeviceType DeviceType, float QualityMultiplier);

    /** Calcular intersecções entre duas lanes específicas */
    void CalculateIntersectionsBetweenLanes(const TArray<FVector>& Lane1Points, const TArray<FVector>& Lane2Points, TArray<FVector>& OutIntersections, float Tolerance, int32 Density);

    /** Calcular intersecção entre duas linhas em 2D */
    bool CalculateLineIntersection2D(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, FVector& OutIntersection, float Tolerance);

    /** Gerar elementos visuais nas intersecções baseado na capacidade do dispositivo */
    void GenerateIntersectionElements(const TArray<FVector>& IntersectionPoints, EAURACRONDeviceType DeviceType, int32 MaxElements);

    /** Aplicar visuais de intersecção baseados no tipo de dispositivo */
    void ApplyIntersectionVisualsForDevice(UStaticMeshComponent* MeshComponent, EAURACRONDeviceType DeviceType);
};
